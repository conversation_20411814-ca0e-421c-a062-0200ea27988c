from django.shortcuts import render
from django.contrib.auth import authenticate, login as d_login
from django.contrib.auth.decorators import login_required, permission_required
from django.http import HttpResponseRedirect, HttpResponse
from django.shortcuts import render, redirect
from newsletter.models import Newsletter
from newsletter.task import newsletter_func

# Create your views here.
def admin_login(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(username=username, password=password)
        if user is not None and user.is_superuser:
            d_login(request, user)
            return redirect('dashboard')
        else:
            return HttpResponseRedirect(request.META.get('HTTP_REFERER'))
    return render(request, 'admin_login.html')


@login_required(login_url='index')
@permission_required('is_superuser')
def dashboard(request):
    subscribers_g = Newsletter.objects.filter(subscribe_status='SUBSCRIBE').count()
    subscribers_count = subscribers_g

    unsubscribers_g = Newsletter.objects.filter(subscribe_status='UN SUBSCRIBE').count()
    unsubscribers_count = unsubscribers_g

    sent_g = Newsletter.objects.filter(message_status='SENT').count()
    sent_count = sent_g

    pending_g = Newsletter.objects.filter(message_status='PENDING').count()
    pending_count = pending_g

    context = {
        'subscribers_count': subscribers_count,
        'unsubscribers_count': unsubscribers_count,
        'sent_count': sent_count,
        'pending_count': pending_count,
    }
    return render(request, 'dashboard.html', context)


def newsletter(request):
    subscribers = Newsletter.objects.filter(subscribe_status='SUBSCRIBE')
    subscribers_count = subscribers.count()
    unsubscribers = Newsletter.objects.filter(subscribe_status='UN SUBSCRIBE')
    unsubscribers_count = unsubscribers.count()
    sent = Newsletter.objects.filter(message_status='SENT')
    sent_count = sent.count()
    pending = Newsletter.objects.filter(message_status='PENDING')
    pending_count = pending.count()
    context = {
        'subscribers_count': subscribers_count,
        'unsubscribers_count': unsubscribers_count,
        'sent_count': sent_count,
        'pending_count': pending_count,
    }
    return render(request, 'newsletter.html', context)

def sent_email(request):
    newsletter_func.delay()
    return redirect('newsletter')


@login_required(login_url='admin.login')
@permission_required('is_superuser')
def logout(request):
    auth.logout(request)
    return redirect('admin_login')