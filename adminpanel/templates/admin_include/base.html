{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>EMAIL MARKETING</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="{% static '../../css.css?family=Source+Sans+Pro:300,400,400i,700&display=fallback' %}">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="{% static 'adminpanel/plugins/fontawesome-free/css/all.min.css' %}">
  <!-- Ionicons -->
  <link rel="stylesheet" href="{% static '../../ionicons/2.0.1/css/ionicons.min.css' %}">
  <!-- Tempusdominus Bootstrap 4 -->
  <link rel="stylesheet" href="{% static 'adminpanel/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css' %}">
  <!-- iCheck -->
  <link rel="stylesheet" href="{% static 'adminpanel/plugins/icheck-bootstrap/icheck-bootstrap.min.css' %}">
  <!-- JQVMap -->
  <link rel="stylesheet" href="{% static 'adminpanel/plugins/jqvmap/jqvmap.min.css' %}">
  <!-- Theme style -->
  <link rel="stylesheet" href="{% static 'adminpanel/dist/css/adminlte.min.css' %}">
  <!-- overlayScrollbars -->
  <link rel="stylesheet" href="{% static 'adminpanel/plugins/overlayScrollbars/css/OverlayScrollbars.min.css' %}">
  <!-- Daterange picker -->
  <link rel="stylesheet" href="{% static 'adminpanel/plugins/daterangepicker/daterangepicker.css' %}">
  <!-- summernote -->
  <link rel="stylesheet" href="{% static 'adminpanel/plugins/summernote/summernote-bs4.min.css' %}">
<link href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">



    <!--  brand_select-->
<link rel="stylesheet" href="{% static 'brand_select/fm.tagator.jquery.css' %}"/>
	<script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
  <!--  brand_select-->

<!--Tag Selectise-->
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
  <link href="/static/taggit_selectize/css/selectize.django.css" type="text/css" media="all" rel="stylesheet"/>
  <script type="text/javascript" src="/static/taggit_selectize/js/selectize.js"></script>
<!--Tag Selectise-->

<!--CK EDITER-->
<script type="text/javascript" src="{% static 'ckeditor/ckeditor-init.js' %}"></script>
<script type="text/javascript" src="{% static 'ckeditor/ckeditor/ckeditor.js' %}"></script>
<!--CK EDITER-->

</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <!-- Preloader -->
  <div class="preloader flex-column justify-content-center align-items-center">
    <img class="animation__shake" src="{% static 'adminpanel/dist/img/logo.png' %}" alt="FMCG LOGO" height="60" width="60">
  </div>

  <!-- Navbar -->
  <nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
      <li class="nav-item">
        <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
      </li>

    </ul>

    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">
      <!-- Navbar Search -->


      <!-- Messages Dropdown Menu -->

      <!-- Notifications Dropdown Menu -->

      <li class="nav-item dropdown">
        <a class="nav-link"  href="">
          <i class="far fa-bell"></i>
          <span class="badge badge-warning navbar-badge"></span>
        </a>
      <li class="nav-item">
        <a class="nav-link" data-widget="fullscreen" href="#" role="button">
          <i class="fas fa-expand-arrows-alt"></i>
        </a>
      </li>

    </ul>
  </nav>
  <!-- /.navbar -->

  <!-- Main Sidebar Container -->
  <aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a href="" class="brand-link">
      <span class="brand-text font-weight-light">EMAIL MARKETING</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">

      <nav class="mt-2">
        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
          <!-- Add icons to the links using the .nav-icon class
               with font-awesome or any other icon font library -->
          <li class="nav-item">
            <a href="{% url 'dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
              <i class="nav-icon fas fa-tachometer-alt"></i>
              <p>
                Dashboard
              </p>
            </a>
          </li>

          <li class="nav-item">
            <a href="{% url 'newsletter' %}" class="nav-link {% if request.resolver_match.url_name == 'newsletter' %}active{% endif %}">
                              <i class="nav-icon fas fa-user-secret"></i>

              <p>
                Newsletter
              </p>
            </a>
          </li>

<!--            <li class="nav-item">-->
<!--            <a href="" class="nav-link {% if request.resolver_match.url_name == 'edit_profile' %}active{% endif %}">-->
<!--                              <i class="nav-icon fas fa-user-secret"></i>-->

<!--              <p>-->
<!--                Send Email-->
<!--              </p>-->
<!--            </a>-->
<!--          </li>-->

<!--<li class="nav-item">-->
<!--            <a href="" class="nav-link {% if request.resolver_match.url_name == 'edit_profile' %}active{% endif %}">-->
<!--                  <i class="nav-icon fas fa-edit"></i>-->
<!--              <p>-->
<!--                Edit Profile-->
<!--              </p>-->
<!--            </a>-->
<!--          </li>-->

          <li class="nav-item">
            <a href="{% url 'logout' %}" class="nav-link">
              <i class="nav-icon fas fa-sign-out-alt"></i>
              <p>
                Logout
              </p>
            </a>
          </li>


        </ul>
      </nav>
      <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
  </aside>

    {% block content %}
    
    {% endblock %}


<!-- /.content-wrapper -->
  <footer class="main-footer">
    <strong>Copyright &copy; 2021 <a href="https://gulftimemedia.com/">Gulf Time Media LLC</a>.</strong>
    All rights reserved.

  </footer>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->
      {% for message in messages %}

<script>
     window.onload = function() {
     alert('{{message}}')
     }
</script>
                                {% endfor %}


<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>

<!-- jQuery -->
<script src="{% static 'adminpanel/plugins/jquery/jquery.min.js' %}"></script>
<!-- jQuery UI 1.11.4 -->
<script src="{% static 'adminpanel/plugins/jquery-ui/jquery-ui.min.js' %}"></script>
<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
<script>
  $.widget.bridge('uibutton', $.ui.button)
</script>
<!-- Bootstrap 4 -->
<script src="{% static 'adminpanel/plugins/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
<!-- ChartJS -->
<script src="{% static 'adminpanel/plugins/chart.js/Chart.min.js' %}"></script>
<!-- Sparkline -->
<script src="{% static 'adminpanel/plugins/sparklines/sparkline.js' %}"></script>
<!-- JQVMap -->
<script src="{% static 'adminpanel/plugins/jqvmap/jquery.vmap.min.js' %}"></script>
<script src="{% static 'adminpanel/plugins/jqvmap/maps/jquery.vmap.usa.js' %}"></script>
<!-- jQuery Knob Chart -->
<script src="{% static 'adminpanel/plugins/jquery-knob/jquery.knob.min.js' %}"></script>
<!-- daterangepicker -->
<script src="{% static 'adminpanel/plugins/moment/moment.min.js' %}"></script>
<script src="{% static 'adminpanel/plugins/daterangepicker/daterangepicker.js' %}"></script>
<!-- Tempusdominus Bootstrap 4 -->
<script src="{% static 'adminpanel/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js' %}"></script>
<!-- Summernote -->
<script src="{% static 'adminpanel/plugins/summernote/summernote-bs4.min.js' %}"></script>
<!-- overlayScrollbars -->
<script src="{% static 'adminpanel/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js' %}"></script>
<!-- AdminLTE App -->
<script src="{% static 'adminpanel/dist/js/adminlte.js' %}"></script>
<!-- AdminLTE for demo purposes -->
<script src="{% static 'adminpanel/dist/js/demo.js' %}"></script>
<!-- AdminLTE dashboard demo (This is only for demo purposes) -->
<script src="{% static 'dist/js/pages/dashboard.js' %}"></script>
<script src="{% static 'frondend/assets/scripts/tag_select.js' %}"></script>


<!--brand_select-->
	<script src="{% static 'brand_select/fm.tagator.jquery.js' %}"></script>
<!--/brand_select-->

</body>
</html>
