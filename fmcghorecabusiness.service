[Unit]
Description=Gunicorn instance to serve FMCG Horeca Business
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/fmcghorecabusiness.com
Environment="PYTHONPATH=/usr/lib/python3/dist-packages:/usr/local/lib/python3.12/dist-packages"
ExecStart=/usr/bin/python3 -m gunicorn --config gunicorn.conf.py fmcgbusiness.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
