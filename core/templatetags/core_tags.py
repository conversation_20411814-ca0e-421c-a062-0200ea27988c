
from django import template
from django.contrib import messages
from django.shortcuts import redirect

from blog.models import BlogCategory
from core.forms import ContactForm, AdvertiseWithUsForm

register = template.Library()


@register.inclusion_tag('template_tags/contact_form.html', takes_context=True)
def contact_tags(context):
    request = context['request']
    if request.method == 'POST':
        contactform = ContactForm(request.POST)
        if contactform.is_valid():
            contactform.save()
            messages.success(request, 'Contact Enquiry Successful', extra_tags='contact')
            return redirect('/')
    else:
        contactform = ContactForm()
    return {request: 'context.request', 'contactform': contactform}


@register.inclusion_tag('template_tags/advertise_form.html', takes_context=True)
def advertise_tags(context):
    request = context['request']
    if request.method == 'POST':
        advertiseform = AdvertiseWithUsForm(request.POST)
        if advertiseform.is_valid():
            advertiseform.save()
            messages.success(request, 'Advertise With Us Successful', extra_tags='advertise')
            return redirect('/')

    else:
        advertiseform = AdvertiseWithUsForm()
    return {request: 'context.request', 'advertiseform': advertiseform}