from django.contrib import admin
from import_export.admin import ImportExportMixin
from core.models import AboutUs, ContactUs, DataCollection
from magazine.models import Flipbook

class AboutUsAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('short_description',)

class DataCollectionAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('company_name', 'name', 'mobile', 'email', 'website')

class ContactUsAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'email', 'subject', 'message', 'created', 'updated')
    search_fields = ('name',)

admin.site.register(AboutUs, AboutUsAdmin)
admin.site.register(ContactUs, ContactUsAdmin)
admin.site.register(Flipbook)
admin.site.register(DataCollection)


