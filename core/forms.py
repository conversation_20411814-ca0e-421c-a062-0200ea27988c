from django import forms

from blog.models import AdvertiseWithUs
from core.models import ContactUs, DataCollection


class ContactForm(forms.ModelForm):
    class Meta:
        model = ContactUs
        fields = ('name', 'email', 'subject', 'message')

class AdvertiseWithUsForm(forms.ModelForm):
    class Meta:
        model = AdvertiseWithUs
        fields = ('company_name', 'contact_number', 'contact_person', 'email', 'website', 'message')


class DataCollectionForm(forms.ModelForm):
    class Meta:
        model = DataCollection
        fields = ('company_name', 'name', 'mobile', 'email', 'website', 'conformed')