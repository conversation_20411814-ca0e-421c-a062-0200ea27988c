from ckeditor.fields import <PERSON><PERSON>ext<PERSON>ield
from django.db import models

# Create your models here.
from django.template.defaultfilters import truncatechars


class AboutUs(models.Model):
    about = RichTextField()

    def __str__(self):
        return self.about

    @property
    def short_description(self):
        return truncatechars(self.about, 100)


class ContactUs(models.Model):
    name = models.CharField(max_length=1022)
    email = models.CharField(max_length=255)
    subject = models.Char<PERSON><PERSON>(max_length=1022)
    message = models.TextField()
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class DataCollection(models.Model):
    company_name = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    name = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    mobile = models.CharField(max_length=255, null=True, blank=True)
    email = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    website = models.CharField(max_length=255, null=True, blank=True)
    conformed = models.BooleanField(null=True, blank=True)

    def __str__(self):
        return self.name
