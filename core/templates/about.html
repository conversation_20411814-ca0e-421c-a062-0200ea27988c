{% extends 'include/base.html' %}
{% load static %}

{% block head %}

<title>Best FMCG & HORECA Business Listing Magazine in Dubai, UAE</title>
<meta name="description" content="Explore FMCG & HORECA Business in UAE. We provide best magazine for hotels, restaurants, catering, delivering top-notch news, articles, & events coverage.">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="Best FMCG & HORECA Business Listing Magazine in Dubai, UAE">
<meta property="og:description" content="Explore FMCG & HORECA Business in UAE. We provide best magazine for hotels, restaurants, catering, delivering top-notch news, articles, & events coverage.">
<meta property="og:image" content="https://fmcghorecabusiness.com/static/assets/images/favicon.png">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="Best FMCG & HORECA Business Listing Magazine in Dubai, UAE">
<meta name="twitter:description" content="Explore FMCG & HORECA Business in UAE. We provide best magazine for hotels, restaurants, catering, delivering top-notch news, articles, & events coverage.">
<meta property="og:image" content="https://fmcghorecabusiness.com/static/assets/images/favicon.png">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}

{% block content %}

<section class="my-breadcrumb"  style="background-image: url('/static/assets/images/banner/about-us.jpg');">
         <div class="container page-banner">
            <div class="row">
               <div class="col-sm-12 col-md-12 col-xs-12">
                  <h1>ABOUT US</h1>
               </div>
            </div>
         </div>
      </section>

      <section class="main-content">
         <div class="container">
            <div class="row">
               <div class="col-md-12 col-sm-12 col-xs-12">
                  <div class="about-us">
                     <div class="post-entry">
                        {% for about in about %}
                        {{ about.about|safe }}
                        {% endfor %}
                     </div>

                  </div>
               </div>
            </div>
         </div>
      </section>

{% endblock %}