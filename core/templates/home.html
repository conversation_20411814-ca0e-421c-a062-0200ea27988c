{% extends 'include/base.html' %}
{% load static %}

{% block head %}

<title>Leading UAE's Best Magazine for FMCG & HORECA Industry | Dubai</title>
<meta name="description" content="Leading FMCG & HORECA magazine in the UAE! Covering the best hotels, restaurants, catering & etc. Stay ahead with Dubai's business, news, articles & events">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="Leading UAE's Best Magazine for FMCG & HORECA Industry | Dubai">
<meta property="og:description" content="Leading FMCG & HORECA magazine in the UAE! Covering the best hotels, restaurants, catering & etc. Stay ahead with Dubai's business, news, articles & events">
<meta property="og:image" content="https://fmcghorecabusiness.com/static/assets/images/favicon.png">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="Leading UAE's Best Magazine for FMCG & HORECA Industry | Dubai">
<meta name="twitter:description" content="Leading FMCG & HORECA magazine in the UAE! Covering the best hotels, restaurants, catering & etc. Stay ahead with Dubai's business, news, articles & events">
<meta property="og:image" content="https://fmcghorecabusiness.com/static/assets/images/favicon.png">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}


{% block content %}
      <section class="zerogrid-section">
         <div class="container-fluid">
            <div class="row">
               <div class="col-md-12 col-xs-12 col-sm-12">
                  <div class="zerogrid">
                     <div class="row">

                        <div class="col-2-4">
                           <div class="wrap-col">
                              <div class="featured-news-slider">
                                 <div class="zerogrid-slider owl-carousel owl-theme">
                                    {% for fmcg_insights_banner in fmcg_insights_banner %}
                                    <div class="item">
                                 <div class="post-content">
                                    <div class="catname">
                                       <a href="{% url 'category' fmcg_insights_banner.category.slug %}" class="btn" style="background-color:{{ fmcg_insights_banner.category.color }}">
                                          <div>{{ fmcg_insights_banner.category }}</div>
                                       </a>
                                    </div>
                                    <h5> <a href="{% url 'blog_details' fmcg_insights_banner.slug %}">{{ fmcg_insights_banner.name|truncatechars:100 }}</a> </h5>
                                 <ul class="post-tools">
                                    <li>{{ fmcg_insights_banner.created }} </li>
                                   </ul>
                                 </div>
                                 <div class="post-thumb" style="height: 412px;">
                                    <img alt="{{ fmcg_insights_banner.name }}" title="{{ fmcg_insights_banner.name }}" src="{{ fmcg_insights_banner.image.url }}" style="height:100%; object-fit:cover;"> </div>
                              </div>
                                                                   {% endfor %}
                           </div>
                        </div>
                           </div>
                        </div>



                        <div class="col-1-4">
                           <div class="wrap-col">
                              <div class="featured-news-slider">
                                 <div class="zerogrid-slider owl-carousel owl-theme">
                                 {% for equipment_banner in equipment_banner %}
                              <div class="item">
                                 <div class="post-content">
                                    <div class="catname">
                                       <a href="{% url 'category' equipment_banner.category.slug %}" class="btn" style="background-color:{{ equipment_banner.category.color }}">
                                          <div>{{ equipment_banner.category }}</div>
                                       </a>
                                    </div>
                                    <h5> <a href="{% url 'blog_details' equipment_banner.slug %}">{{ equipment_banner.name|truncatechars:70 }}</a> </h5>
                                  <ul class="post-tools">
                                    <li>{{ equipment_banner.created }} </li>
                                   </ul>
                                 </div>
                                 <div class="post-thumb banner-middle-img">
                                    <img alt="{{ equipment_banner.name }}" title="{{ equipment_banner.name }}" src="{{ equipment_banner.image.url }}"> </div>
                              </div>
                               {% endfor %}
                           </div>
                              </div>
                           </div>


                                      <div class="wrap-col">
                              <div class="featured-news-slider">
                                 <div class="zerogrid-slider owl-carousel owl-theme">
                                 {% for hotels_banner in hotels_banner %}
                              <div class="item">
                                 <div class="post-content">
                                    <div class="catname">
                                       <a class="btn" href="{% url 'category' hotels_banner.category.slug %}" style="background-color:{{ hotels_banner.category.color }}">
                                          <div>{{ hotels_banner.category }}</div>
                                       </a>
                                    </div>
                                    <h5> <a href="{% url 'blog_details' hotels_banner.slug %}">{{ hotels_banner.name|truncatechars:70 }}</a> </h5>
                                   <ul class="post-tools">
                                    <li>{{ hotels_banner.created }} </li>
                                   </ul>
                                 </div>
                                 <div class="post-thumb banner-middle-img">
                                    <img alt="{{ hotels_banner.name }}" title="{{ hotels_banner.name }}" src="{{ hotels_banner.image.url }}"> </div>
                              </div>
                                    {% endfor %}
                                 </div>
                              </div>
                           </div>

                        </div>

                        <div class="col-1-4">

                           <div class="wrap-col">
                               <div class="featured-news-slider">
                                 <div class="zerogrid-slider owl-carousel owl-theme">
                                 {% for catering_banner in catering_banner %}
                              <div class="item">
                                 <div class="post-content">
                                    <div class="catname">
                                       <a class="btn" href="{% url 'category' catering_banner.category.slug %}" style="background-color:{{ catering_banner.category.color }}">
                                          <div>{{ catering_banner.category }}</div>
                                       </a>
                                    </div>
                                    <h5> <a href="{% url 'blog_details' catering_banner.slug %}">{{ catering_banner.name|truncatechars:70 }}</a> </h5>
                                   <ul class="post-tools">
                                    <li>{{ catering_banner.created }} </li>
                                   </ul>
                                 </div>
                                 <div class="post-thumb banner-middle-img">
                                    <img alt="{{ catering_banner.name }}" title="{{ catering_banner.name }}" src="{{ catering_banner.image.url }}"> </div>
                              </div>
                                    {% endfor %}
                           </div>
                               </div>
                           </div>

                               <div class="wrap-col">
                               <div class="featured-news-slider">
                                 <div class="zerogrid-slider owl-carousel owl-theme">
                                 {% for restaurant_banner in restaurant_banner %}
                              <div class="item">
                                 <div class="post-content">
                                    <div class="catname">
                                       <a href="{% url 'category' restaurant_banner.category.slug %}" class="btn" style="background-color:{{ restaurant_banner.category.color }}">
                                          <div>{{ restaurant_banner.category }}</div>
                                       </a>
                                    </div>
                                    <h5> <a href="{% url 'blog_details' restaurant_banner.slug %}">{{ restaurant_banner.name|truncatechars:70 }}</a> </h5>
                                  <ul class="post-tools">
                                    <li>{{ restaurant_banner.created }} </li>
                                   </ul>
                                 </div>
                                 <div class="post-thumb banner-middle-img">
                                    <img alt="{{ restaurant_banner.name }}" title="{{ restaurant_banner.name }}" src="{{ restaurant_banner.image.url }}"> </div>
                              </div>
                                    {% endfor %}
                           </div>
                        </div>
                           </div>



                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </section>

<section class="news-ticker">
         <div class="container">
            <div class="row">
               <div class="col-md-12 col-xs-12 col-sm-12">
                  <div class="news-ticker-block">
                     <div class="breakingNews bn-red" id="bn4">
                        <div class="bn-title" style="width: auto;">
                           <h2 style="display: inline-block;">Latest </h2>
                           <span></span>
                        </div>
                        <ul style="left: 191px;">
                            {% for blog_top in blog_top %}
                           <li style="display: block; top: -60px;"><a href="{% url 'blog_details' blog_top.slug %}"> {{ blog_top.name|truncatechars:150 }}</a></li>
                        {% endfor %}
                        </ul>
                        <div class="bn-navi">
                           <span></span>
                           <span></span>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </section>


      <section class="main-content">
         <div class="container">
            <div class="row">
               <div class="col-md-8 col-sm-12 col-xs-12">

                  <div class="section" id="fmcginsights">
                     <div class="col-md-12 col-xs-12 col-sm-12 nopadding">
                        <div class="heading" style="margin-bottom: 13px;">
                           <h2 class="main-heading">FMCG INSIGHTS </h2>
                           <span class="heading-read-more">
                              {% for fmcg_insights_blog in fmcg_insights_blog|slice:1 %}
                           <a href="{% url 'category' fmcg_insights_blog.category.slug %}" class="btn" style="background-color:{{ fmcg_insights_blog.category.color }}; color: white;">READ MORE</a>
                              {% endfor %}
                           </span>
                        </div>
                     </div>
                     <div class="row">
                        {% for fmcg_insights_blog in fmcg_insights_blog %}
                        <article class="col-md-4 col-sm-6 col-xs-12">
                           <div class="latest-news-grid grid-1">
                              <div class="picture">
                                 <div class="category-image">
                                    <img alt="{{ fmcg_insights_blog.name }}" title="{{ fmcg_insights_blog.name }}" class="img-responsive section-blog-image" src="{{ fmcg_insights_blog.image.url }}">
                                    <div class="catname">
                                       <a href="{% url 'category' fmcg_insights_blog.category.slug %}" class="btn" style="background-color:{{ fmcg_insights_blog.category.color }}">
                                          <div>{{ fmcg_insights_blog.category }}</div>
                                       </a>
                                    </div>
                                 </div>
                              </div>
                              <div class="detail" style="height: 68px">
                                 <div class="caption">
                                    <h5>
                                       <a href="{% url 'blog_details' fmcg_insights_blog.slug %}">{{ fmcg_insights_blog.name|truncatechars:56 }}</a>
                                    </h5>
<!--                                    <p>-->
<!--                                       {{ fmcg_insights_blog.descriptions|safe|truncatechars:40 }}-->
<!--                                    </p>-->
                                 </div>

                              </div>
                           </div>
                        </article>
                           {% endfor %}
                     </div>

                         <div class="related-posts">
                        <div class="row">
                           {% for ads_section in ads_section %}
                           {% if ads_section.area == 'Grid Ads' %}
                           <article class="col-md-6 col-sm-4 col-xs-12">
                              <div class="grid-1">
                                 <div class="picture">
                                    <div class="category-image">
                                      {% if ads_section.link %}
                                       <a href="{{ ads_section.link }}" target="_blank">
                                          {% endif %}
                                       <img src="{{ ads_section.image.url }}" title="{{ ads_section.name }}" class="img-responsive">
                                       </a>
                                    </div>
                                 </div>
                              </div>
                           </article>
                           {% endif %}
                           {% endfor %}
                        </div>
                     </div>

                               <div class="section" style="margin-bottom: -23px;">
                     <div class="col-md-12 col-xs-12 col-sm-12 nopadding">
                        <div class="heading" style="margin-bottom: 10px;">
                           <h2 class="main-heading">HORECA INSIGHTS</h2>
                           <span class="heading-read-more">
                           <a href="#equipment" class="btn" style="background-color:#7648db; color:white;">EQUIPMENT & SUPPLIES</a>
                            <a href="#catering" class="btn" style="background-color:#810c0c; color:white;">CATERING</a>
                            <a href="#hotels" class="btn" style="background-color:#8fab00; color:white;">HOTELS</a>
                               <a href="#restaurant" class="btn" style="background-color:#004559; color:white;">RESTAURANTS & REVIEWS</a>
                           </span>
                        </div>
                     </div>

                                                <div class="row" id="equipment">
                          {% for equipment_blog in equipment_blog %}
                                 {% if forloop.counter < 3 %}
                        <article class="col-md-6 col-sm-6 col-xs-12">
                           <div class="latest-news-grid grid-1">
                              <div class="picture">
                                 <div class="category-image">
                                    <img alt="{{ equipment_blog.name }}" title="{{ equipment_blog.name }}" class="img-responsive section-blog-image" src="{{ equipment_blog.image.url }}">
                                    <div class="catname">
                                       <a href="{% url 'category' equipment_blog.category.slug %}" class="btn" style="background-color:{{ equipment_blog.category.color }}">
                                          <div>{{ equipment_blog.category }}</div>
                                       </a>
                                    </div>
                                 </div>
                              </div>
                              <div class="detail"  style="height: 69px;">
                                 <div class="caption">
                                    <h5>
                                       <a href="{% url 'blog_details' equipment_blog.slug %}">{{ equipment_blog.name|truncatechars:80 }}</a>
                                    </h5>
                                 </div>
<!--                                   <ul class="post-tools">-->
<!--                                    <li>{{ equipment_blog.created }} </li>-->
<!--                                   </ul>-->
                              </div>
                           </div>

                        </article>
                     {% else %}
                      <article class="col-md-6 col-sm-6 col-xs-12">
                           <div class="latest-news-grid grid-1">
                               <ul class="tabs-posts">
                              <li>
                                 <div class="pic">
                                    <img alt="{{ equipment_blog.name }}" title="{{ equipment_blog.name }}" class="img-responsive" src="{{ equipment_blog.image.url }}" style="width:75px; height:75px; object-fit:cover;"> </div>
                                 <div class="caption">
                                    <a href="{% url 'blog_details' equipment_blog.slug %}">{{ equipment_blog.name|truncatechars:100 }}
                                    </a>
                                 </div>
<!--                                  <ul class="post-tools">-->
<!--                                    <li>{{ equipment_blog.created }} </li>-->
<!--                                   </ul>-->
                              </li>
                           </ul>
                           </div>
                        </article>
                                          {% endif %}

                           {% endfor %}
                     </div>
                                            <div class="ad-div text-center">
                        {% for ads_section in ads_section %}
                           {% if ads_section.area == 'Width Ads 2' %}

                              {% if ads_section.link %}
                     <a href="{{ ads_section.link }}" target="_blank">
                        {% endif %}
                     <img src="{{ ads_section.image.url }}" title="{{ ads_section.name }}" class="img-responsive">
                     </a>
                        {% endif %}
                        {% endfor %}
                  </div>
                  </div>

                          <div class="section" id="catering">
                     <div class="col-md-12 col-xs-12 col-sm-12 nopadding">
                        <div class="heading" style="margin-bottom: 6px;">
                           <h2 class="main-heading">CATERING</h2>
                             <span class="heading-read-more">
                          {% for catering_blog in catering_blog|slice:1 %}
                           <a href="{% url 'category' catering_blog.category.slug %}" class="btn" style="background-color:{{ catering_blog.category.color }}; color: white;">READ MORE</a>
                              {% endfor %}
                           </span>
                        </div>
                     </div>

                     <div class="row">
                          {% for catering_blog in catering_blog %}
                                 {% if forloop.counter < 3 %}
                        <article class="col-md-6 col-sm-6 col-xs-12">
                           <div class="latest-news-grid grid-1">
                              <div class="picture">
                                 <div class="category-image">
                                    <img alt="{{ catering_blog.name }}" title="{{ catering_blog.name }}" class="img-responsive section-blog-image" src="{{ catering_blog.image.url }}">
                                    <div class="catname">
                                       <a href="{% url 'category' catering_blog.category.slug %}" class="btn" style="background-color:{{ catering_blog.category.color }}">
                                          <div>{{ catering_blog.category }}</div>
                                       </a>
                                    </div>
                                 </div>
                              </div>
                              <div class="detail"  style="height: 68px;">
                                 <div class="caption">
                                    <h5>
                                       <a href="{% url 'blog_details' catering_blog.slug %}">{{ catering_blog.name|truncatechars:100 }}</a>
                                    </h5>
                                 </div>
<!--                                   <ul class="post-tools">-->
<!--                                    <li>{{ catering_blog.created }} </li>-->
<!--                                   </ul>-->
                              </div>
                           </div>
                        </article>
                     {% else %}
                      <article class="col-md-6 col-sm-6 col-xs-12">
                           <div class="latest-news-grid grid-1">
                               <ul class="tabs-posts">
                              <li>
                                 <div class="pic">
                                    <img alt="{{ catering_blog.name }}" title="{{ catering_blog.name }}" class="img-responsive" src="{{ catering_blog.image.url }}" style="width:75px; height:75px; object-fit:cover;"> </div>
                                 <div class="caption">
                                    <a href="{% url 'blog_details' catering_blog.slug %}">{{ catering_blog.name|truncatechars:100 }}
                                    </a>
                                 </div>
<!--                                  <ul class="post-tools">-->
<!--                                    <li>{{ catering_blog.created }} </li>-->
<!--                                   </ul>-->
                              </li>
                           </ul>
                           </div>
                        </article>
                                          {% endif %}

                           {% endfor %}
                     </div>
                  </div>
                  </div>


                     <div class="ad-div text-center" style="margin-bottom: 16px;">
                        {% for ads_section in ads_section %}
                           {% if ads_section.area == 'Width Ads 1' %}

                        {% if ads_section.link %}
                     <a href="{{ ads_section.link }}" target="_blank">
                        {% endif %}
                     <img src="{{ ads_section.image.url }}" title="{{ ads_section.name }}" class="img-responsive">
                     </a>
                        {% endif %}
                        {% endfor %}
                  </div>
                               <div class="section" style="margin-bottom: 5px;">
                         <div class="col-md-12 col-xs-12 col-sm-12 nopadding">
                        <div class="heading" style="margin-bottom: 10px;">
                           <h2 class="main-heading">HOTELS</h2>
                             <span class="heading-read-more">
                          {% for hotels_blog in hotels_blog|slice:1 %}
                           <a href="{% url 'category' hotels_blog.category.slug %}" class="btn" style="background-color:{{ hotels_blog.category.color }}; color: white;">READ MORE</a>
                              {% endfor %}
                           </span>
                        </div>
                     </div>
                          <div class="row" id="hotels">
                          {% for hotels_blog in hotels_blog %}
                                 {% if forloop.counter < 3 %}
                        <article class="col-md-6 col-sm-6 col-xs-12">
                           <div class="latest-news-grid grid-1">
                              <div class="picture">
                                 <div class="category-image">
                                    <img alt="{{ hotels_blog.name }}" title="{{ hotels_blog.name }}" class="img-responsive section-blog-image" src="{{ hotels_blog.image.url }}">
                                    <div class="catname">
                                       <a href="{% url 'category' hotels_blog.category.slug %}" class="btn" style="background-color:{{ hotels_blog.category.color }}">
                                          <div>{{ hotels_blog.category }}</div>
                                       </a>
                                    </div>
                                 </div>
                              </div>
                              <div class="detail"  style="height: 68px;">
                                 <div class="caption">
                                    <h5>
                                       <a href="{% url 'blog_details' hotels_blog.slug %}">{{ hotels_blog.name|truncatechars:70 }}</a>
                                    </h5>
                                 </div>
<!--                                   <ul class="post-tools">-->
<!--                                    <li>{{ hotels_blog.created }} </li>-->
<!--                                   </ul>-->
                              </div>
                           </div>
                        </article>
                     {% else %}
                      <article class="col-md-6 col-sm-6 col-xs-12">
                           <div class="latest-news-grid grid-1">
                              <ul class="tabs-posts">
                              <li>
                                 <div class="pic">
                                    <img alt="{{ hotels_blog.name }}" title="{{ hotels_blog.name }}" class="img-responsive" src="{{ hotels_blog.image.url }}" style="width:75px; height:75px; object-fit:cover;"> </div>
                                 <div class="caption">
                                    <a href="{% url 'blog_details' hotels_blog.slug %}">{{ hotels_blog.name|truncatechars:100 }}
                                    </a>
                                 </div>
<!--                                  <ul class="post-tools">-->
<!--                                    <li>{{ hotels_blog.created }} </li>-->
<!--                                   </ul>-->
                              </li>
                           </ul>
                           </div>
                        </article>
                                          {% endif %}

                           {% endfor %}
                     </div>
                               </div>

                  <div class="section" id="restaurant">
                     <div class="col-md-12 col-xs-12 col-sm-12 nopadding">
                        <div class="heading" style="margin-bottom: 15px;">
                           <h2 class="main-heading">RESTAURANT & REVIEWS</h2>
                        </div>
                     </div>
                     <div class="col-md-12 col-xs-12 col-sm-12 nopadding">
                        <div class="row">
                           <div class="content-slider-2  owl-carousel owl-theme">
                              {% for restaurant_blog in restaurant_blog %}
                              <div class="item">
                                 <article class="col-md-12 col-sm-12 col-xs-12">
                                    <div class="latest-news-grid grid-1">
                                       <div class="picture">
                                          <div class="category-image">
                                             <img alt="{{ restaurant_blog.name }}" title="{{ restaurant_blog.name }}" class="img-responsive" src="{{ restaurant_blog.image.url }}" style="height:252.53px; object-fit:cover;">
                                             <div class="catname">
                                                <a class="btn" href="{% url 'category' restaurant_blog.category.slug %}" style="background-color:{{ restaurant_blog.category.color }}">
                                                   <div>{{ restaurant_blog.category }}</div>
                                                </a>
                                             </div>
                                          </div>
                                       </div>
                                       <div class="detail">
                                          <div class="caption">
                                             <h5>
                                                <a href="{% url 'blog_details' restaurant_blog.slug %}">{{ restaurant_blog.name|truncatechars:90 }}</a>
                                             </h5>
                                          </div>

                                       </div>
                                    </div>
                                 </article>
                              </div>
                               {% endfor %}
                           </div>
                        </div>
                     </div>
                  </div>

                  <!--Ad Div -->

                  </div>


               <div class="col-md-4 col-sm-12 col-xs-12" id="side-bar">
                  <div class="theiaStickySidebar">


                         {% for ads_section in ads_section %}
                            {% if ads_section.area == 'MAGAZINE' %}
                    <div class="widget widget-bg">
                           <div class="ad-div style-box">
                             {% if ads_section.link %}
                              <a href="{{ ads_section.link }}" target="_blank">
                                 {% endif %}
                              <img src="{{ ads_section.image.url }}" title="{{ ads_section.name }}"  class="img-responsive">
                              </a>
                           </div>
                        </div>
                     {% endif %}
                              {% endfor %}

                      
                     <aside class="sidebar-color ">


                        <div class="widget widget-bg">
                           <div class="heading">
                              <h2 class="main-heading">EVENTS</h2>
                           </div>
                           <ul class="tabs-posts">
                              {% for events_blog in events_blog %}
                              <li>
                                 <div class="pic">
                                    <img alt="{{ events_blog.event.name }}" title="{{ events_blog.event.name }}" class="img-responsive" src="{{ events_blog.event.image.url }}" style="width:75px; height:75px; object-fit:cover;"> </div>
                                 <div class="caption">
                                    <a href="{% url 'blog_details' events_blog.event.slug %}">{{ events_blog.event.name|truncatechars:70 }}
                                    </a>
                                 </div>
<!--                                 <ul class="post-tools">-->
<!--                                    <li>{{ events_blog.created }} </li>-->
<!--                                 <li><i class="ti-user"></i>FMCG Horeca Business</li>-->
<!--                                   </ul>-->
                              </li>
                              {% endfor %}
                           </ul>
                        </div>
                     </aside>
                     <br>



                                 {% include "subscribe.html" %}


                 {% for ads_section in ads_section %}
                            {% if ads_section.area == 'Right Side Ads' %}
                    <div class="widget widget-bg">
                           <div class="ad-div style-box">
                             {% if ads_section.link %}
                              <a href="{{ ads_section.link }}" target="_blank">
                                 {% endif %}
                              <img src="{{ ads_section.image.url }}" title="{{ ads_section.name }}"  class="img-responsive">
                              </a>
                           </div>
                        </div>
                     {% endif %}
                              {% endfor %}
                  </div>
               </div>
               <!-- Content Sidebar End -->
            </div>
            <!-- Row End -->
         </div>

         <!-- Container End -->
      </section>



      <section class="parallel-post-style">
         <div class="container">
            <div class="row">
                <div class="section">
                     <div class="col-md-12 col-xs-12 col-sm-12 nopadding">
                        <div class="heading">
                           <h2 class="main-heading">NEWS</h2>
                           <span class="heading-read-more">
                              <a href="#fmcginsights" class="btn" style="background-color:#ff5722; color:white;">FMCG INSIGHTS</a>
                               <a href="#equipment" class="btn" style="background-color:#7648db; color:white;">EQUIPMENT & SUPPLIES</a>
                            <a href="#catering" class="btn" style="background-color:#810c0c; color:white;">CATERING</a>
                            <a href="#hotels" class="btn" style="background-color:#8fab00; color:white;">HOTELS</a>
                               <a href="#restaurant" class="btn" style="background-color:#004559; color:white;">RESTAURANTS & REVIEWS</a>
                           </span>
                        </div>
                     </div>

               <div class="col-md-12 col-sm-12 col-xs-12">
                  <div class="row">
                     {% for news_blog in news_blog %}
                     <div class="col-md-4 col-sm-6 col-xs-12">
                        <div class="grid-box">
                           <ul>
                              <li class="col-md-5 col-sm-3 col-xs-12 nopadding">
                                 <div class="thumb">
                                    <img src="{{ news_blog.image.url }}" title="{{ news_blog.name }}" alt="{{ news_blog.name }}">
                                 </div>
                              </li>
                              <li class="col-md-7 col-sm-9 col-xs-12">
                                 <div class="desc post-content">
                                    <h5><a href="{% url 'blog_details' news_blog.slug %}">{{ news_blog.name|truncatechars:80 }}</a></h5>
                                 </div>
                              </li>
                           </ul>
                        </div>
                     </div>
                     {% endfor %}
                  </div>
               </div>
            </div>
         </div>
      </section>



{% endblock %}


 <script src="https://unpkg.com/htmx.org@1.6.0"></script>
    <script>
        document.body.addEventListener('htmx:configRequest', (e) => {
          e.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
          })
      </script>
