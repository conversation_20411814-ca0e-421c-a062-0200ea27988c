{% load static %}
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Subscribe</title>
  <link rel="stylesheet" type="text/css" href="{% static 'assets/css/enquiry_style.css' %}">
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-color: #fff;
    }

    .login-root {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    .formbg-inner {
      padding: 11px;
    }

    .field {
      padding-bottom: 12px;
    }

    @media only screen and (max-width: 600px) {
      .formbg-inner {
        padding: 16px;
      }

      .field {
        padding-bottom: 12px;
      }
    }
  </style>
</head>
<body>
 <div class="login-root">
    <div class="box-root flex-flex flex-direction--column" style="flex-grow: 1;">
      <!-- Your existing HTML content -->
      <div class="loginbackground box-background--white padding-top--64">
        <div class="loginbackground-gridContainer">
          <div class="box-root flex-flex" style="grid-area: top / start / 8 / end;">
            <div class="box-root" style="background-image: linear-gradient(white 0%, rgb(247, 250, 252) 33%); flex-grow: 1;">
            </div>
          </div>
          <div class="box-root flex-flex" style="grid-area: 4 / 2 / auto / 5;">
            <div class="box-root box-divider--light-all-2 animationLeftRight tans3s" style="flex-grow: 1;"></div>
          </div>
          <div class="box-root flex-flex" style="grid-area: 6 / start / auto / 2;">
            <div class="box-root box-background--blue800" style="flex-grow: 1;"></div>
          </div>
          <div class="box-root flex-flex" style="grid-area: 7 / start / auto / 4;">
            <div class="box-root box-background--blue animationLeftRight" style="flex-grow: 1;"></div>
          </div>
          <div class="box-root flex-flex" style="grid-area: 8 / 4 / auto / 6;">
            <div class="box-root box-background--gray100 animationLeftRight tans3s" style="flex-grow: 1;"></div>
          </div>
          <div class="box-root flex-flex" style="grid-area: 2 / 15 / auto / end;">
            <div class="box-root box-background--cyan200 animationRightLeft tans4s" style="flex-grow: 1;"></div>
          </div>
          <div class="box-root flex-flex" style="grid-area: 3 / 14 / auto / end;">
            <div class="box-root box-background--blue animationRightLeft" style="flex-grow: 1;"></div>
          </div>
          <div class="box-root flex-flex" style="grid-area: 4 / 17 / auto / 20;">
            <div class="box-root box-background--gray100 animationRightLeft tans4s" style="flex-grow: 1;"></div>
          </div>
          <div class="box-root flex-flex" style="grid-area: 5 / 14 / auto / 17;">
            <div class="box-root box-divider--light-all-2 animationRightLeft tans3s" style="flex-grow: 1;"></div>
          </div>
        </div>
      </div>
      <div class="box-root padding-top--24 flex-flex flex-direction--column" style="flex-grow: 1; z-index: 9;">
<!--        <div class="box-root padding-top&#45;&#45;48 padding-bottom&#45;&#45;24 flex-flex flex-justifyContent&#45;&#45;center">-->
<!--          <h1><a href="http://blog.stackfindover.com/" rel="dofollow">SUBSCRIBE</a></h1>-->
<!--        </div>-->

      <div class="formbg-outer">
        <div class="formbg">
          <div class="formbg-inner">
            <center><span class="padding-bottom--15">Enquiry</span></center>
            <form id="stripe-login" method="post">
              {% csrf_token %}


              <div class="field">
                  <label>Company Name</label>
                  <input type="text" name="company_name">
                </div>

              <div class="field">
                  <label>Name *</label>
                  <input type="text" name="name" required>
                </div>

              <div class="field">
                  <label>Email *</label>
                  <input type="email" name="email" required>
                </div>

              <div class="field">
                  <label>Mobile *</label>
                  <input type="tel" name="mobile" required>
                </div>

              <div class="field">
                  <label>Website</label>
                  <input type="text" name="website">
                </div>



              <div class="field field-checkbox flex-flex align-center">
                <label>
                  <input type="checkbox" name="conformed" required>
                  I agree to receive emails from FMCG & HORECA Business
                </label>
              </div>

              <div class="field">
                <input type="submit" name="submit" value="Continue">
              </div>

            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>