#!/bin/bash

# Script to setup Let's Encrypt SSL certificate for fmcghorecabusiness.com
# Run this script once DNS propagation is complete

DOMAIN="fmcghorecabusiness.com"
WWW_DOMAIN="www.fmcghorecabusiness.com"
EMAIL="<EMAIL>"

echo "Checking DNS propagation..."
CURRENT_IP=$(curl -4 -s ifconfig.me)
DOMAIN_IP=$(dig +short $DOMAIN)

echo "Current server IP: $CURRENT_IP"
echo "Domain resolves to: $DOMAIN_IP"

if [ "$CURRENT_IP" != "$DOMAIN_IP" ]; then
    echo "WARNING: DNS is not pointing to this server yet!"
    echo "Please update your DNS records to point to $CURRENT_IP"
    echo "Then run this script again."
    exit 1
fi

echo "DNS looks good! Proceeding with Let's Encrypt certificate..."

# Stop nginx temporarily
sudo systemctl stop nginx

# Get the certificate using standalone mode
sudo certbot certonly --standalone \
    -d $DOMAIN \
    -d $WWW_DOMAIN \
    --non-interactive \
    --agree-tos \
    --email $EMAIL

if [ $? -eq 0 ]; then
    echo "Certificate obtained successfully!"
    
    # Update nginx configuration to use Let's Encrypt certificates
    sudo sed -i 's|ssl_certificate /etc/ssl/certs/fmcghorecabusiness.com.crt;|ssl_certificate /etc/letsencrypt/live/fmcghorecabusiness.com/fullchain.pem;|' /etc/nginx/sites-available/fmcghorecabusiness.com
    sudo sed -i 's|ssl_certificate_key /etc/ssl/private/fmcghorecabusiness.com.key;|ssl_certificate_key /etc/letsencrypt/live/fmcghorecabusiness.com/privkey.pem;|' /etc/nginx/sites-available/fmcghorecabusiness.com
    
    # Test nginx configuration
    sudo nginx -t
    
    if [ $? -eq 0 ]; then
        # Start nginx
        sudo systemctl start nginx
        echo "SSL certificate installed successfully!"
        echo "Your site is now available at https://$DOMAIN"
        
        # Setup automatic renewal
        echo "Setting up automatic certificate renewal..."
        (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
        
    else
        echo "Nginx configuration test failed. Reverting changes..."
        sudo sed -i 's|ssl_certificate /etc/letsencrypt/live/fmcghorecabusiness.com/fullchain.pem;|ssl_certificate /etc/ssl/certs/fmcghorecabusiness.com.crt;|' /etc/nginx/sites-available/fmcghorecabusiness.com
        sudo sed -i 's|ssl_certificate_key /etc/letsencrypt/live/fmcghorecabusiness.com/privkey.pem;|ssl_certificate_key /etc/ssl/private/fmcghorecabusiness.com.key;|' /etc/nginx/sites-available/fmcghorecabusiness.com
        sudo systemctl start nginx
    fi
else
    echo "Failed to obtain certificate. Starting nginx with self-signed certificate..."
    sudo systemctl start nginx
fi
