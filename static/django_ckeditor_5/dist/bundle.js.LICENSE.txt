/* istanbul ignore else -- @preserve */

/* istanbul ignore else: This is always true because otherwise it would not register a reducer callback. -- @preserve */

/* istanbul ignore file -- @preserve */

/* istanbul ignore if -- @preserve */

/* istanbul ignore if: paranoid check -- @preserve */

/* istanbul ignore next -- @preserve */

/* istanbul ignore next: paranoid check -- @preserve */

/* istanbul ignore next: static function definition -- @preserve */

/**
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */

/**
 * @license Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-licensing-options
 */
