!function(e){const t=e["en-gb"]=e["en-gb"]||{};t.dictionary=Object.assign(t.dictionary||{},{"(may require <kbd>Fn</kbd>)":"","%0 of %1":"%0 of %1",Accept:"",Accessibility:"","Accessibility help":"","Advanced options":"","Align cell text to the bottom":"","Align cell text to the center":"","Align cell text to the left":"","Align cell text to the middle":"","Align cell text to the right":"","Align cell text to the top":"","Align center":"Align center","Align left":"Align left","Align right":"Align right","Align table to the left":"","Align table to the right":"",Alignment:"",All:"","Almost equal to":"",Angle:"","Approximately equal to":"",Aquamarine:"Aquamarine",Arrows:"","Asterisk operator":"","Austral sign":"",Back:"Back","back with leftwards arrow above":"",Background:"","Below, you can find a list of keyboard shortcuts that can be used in the editor.":"",Big:"Big","Bitcoin sign":"",Black:"Black","Block quote":"Block quote","Block styles":"",Blue:"Blue","Blue marker":"Blue marker",Bold:"Bold","Bold text":"",Border:"","Break text":"","Bulleted List":"Bulleted List","Bulleted list styles toolbar":"",Cancel:"Cancel","Cannot upload file:":"Cannot upload file:","Caption for image: %0":"","Caption for the image":"",Category:"","Cedi sign":"","Cell properties":"","Cent sign":"","Center table":"","Centered image":"Centred image","Change image text alternative":"Change image text alternative","Characters: %0":"Characters: %0","Choose heading":"Choose heading","Choose table type":"Choose table type",Circle:"",Clear:"","Click to edit block":"",Close:"","Close contextual balloons, dropdowns, and dialogs":"",Code:"Code","Code block":"","Colon sign":"",Color:"","Color picker":"",Column:"Column","Contains as member":"","Content editing keystrokes":"","Content table":"Content table","Copy selected content":"","Copyright sign":"","Create link":"","Cruzeiro sign":"",Currency:"","Currency sign":"",Custom:"","Custom image size":"",Dashed:"",Decimal:"","Decimal with leading zero":"","Decrease indent":"Decrease indent","Decrease list item indent":"",Default:"Default","Degree sign":"","Delete column":"Delete column","Delete row":"Delete row","Dim grey":"Dim grey",Dimensions:"",Disc:"","Displayed text":"Displayed text","Division sign":"","Document colors":"Document colours","Dollar sign":"","Dong sign":"",Dotted:"",Double:"","Double dagger":"","Double exclamation mark":"","Double low-9 quotation mark":"","Double question mark":"",Downloadable:"Downloadable","downwards arrow to bar":"","downwards dashed arrow":"","downwards double arrow":"","downwards simple arrow":"","Drachma sign":"","Drag to move":"","Dropdown menu":"","Dropdown toolbar":"","Edit block":"Edit block","Edit link":"Edit link","Edit source":"","Editor block content toolbar":"","Editor contextual toolbar":"","Editor dialog":"","Editor menu bar":"","Editor toolbar":"","Element of":"","Em dash":"","Empty set":"","Empty snippet content":"","En dash":"","end with leftwards arrow above":"","Enter image caption":"Enter image caption","Enter table caption":"","Entering %0 code snippet":"","Entering a to-do list":"","Entering code snippet":"","Error during image upload":"","Euro sign":"","Euro-currency sign":"","Exclamation question mark":"","Execute the currently focused button. Executing buttons that interact with the editor content moves the focus back to the content.":"",Find:"","Find and replace":"","Find in text…":"","Find in the document":"","Font Background Color":"Font Background Colour","Font Color":"Font Colour","Font Family":"Font Family","Font Size":"Font Size","For all":"","Fraction slash":"","French franc sign":"","From computer":"","Full size image":"Full size image","German penny sign":"","Greater-than or equal to":"","Greater-than sign":"",Green:"Green","Green marker":"Green marker","Green pen":"Green pen",Grey:"Grey",Groove:"","Guarani sign":"","Header column":"Header column","Header row":"Header row",Heading:"Heading","Heading 1":"Heading 1","Heading 2":"Heading 2","Heading 3":"Heading 3","Heading 4":"Heading 4","Heading 5":"Heading 5","Heading 6":"Heading 6",Height:"","Help Contents. To close this dialog press ESC.":"",HEX:"",Highlight:"Highlight","Horizontal ellipsis":"","Horizontal line":"","Horizontal text alignment toolbar":"","Hryvnia sign":"","HTML object":"","HTML snippet":"",Huge:"Huge","Identical to":"",Image:"","Image from computer":"","Image Resize":"Image Resize","Image resize list":"","Image toolbar":"","Image upload complete":"","Image via URL":"","image widget":"Image widget","In line":"","Increase indent":"Increase indent","Increase list item indent":"","Indian rupee sign":"",Infinity:"",Insert:"Insert","Insert a hard break (a new paragraph)":"","Insert a new paragraph directly after a widget":"","Insert a new paragraph directly before a widget":"","Insert a new table row (when in the last cell of a table)":"","Insert a soft break (a <code>&lt;br&gt;</code> element)":"","Insert code block":"","Insert column left":"Insert column left","Insert column right":"Insert column right","Insert HTML":"","Insert image":"Insert image","Insert image via URL":"","Insert media":"Insert media","Insert paragraph after block":"","Insert paragraph before block":"","Insert row above":"Insert row above","Insert row below":"Insert row below","Insert table":"Insert table","Insert table layout":"Insert table layout","Insert via URL":"",Inset:"",Integral:"",Intersection:"","Invalid start index value.":"","Inverted exclamation mark":"","Inverted question mark":"",Italic:"Italic","Italic text":"",Justify:"Justify","Justify cell text":"","Keystrokes that can be used in a list":"","Keystrokes that can be used in a table cell":"","Keystrokes that can be used when a widget is selected (for example: image, table, etc.)":"","Kip sign":"",Latin:"","Latin capital letter a with breve":"","Latin capital letter a with macron":"","Latin capital letter a with ogonek":"","Latin capital letter c with acute":"","Latin capital letter c with caron":"","Latin capital letter c with circumflex":"","Latin capital letter c with dot above":"","Latin capital letter d with caron":"","Latin capital letter d with stroke":"","Latin capital letter e with breve":"","Latin capital letter e with caron":"","Latin capital letter e with dot above":"","Latin capital letter e with macron":"","Latin capital letter e with ogonek":"","Latin capital letter eng":"","Latin capital letter g with breve":"","Latin capital letter g with cedilla":"","Latin capital letter g with circumflex":"","Latin capital letter g with dot above":"","Latin capital letter h with circumflex":"","Latin capital letter h with stroke":"","Latin capital letter i with breve":"","Latin capital letter i with dot above":"","Latin capital letter i with macron":"","Latin capital letter i with ogonek":"","Latin capital letter i with tilde":"","Latin capital letter j with circumflex":"","Latin capital letter k with cedilla":"","Latin capital letter l with acute":"","Latin capital letter l with caron":"","Latin capital letter l with cedilla":"","Latin capital letter l with middle dot":"","Latin capital letter l with stroke":"","Latin capital letter n with acute":"","Latin capital letter n with caron":"","Latin capital letter n with cedilla":"","Latin capital letter o with breve":"","Latin capital letter o with double acute":"","Latin capital letter o with macron":"","Latin capital letter r with acute":"","Latin capital letter r with caron":"","Latin capital letter r with cedilla":"","Latin capital letter s with acute":"","Latin capital letter s with caron":"","Latin capital letter s with cedilla":"","Latin capital letter s with circumflex":"","Latin capital letter t with caron":"","Latin capital letter t with cedilla":"","Latin capital letter t with stroke":"","Latin capital letter u with breve":"","Latin capital letter u with double acute":"","Latin capital letter u with macron":"","Latin capital letter u with ogonek":"","Latin capital letter u with ring above":"","Latin capital letter u with tilde":"","Latin capital letter w with circumflex":"","Latin capital letter y with circumflex":"","Latin capital letter y with diaeresis":"","Latin capital letter z with acute":"","Latin capital letter z with caron":"","Latin capital letter z with dot above":"","Latin capital ligature ij":"","Latin capital ligature oe":"","Latin small letter a with breve":"","Latin small letter a with macron":"","Latin small letter a with ogonek":"","Latin small letter c with acute":"","Latin small letter c with caron":"","Latin small letter c with circumflex":"","Latin small letter c with dot above":"","Latin small letter d with caron":"","Latin small letter d with stroke":"","Latin small letter dotless i":"","Latin small letter e with breve":"","Latin small letter e with caron":"","Latin small letter e with dot above":"","Latin small letter e with macron":"","Latin small letter e with ogonek":"","Latin small letter eng":"","Latin small letter f with hook":"","Latin small letter g with breve":"","Latin small letter g with cedilla":"","Latin small letter g with circumflex":"","Latin small letter g with dot above":"","Latin small letter h with circumflex":"","Latin small letter h with stroke":"","Latin small letter i with breve":"","Latin small letter i with macron":"","Latin small letter i with ogonek":"","Latin small letter i with tilde":"","Latin small letter j with circumflex":"","Latin small letter k with cedilla":"","Latin small letter kra":"","Latin small letter l with acute":"","Latin small letter l with caron":"","Latin small letter l with cedilla":"","Latin small letter l with middle dot":"","Latin small letter l with stroke":"","Latin small letter long s":"","Latin small letter n preceded by apostrophe":"","Latin small letter n with acute":"","Latin small letter n with caron":"","Latin small letter n with cedilla":"","Latin small letter o with breve":"","Latin small letter o with double acute":"","Latin small letter o with macron":"","Latin small letter r with acute":"","Latin small letter r with caron":"","Latin small letter r with cedilla":"","Latin small letter s with acute":"","Latin small letter s with caron":"","Latin small letter s with cedilla":"","Latin small letter s with circumflex":"","Latin small letter t with caron":"","Latin small letter t with cedilla":"","Latin small letter t with stroke":"","Latin small letter u with breve":"","Latin small letter u with double acute":"","Latin small letter u with macron":"","Latin small letter u with ogonek":"","Latin small letter u with ring above":"","Latin small letter u with tilde":"","Latin small letter w with circumflex":"","Latin small letter y with circumflex":"","Latin small letter z with acute":"","Latin small letter z with caron":"","Latin small letter z with dot above":"","Latin small ligature ij":"","Latin small ligature oe":"","Layout table":"Layout table","Leaving %0 code snippet":"","Leaving a to-do list":"","Leaving code snippet":"","Left aligned image":"Left aligned image","Left double quotation mark":"","Left single quotation mark":"","Left-pointing double angle quotation mark":"","leftwards arrow to bar":"","leftwards dashed arrow":"","leftwards double arrow":"","leftwards simple arrow":"","Less-than or equal to":"","Less-than sign":"","Light blue":"Light blue","Light green":"Light green","Light grey":"Light grey",Link:"Link","Link image":"","Link properties":"Link properties","Link URL":"Link URL","Link URL must not be empty.":"","Lira sign":"","List properties":"","Livre tournois sign":"","Logical and":"","Logical or":"","Lower-latin":"","Lower–roman":"",Macron:"","Manat sign":"","Match case":"",Mathematical:"",Media:"","Media embed":"Media embed","Media URL":"Media URL","media widget":"Media widget",MENU_BAR_MENU_EDIT:"",MENU_BAR_MENU_FILE:"",MENU_BAR_MENU_FONT:"",MENU_BAR_MENU_FORMAT:"",MENU_BAR_MENU_HELP:"",MENU_BAR_MENU_INSERT:"",MENU_BAR_MENU_TEXT:"",MENU_BAR_MENU_TOOLS:"",MENU_BAR_MENU_VIEW:"","Merge cell down":"Merge cell down","Merge cell left":"Merge cell left","Merge cell right":"Merge cell right","Merge cell up":"Merge cell up","Merge cells":"Merge cells","Mill sign":"","Minus sign":"","Move focus between form fields (inputs, buttons, etc.)":"","Move focus from an editable area back to the parent widget":"","Move focus in and out of an active dialog window":"","Move focus to the menu bar, navigate between menu bars":"","Move focus to the toolbar, navigate between toolbars":"","Move out of a link":"","Move out of an inline code style":"","Move the caret to allow typing directly after a widget":"","Move the caret to allow typing directly before a widget":"","Move the selection to the next cell":"","Move the selection to the previous cell":"","Multiple styles":"","Multiplication sign":"","N-ary product":"","N-ary summation":"",Nabla:"","Naira sign":"","Navigate through the table":"","Navigate through the toolbar or menu bar":"","New sheqel sign":"",Next:"Next","Next result":"","No links available":"No links available","No preview available":"","No results found":"","No searchable items":"",None:"","Nordic mark sign":"","Not an element of":"","Not equal to":"","Not sign":"","Numbered List":"Numbered List","Numbered list styles toolbar":"","on with exclamation mark with left right arrow above":"","Open in a new tab":"Open in a new tab","Open link in new tab":"Open link in new tab","Open media in new tab":"","Open the accessibility help dialog":"",Orange:"Orange",Original:"",Outset:"",Overline:"",Padding:"",Paragraph:"Paragraph","Paragraph sign":"","Partial differential":"","Paste content":"","Paste content as plain text":"","Paste raw HTML here...":"","Paste the media URL in the input.":"Paste the media URL in the input.","Per mille sign":"","Per ten thousand sign":"","Peseta sign":"","Peso sign":"","Pink marker":"Pink marker","Plain text":"",'Please enter a valid color (e.g. "ff0000").':"","Plus-minus sign":"","Pound sign":"","Press %0 for help.":"","Press Enter to type after or press Shift + Enter to type before the widget":"",Previous:"Previous","Previous result":"","Proportional to":"",Purple:"Purple","Question exclamation mark":"",Red:"Red","Red pen":"Red pen",Redo:"Redo","Registered sign":"","Remove color":"Remove colour","Remove Format":"Remove Format","Remove highlight":"Remove highlight",Replace:"","Replace all":"","Replace from computer":"","Replace image":"","Replace image from computer":"","Replace with…":"","Resize image":"","Resize image (in %0)":"","Resize image to %0":"","Resize image to the original size":"","Restore default":"","Reversed order":"","Reversed paragraph sign":"","Revert autoformatting action":"","Rich Text Editor":"Rich Text Editor","Rich Text Editor. Editing area: %0":"",Ridge:"","Right aligned image":"Right aligned image","Right double quotation mark":"","Right single quotation mark":"","Right-pointing double angle quotation mark":"","rightwards arrow to bar":"","rightwards dashed arrow":"","rightwards double arrow":"","rightwards simple arrow":"",Row:"Row","Ruble sign":"","Rupee sign":"",Save:"Save","Save changes":"","Section sign":"","Select all":"","Select column":"","Select row":"","Show blocks":"","Show more items":"","Show source":"","Side image":"Side image","Single left-pointing angle quotation mark":"","Single low-9 quotation mark":"","Single right-pointing angle quotation mark":"",Small:"Small",Solid:"","soon with rightwards arrow above":"",Source:"Source","Special characters":"","Spesmilo sign":"","Split cell horizontally":"Split cell horizontally","Split cell vertically":"Split cell vertically",Square:"","Square root":"","Start at":"","Start index must be greater than 0.":"",Strikethrough:"Strikethrough","Strikethrough text":"",Style:"",Styles:"",Subscript:"Subscript",Superscript:"Superscript",Table:"","Table alignment toolbar":"","Table cell text alignment":"","Table layout":"Table layout","Table properties":"","Table toolbar":"","Table type":"Table type","Table type options":"Table type options","Tenge sign":"",Text:"","Text alignment":"Text alignment","Text alignment toolbar":"","Text alternative":"Text alternative","Text Alternative":"Text Alternative","Text highlight toolbar":"","Text styles":"","Text to find must not be empty.":"",'The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".':"","The URL must not be empty.":"The URL must not be empty.",'The value is invalid. Try "10px" or "2em" or simply "2".':"","The value must not be empty.":"","The value should be a plain number.":"","There exists":"","These keyboard shortcuts allow for quick access to content editing features.":"","This media URL is not supported.":"This media URL is not supported.","Tilde operator":"",Tiny:"Tiny","Tip: Find some text first in order to replace it.":"","Tip: Paste the URL into the content to embed faster.":"Tip: Paste the URL into the content to embed faster.","To-do List":"","Toggle caption off":"","Toggle caption on":"","Toggle the circle list style":"","Toggle the decimal list style":"","Toggle the decimal with leading zero list style":"","Toggle the disc list style":"","Toggle the lower–latin list style":"","Toggle the lower–roman list style":"","Toggle the square list style":"","Toggle the upper–latin list style":"","Toggle the upper–roman list style":"","top with upwards arrow above":"","Trade mark sign":"","Tugrik sign":"","Turkish lira sign":"",Turquoise:"Turquoise","Two dot leader":"",Underline:"Underline","Underline text":"",Undo:"Undo",Union:"",Unlink:"Unlink","up down arrow with base":"",Update:"Update","Update image URL":"","Upload failed":"Upload failed","Upload from computer":"","Upload image from computer":"","Upload in progress":"Upload in progress","Uploading image":"","Upper-latin":"","Upper-roman":"","upwards arrow to bar":"","upwards dashed arrow":"","upwards double arrow":"","upwards simple arrow":"","Use the following keystrokes for more efficient navigation in the CKEditor 5 user interface.":"","User interface and content navigation keystrokes":"","Vertical text alignment toolbar":"","Via URL":"","Vulgar fraction one half":"","Vulgar fraction one quarter":"","Vulgar fraction three quarters":"",White:"White","Whole words only":"","Widget toolbar":"",Width:"","Won sign":"","Words: %0":"Words: %0","Wrap text":"",Yellow:"Yellow","Yellow marker":"Yellow marker","Yen sign":"","You have no image upload permissions.":""}),t.getPluralForm=function(e){return 1!=e}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));