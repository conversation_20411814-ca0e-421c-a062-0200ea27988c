@import "globals";

/* FORM BUTTONS */

.button, input[type="submit"], input[type="button"], .object-tools a {
  &, &:visited, &:hover {
    display: inline-block;
    background-color: $button-background-color;
    color: $button-text-color;
    border: 0;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
    outline: 0;
    font-size: 12px;
    font-weight: normal;
    text-align: center;
    padding: 0 10px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 100%;
    box-sizing: border-box;
    appearance: none;
    transition: background $transitions-duration;
  }

  &.default {
    font-weight: lighter;
    background-color: $primary-button-background-color;
    color: $primary-button-text-color;
    text-transform: uppercase;
    margin: 0 8px 0 0;
    padding: 0 20px;
  }

  &.transparent {
    background-color: transparent;
  }

  &:hover, &:focus {
    background-color: $button-hover-background-color;
    color: $button-hover-text-color;
  }

  &:active {
    background-color: $button-active-background-color;
    color: $button-active-text-color;
  }
}

.button[disabled], input[type=submit][disabled], input[type=button][disabled] {
  opacity: 0.4;
}

input[type="text"], input[type="email"], input[type="password"], input[type="url"], input[type="number"], textarea, select, .vTextField {
  border-radius: 4px;
  font-size: 13px;
  height: $input-height;
  white-space: nowrap;
  outline: 0;
  box-sizing: border-box;
  margin: 0;
  background-color: $input-background-color;
  color: $input-text-color;
  border: 1px solid $input-border-color;
  padding: 0 12px;
  appearance: none;
  transition: background $transitions-duration, box-shadow $transitions-duration, border $transitions-duration;

  //noinspection CssInvalidPseudoSelector
  &::placeholder {
    color: $input-placeholder-color;
  }

  @include for-mobile {
    fieldset.module & {
      box-shadow: inset 0 2px 6px 0 rgba(0, 0, 0, 0.04)
    }
  }

  &:focus, fieldset.module &:focus {
    box-shadow: 0 0 4px 0 $input-shadow-color;
    border-color: $input-background-color;
  }
}

textarea {
  height: auto;
  line-height: normal;
  padding: 12px;
  white-space: pre-wrap;
  vertical-align: top;
}

.segmented-button {
  &, &:visited, &:hover {
    border: 0;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    text-align: center;
    background-color: $button-background-color;
    color: $button-text-color;
    padding: 0 10px;
    display: inline-block;
    text-transform: none;
    border-radius: 0;
    transition: background $transitions-duration;
  }

  &:hover, &:focus {
    background-color: $button-hover-background-color;
    color: $button-hover-text-color;
  }

  &:active {
    background-color: $button-active-background-color;
    color: $button-active-text-color;
  }

  &.disabled {
    background-color: $button-background-color !important;
    color: $button-text-color;
    opacity: 0.5;
  }

  &.left {
    border-radius: 4px 0 0 4px;
  }

  &.right {
    border-radius: 0 4px 4px 0;
  }
}

input[type=checkbox] {
  display: none;

  &#action-toggle {
    display: none !important;
  }

  + label:before {
    @include font-icon;
    color: $input-icon-color;
    font-size: 12px;
    content: $icon-checkbox-outline;
    letter-spacing: 5px;

    .action-checkbox-column & {
      color: $content-contrast2-text-color;
    }
  }

  &:checked + label:before {
    content: $icon-checkbox;
  }
}

/* SELECTOR */

.selector {
  display: none;
}
