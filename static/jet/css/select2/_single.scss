@import "../globals";

.select2-selection--single {
  height: $input-height;

  .select2-selection__rendered {
    padding-right: 24px;
  }

  .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold;
  }

  .select2-selection__arrow {
    height: 26px;
    position: absolute;
    top: 1px;
    right: 4px;
    width: 20px;

    b:before {
      @include font-icon;
      color: $input-icon-color;
      font-size: 20px;
      content: $icon-arrow-down;
      line-height: 32px;
    }
  }
}

&[dir="rtl"] {
  .select2-selection--single {
    .select2-selection__clear {
      float: left;
    }

    .select2-selection__arrow {
      left: 1px;
      right: auto;
    }
  }
}

&.select2-container--disabled {
  .select2-selection--single {
    background-color: transparentize($input-contrast-background-color, 0.75);
    cursor: default;

    .select2-selection__clear {
      display: none;
    }
  }
}