@import "../globals";

.select2-selection--multiple {
  background-color: $input-background-color !important;
  border: 1px solid $input-border-color;
  cursor: text;
  height: auto;
  min-height: $input-height;

  .select2-selection__rendered {
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding: 0 5px;
    width: 100%;

    li {
      list-style-type: none;
    }
  }

  .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-top: 5px;
    margin-right: 10px;
  }

  .select2-selection__choice {
    background-color: $input-contrast-background-color;
    color: $input-contrast-text-color;
    font-size: 13px;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 5px 5px;
    line-height: normal;
    list-style-type: none;
  }

  .select2-selection__choice__remove {
    color: $input-contrast-text-color;
    cursor: pointer;
    display: inline-block;
    font-weight: bold;
    margin-right: 2px;

    &:hover {
      color: $input-hover-background-color;
    }
  }
}

&[dir="rtl"] {
  .select2-selection--multiple {
    .select2-selection__choice, .select2-selection__placeholder {
      float: right;
    }

    .select2-selection__choice {
      margin-left: 5px;
      margin-right: auto;
    }

    .select2-selection__choice__remove {
      margin-left: 2px;
      margin-right: auto;
    }
  }
}

&.select2-container--disabled {
  .select2-selection--multiple {
    background-color: $input-contrast-background-color;
    cursor: default;
  }

  .select2-selection__choice__remove {
    display: none;
  }
}
