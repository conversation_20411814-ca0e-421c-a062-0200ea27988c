@import "globals";

.sidebar {
  position: fixed;
  width: $sidebar-width;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 6;
  background-color: $sidebar-background-color;
  color: $sidebar-text-color;
  transition: background-color $transitions-duration, transform $transitions-duration;
  transform: translate3d(-100%, 0, 0);

  @include for-mobile {
    width: 360px;
    padding-bottom: 0;
    transition: transform $transitions-duration cubic-bezier(0, 0.5, 0.5, 1);
  }

  @include for-phone {
    width: 80%;
  }

  &.sidebar-opened {
    transform: none;

    @include for-mobile {
      box-shadow: 0 0 30px 10px rgba(0, 0, 0, 0.2);
    }
  }

  body.menu-pinned & {
    @include for-desktop {
      transform: none;
    }
  }

  &-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000;
    opacity: 0;
    z-index: 5;
  }

  &-header {
    height: $sidebar-header-height;
    line-height: $sidebar-header-height;
    transition: transform $transitions-duration;

    &.sidebar-opened {
      @include for-mobile {
        transform: translate3d(360px, 0, 0);
      }

      @include for-phone {
        transform: translate3d(80%, 0, 0);
      }
    }

    &-wrapper {
      display: none;
      background-color: $sidebar-background-color;
      color: $sidebar-text-color;
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      z-index: 6;
      transition: background-color $transitions-duration, transform $transitions-duration;

      @include for-mobile {
        display: block;

        body.scroll-to-bottom & {
          transform: translate3d(0, -100%, 0);
        }
      }

      &.sidebar-opened {
        background-color: $sidebar-contrast-background-color;
        transform: none !important;
      }
    }

    &-menu {
      &, &:visited, &:hover {
        display: inline-block;
        font-size: 14px;
        text-transform: uppercase;
        color: $sidebar-link-color;
        line-height: $sidebar-header-height;
        padding: 0 16px;
        border-right: 1px solid $sidebar-contrast-background-color;
      }

      &-icon {
        font-size: 16px;
        vertical-align: middle;

        &.icon-cross {
          display: none;
          font-size: 20px;
          color: $sidebar-action-color;
        }
      }
    }

    &.sidebar-opened &-menu-icon {
      &.icon-menu {
        display: none;
      }

      &.icon-cross {
        display: inline;
      }
    }
  }

  &-close {
    display: none;
    float: right;
    padding: 4px;
    margin: 12px 18px 0 12px;
    background-color: $sidebar-popup-search-input-background-color;
    border-radius: 5px;

    @include for-mobile {
      display: inline-block;
    }

    &-icon {
      color: $sidebar-popup-search-input-text-color;
      font-size: 28px;
      font-weight: bold;
      vertical-align: middle;
    }
  }

  &-wrapper {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    transform: translate3d(0, 0, 0);
  }

  &-section {
    padding: 20px 0;
    border-bottom: 1px solid $sidebar-contrast-background-color;
    transition: border-bottom-color 0.3s;

    @include for-mobile {
      padding: 10px 0;
    }

    &:last-child, &.last {
      border-bottom: 0;
    }
  }

  &-title {
    display: block;
    color: $sidebar-text-color;
    text-transform: uppercase;
    font-size: 11px;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 14px 0 24px;
    margin-bottom: 10px;
    transition: color $transitions-duration;

    @include for-mobile {
      padding: 12px 18px 12px 30px;
      margin-bottom: 0;

      html.touchevents & {
        padding-left: 20px;
      }
    }

    &-link {
      &, &:visited, &:hover {
        color: $sidebar-text-color;
        font-weight: bold;
        transition: color $transitions-duration;
      }

      &:hover {
        color: $sidebar-hover-title-action-item-color;
      }
    }
  }

  &-link {
    &, &:visited, &:hover {
      display: block;
      color: $sidebar-link-color;
      padding: 8px 12px 8px 20px;
      vertical-align: middle;
      transition: color $transitions-duration, background-color $transitions-duration;
      position: relative;

      @include for-mobile {
        padding: 12px 18px 12px 30px;

        html.touchevents & {
          padding-left: 20px;
        }
      }

      &.icon {
        font-size: 11px;
        text-transform: uppercase;
      }
    }

    &:hover, &.selected {
      color: $sidebar-hover-link-color;
      background-color: $sidebar-hover-background-color;
    }

    &-icon {
      font-size: 18px;
      vertical-align: middle;
      margin-right: 6px;
      color: $sidebar-icon-color;
      transition: color $transitions-duration;
    }

    &-label {
      vertical-align: middle;
      display: block;
      transition: transform $transitions-duration;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      html.touchevents .editing & {
        transform: translate3d(20px, 0, 0);
      }
    }
  }

  &-center-link {
    &, &:visited, &:hover {
      display: block;
      color: $sidebar-action-color;
      font-size: 11px;
      text-align: center;
      padding: 8px 0;
      text-transform: uppercase;
      transition: color $transitions-duration, background-color $transitions-duration;

      @include for-mobile {
        padding: 12px 20px;
      }
    }

    &:hover {
      color: $sidebar-hover-action-color;
      background-color: $sidebar-hover-background-color;
    }
  }

  &-left {
    position: absolute;
    left: 4px;

    html.touchevents & {
      top: 0;
      bottom: 0;
      transition: opacity $transitions-duration, transform $transitions-duration;
    }

    &.collapsible {
      display: none;

      html.touchevents & {
        display: inline-block;
        width: 0;
        opacity: 0;
        transform: scale(0);
        overflow: hidden;
      }
    }

    &-pin, &-unpin {
      &, &:visited, &:hover {
        display: inline-block;
        position: absolute;
        top: 1px;
        font-size: 14px;
        color: $sidebar-action-color;
        transition: color $transitions-duration;

        html.touchevents & {
          position: static;
          padding: 6px;
          margin-top: 2px;
          font-size: 18px;

          @include for-mobile {
            margin-top: 6px;
          }
        }
      }

      &:hover {
        color: $sidebar-hover-action-color;
      }
    }

    .apps-list-pinned &-pin {
      display: none;
    }

    .apps-list &-unpin {
      display: none;
    }
  }

  html.no-touchevents &-link:hover &-left.collapsible {
    display: inline-block;
  }

  html.touchevents .editing &-left.collapsible {
    opacity: 1;
    transform: scale(1);
    width: auto;
  }

  &-right {
    float: right;
    margin-left: 10px;

    &.collapsible {
      display: none;

      html.touchevents & {
        display: inline;
      }
    }

    &-edit {
      display: none;
      font-size: 18px;

      html.touchevents & {
        display: inline;
      }
    }

    &-plus {
      font-size: 14px;
      outline: 0;
    }

    &-arrow {
      color: $sidebar-arrow-color;
      font-size: 16px;
      font-weight: bold !important;
      transition: color $transitions-duration, opacity $transitions-duration;

      html.touchevents .editing & {
        opacity: 0;
      }
    }

    &-remove {
      &, &:visited, &:hover {
        position: relative;
        color: $sidebar-action-color;
        transition: color $transitions-duration;
      }

      &:hover {
        color: $sidebar-hover-action-color;
      }
    }
  }

  &-link:hover &-right.collapsible {
    display: inline-block;
  }

  &-link:hover &-right-arrow {
    color: $sidebar-hover-arrow-color;
  }

  .clone {
    display: none;
  }

  .apps-hide {
    &-label {
      display: none;
    }

    &.apps-visible .apps-hide-label.apps-visible {
      display: inline;
    }

    &.apps-hidden .apps-hide-label.apps-hidden {
      display: inline;
    }
  }

  &-copyright {
    background-color: $sidebar-contrast-background-color;
    color: $sidebar-contrast-text-color;
    height: 32px;
    line-height: 32px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 11px;
    font-weight: bold;
    transition: background-color $transitions-duration, color $transitions-duration;

    @include for-mobile {
      display: none;
    }
  }

  &-popup {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: $sidebar-width;
    color: $sidebar-popup-text-color;
    background-color: $sidebar-popup-background-color;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;

    @include for-mobile {
      width: 360px;
    }

    @include for-phone {
      width: 80%;
    }

    &-container {
      display: none;
      position: fixed;
      top: 0;
      left: $sidebar-width;
      bottom: 0;
      right: 0;
      z-index: 5;

      body.menu-pinned & {
        background-color: transparentize($sidebar-popup-overlay-color, 0.5);
      }

      @include for-mobile {
        left: 0;
      }
    }

    &-section {
      display: none;
    }

    &-title {
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
      padding: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      @include for-mobile {
        padding: 24px 0 24px 20px;
        margin-bottom: 0;
        font-size: 14px;
      }
    }

    &-search {
      background-color: $sidebar-popup-search-input-background-color;
      color: $sidebar-popup-search-input-text-color;
      width: 100%;
      height: 32px;
      text-indent: 20px;
      border: 0;
      font-size: 13px;
      outline: 0;
      padding: 0;
      margin: 0 0 12px 0;
      border-radius: 0;

      //noinspection CssInvalidPseudoSelector
      &::placeholder {
        color: $sidebar-popup-search-input-placeholder-color;
      }

      @include for-mobile {
        font-size: 14px;
        height: 40px;
      }
    }

    &-list {
      margin: 0;
      padding: 0;
      list-style: none;

      &-item {
        display: block;

        a, a:visited, a:hover {
          color: $sidebar-popup-link-text-color;
          padding: 8px 20px;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          @include for-mobile {
            padding: 12px 20px;
          }
        }

        &.selected a {
          background-color: $sidebar-popup-hover-link-background-color;
          color: $sidebar-popup-hover-link-color;
        }
      }
    }
  }

  &-container-toggle {
    float: left;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    line-height: 31px;
    padding: 10px 0 10px 20px;

    &:hover {
      color: $hover-link-color;
    }

    body.login &, body.menu-pinned & {
      display: none;
    }

    @include for-mobile {
      display: none;
    }
  }
}
