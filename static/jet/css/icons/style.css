@font-face {
  font-family: 'jet-icons';
  src:  url('fonts/jet-icons.eot?415d6s');
  src:  url('fonts/jet-icons.eot?415d6s#iefix') format('embedded-opentype'),
    url('fonts/jet-icons.ttf?415d6s') format('truetype'),
    url('fonts/jet-icons.woff?415d6s') format('woff'),
    url('fonts/jet-icons.svg?415d6s#jet-icons') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'jet-icons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-settings:before {
  content: "\e900";
}
.icon-menu:before {
  content: "\e901";
}
.icon-reset:before {
  content: "\e61e";
}
.icon-search:before {
  content: "\e61d";
}
.icon-user:before {
  content: "\e61c";
}
.icon-jet:before {
  content: "\e61b";
}
.icon-refresh:before {
  content: "\e61a";
}
.icon-grid:before {
  content: "\e619";
}
.icon-star:before {
  content: "\e618";
}
.icon-pin:before {
  content: "\e617";
}
.icon-new:before {
  content: "\e616";
}
.icon-edit:before {
  content: "\e615";
}
.icon-clock:before {
  content: "\e611";
}
.icon-calendar:before {
  content: "\e612";
}
.icon-book:before {
  content: "\e60d";
}
.icon-open-external:before {
  content: "\e60e";
}
.icon-data:before {
  content: "\e60f";
}
.icon-question:before {
  content: "\e613";
}
.icon-tick:before {
  content: "\e614";
}
.icon-cross:before {
  content: "\e610";
}
.icon-key:before {
  content: "\e60c";
}
.icon-arrow-right:before {
  content: "\e60b";
}
.icon-arrow-left:before {
  content: "\e60a";
}
.icon-arrow-down:before {
  content: "\e608";
}
.icon-arrow-up:before {
  content: "\e609";
}
.icon-checkbox-outline:before {
  content: "\e607";
}
.icon-remove:before {
  content: "\e600";
}
.icon-add2:before {
  content: "\e601";
}
.icon-exit:before {
  content: "\e602";
}
.icon-add:before {
  content: "\e603";
}
.icon-add3:before {
  content: "\e604";
}
.icon-expand:before {
  content: "\e605";
}
.icon-checkbox:before {
  content: "\e606";
}

