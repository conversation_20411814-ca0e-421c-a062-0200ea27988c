@import "globals";

/* LOGIN FORM */

body.login {
  background: $login-background-color;
  padding: 100px 30px 30px 30px;

  @include for-phone {
    padding: 30px 10px 10px 10px;
  }

  #container {
    background: $login-content-background-color;
    border-radius: 4px;
    min-height: 0;
    padding: 0;
    margin-left: auto;
    margin-right: auto;
    width: 400px;

    @include for-phone {
      width: 100%;
    }
  }

  #header {
    background: $login-header-background-color;
    color: $login-header-text-color;
    text-transform: uppercase;
    font-size: 11px;
    font-weight: bold;
    border-radius: 4px 4px 0 0;
  }

  #content {
    padding: 30px;
  }

  .sidebar {
    display: none;
  }

  .sidebar-header {
    display: none;
  }

  .breadcrumbs {
    display: none;
  }

  #content-main {
    width: 100%;
  }

  .form-row {
    padding: 4px;
    float: left;
    width: 100%;
    box-sizing: border-box;

    label {
      padding-right: 0.5em;
      line-height: 2em;
      font-size: 1em;
      clear: both;

      &.required:after {
        content: '';
      }
    }

    #id_username, #id_password {
      clear: both;
      padding: 6px;
      width: 100%;
      box-sizing: border-box;
    }
  }

  span.help {
    font-size: 10px;
    display: block;
  }

  .submit-row {
    clear: both;
    padding: 20px 0 0 0;
    margin: 0;
    text-align: center;

    input[type="submit"] {
      font-size: 12px;
      font-weight: lighter;
      background-color: $primary-button-background-color;
      color: $primary-button-text-color;
      text-transform: uppercase;

      &:hover, &:focus {
        background-color: $button-hover-background-color;
        color: $button-hover-text-color;
      }

      &:active {
        background-color: $button-active-background-color;
        color: $button-active-text-color;
      }
    }
  }

  .password-reset-link {
    text-align: center;
  }

  #footer {
    padding: 0;
  }
}
