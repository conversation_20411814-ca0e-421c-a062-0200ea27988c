/*
 * Default variable values
 * Create separate themes/theme/_variables.scss to override these variables
 */

/*
 * General
 */

$background-color: #ecf2f6 !default;
$text-color: #6f7e95 !default;
$dim-text-color: #d0dbe6 !default;
$error-text-color: #c14747 !default;

$link-color: #47bac1 !default;
$hover-link-color: #639af5 !default;

$font: Arial, sans-serif !default;
$font-size: 14px !default;

$transitions-duration: 0.3s !default;
$fast-transitions-duration: 0.1s !default;

$mobile-max-width: 960px;
$phone-max-width: 480px;

/*
 * Sidebar
 */

$sidebar-width: 250px !default;
$sidebar-header-height: 44px !default;

$sidebar-background-color: #354052 !default;
$sidebar-contrast-background-color: #2b3647 !default;
$sidebar-contrast-text-color: #6f7e95 !default;

$sidebar-arrow-color: #639af5 !default;
$sidebar-hover-arrow-color: #639af5 !default;

$sidebar-action-color: #47bac1 !default;
$sidebar-hover-action-color: #639af5 !default;

$sidebar-title-action-color: #47bac1 !default;
$sidebar-hover-title-action-item-color: #639af5 !default;

$sidebar-text-color: #6f7e95 !default;
$sidebar-icon-color: #6f7e95 !default;
$sidebar-link-color: #c0cad8 !default;
$sidebar-hover-link-color: #fff !default;
$sidebar-hover-background-color: #2b3647 !default;

$sidebar-popup-search-input-background-color: #d0dbe6 !default;
$sidebar-popup-search-input-text-color: #6f7e95 !default;
$sidebar-popup-search-input-placeholder-color: transparentize(#6f7e95, 0.5) !default;

$sidebar-popup-background-color: #ecf2f6 !default;
$sidebar-popup-text-color: #6f7e95 !default;
$sidebar-popup-overlay-color: #000 !default;

$sidebar-popup-link-text-color: #6f7e95 !default;
$sidebar-popup-hover-link-color: #fff !default;
$sidebar-popup-hover-link-background-color: #639af5 !default;

/*
 * Top
 */

$top-height: 32px !default;

$top-text-color: #6f7e95 !default;
$top-separator-color: #c0d4e8 !default;
$top-link-color: #c0d4e8 !default;
$top-hover-link-color: #639af5 !default;
$top-border-color: #c0d4e8 !default;
$top-icon-color: #47bac1 !default;

$top-dropdown-background-color: #6f7e95 !default;
$top-dropdown-text-color: #ecf2f6 !default;
$top-dropdown-contrast-background-color: #59677e !default;
$top-dropdown-contrast-text-color: #c0cad8 !default;
$top-dropdown-border-color: #76849a !default;
$top-dropdown-link-color: #ecf2f6 !default;
$top-dropdown-hover-link-color: #ecf2f6 !default;
$top-dropdown-icon-color: #ecf2f6 !default;
$top-dropdown-selected-color: #e5e2a5 !default;

/*
 * Content
 */

$content-background-color: #fff !default;
$content-contrast-background-color: #f6fafc !default; //inline list bg
$content-contrast2-background-color: #59677e !default; //table header
$content-contrast3-background-color: #d0dbe6 !default; //delete collapsable
$content-selected-background-color: #fffcc0 !default;
$content-contrast2-text-color: #fff !default;
$content-border-color: #f4f4f4 !default; //row bottom
$content-border2-color: #d0dbe6 !default; //table bottom
$content-selected-border-color: #e5e2a5 !default;

$tab-selected-border-color: #639af5 !default;
$tab-error-border-color: #c14747 !default;

/*
 * Buttons
 */

$button-background-color: #d0dbe6 !default;
$button-hover-background-color: #639af5 !default;
$button-active-background-color: #6f7e95 !default;
$button-text-color: #6f7e95 !default;
$button-hover-text-color: #fff !default;
$button-active-text-color: #fff !default;

$primary-button-background-color: #47bac1 !default;
$primary-button-text-color: #fff !default;

$danger-button-background-color: #c14747 !default;
$danger-button-text-color: #fff !default;

$background-button-background-color: #fff !default;
$background-button-text-color: #6f7e95 !default;

/*
 * Inputs
 */

$input-height: 32px !default;
$input-background-color: #fff !default;
$input-contrast-background-color: #d0dbe6 !default;
$input-border-color: #ecf2f6 !default;
$input-hover-background-color: #639af5 !default;
$input-icon-color: #47bac1 !default;
$input-text-color: #6f7e95 !default;
$input-contrast-text-color: #6f7e95 !default;
$input-hover-text-color: #fff !default;
$input-selected-text-color: #47bac1 !default;
$input-disabled-text-color: #d0dbe6 !default;
$input-placeholder-color: #d0dbe6 !default;
$input-shadow-color: transparentize(#47bac1, 0.25) !default;

$background-input-background-color: #fff !default;
$background-input-border-color: #fff !default;
$background-input-text-color: #6f7e95 !default;

/*
 * Messages
 */

$warning-color: #f0dada !default;
$warning-text-color: #d49d9d !default;
$info-color: #e8e8bd !default;
$info-text-color: #b9b97f !default;
$success-color: #c4ecc5 !default;
$success-text-color: #82b982 !default;

/*
 * Login
 */

$login-background-color: #354052 !default;
$login-title-text-color: #6f7e95 !default;
$login-title-contrast-text-color: #fff !default;
$login-header-background-color: #59677e !default;
$login-header-text-color: #fff !default;
$login-content-background-color: #fff !default;

/*
 * jQuery UI
 */

$jquery-ui-buttonpane-background: #ecf2f6 !default;

$jquery-ui-state-default-background-color: #fff !default;
$jquery-ui-state-default-border-color: #ecf2f6 !default;
$jquery-ui-state-default-text-color: #6f7e95 !default;

$jquery-ui-state-hover-background-color: #639af5 !default;
$jquery-ui-state-hover-border-color: #639af5 !default;
$jquery-ui-state-hover-text-color: #fff !default;

$jquery-ui-state-active-background-color: #47bac1 !default;
$jquery-ui-state-active-border-color: #47bac1 !default;
$jquery-ui-state-active-text-color: #fff !default;

$jquery-ui-state-highlight-background-color: #fff !default;
$jquery-ui-state-highlight-border-color: #639af5 !default;
$jquery-ui-state-highlight-text-color: #639af5 !default;

$jquery-ui-overlay-color: #000 !default;

$jquery-ui-tooltip-background-color: #000 !default;
$jquery-ui-tooltip-text-color: #fff !default;

/*
 * Charts
 */

$chart-fillColor: transparentize($hover-link-color, 0.75) !default;
$chart-strokeColor: $hover-link-color !default;
$chart-pointColor: $content-contrast2-text-color !default;
$chart-pointHighlightFill: $hover-link-color !default;
$chart-scaleGridLineColor: transparentize(#000, 0.9) !default;
$chart-scaleLineColor: transparentize(#000, 0.9) !default;
$chart-scaleFontColor: $content-contrast2-text-color !default;
