@import "globals";

/* MESSAGES & ERRORS */

ul.messagelist {
  padding: 0;
  margin: 0;

  li {
    display: block;
    margin: 0 20px 10px 20px;
    border-radius: 6px;
    padding: 10px;

    @include for-phone {
      margin-left: 10px;
      margin-right: 10px;
    }

    &.success {
      background: $success-color;
      color: $success-text-color;
    }

    &.warning, &.error {
      background: $warning-color;
      color: $warning-text-color;
    }

    &.info, &.debug {
      background: $info-color;
      color: $info-text-color;
    }
  }
}

.errornote {
  display: block;
  margin: 0 0 10px 0;
  border-radius: 6px;
  padding: 10px;
  background: $warning-color;
  color: $warning-text-color;
}

ul.errorlist {
  margin: 0 0 4px;
  padding: 0;
  color: #ba2121;
  background: #fff;

  li {
    font-size: 13px;
    display: block;
    margin-bottom: 4px;

    &:first-child {
      margin-top: 0;
    }

    a {
      color: inherit;
      text-decoration: underline;
    }
  }

  td & {
    margin: 0;
    padding: 0;

    li {
      margin: 0;
    }
  }
}

.form-row.errors {
  ul.errorlist li {
    padding-left: 0;
  }
}

div.system-message {
  margin: 0 20px 10px 20px;
  border-radius: 6px;
  padding: 10px;
  background: $warning-color;
  color: $warning-text-color;

  @include for-phone {
    margin-left: 10px;
    margin-right: 10px;
  }

  p.system-message-title {
    margin: 0;

    &:before {
      @include font-icon;
      content: $icon-cross;
      vertical-align: middle;
      margin-right: 4px;
      color: $warning-text-color;
    }
  }
}

.description {
  font-size: 12px;
  margin: 0;
  padding: 6px 0 0 0;
}
