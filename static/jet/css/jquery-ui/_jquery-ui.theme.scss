@import "../globals";

.ui-widget-content {
  color: $text-color;
  border-color: $content-border-color;
}

.ui-widget, .ui-timepicker-table {
  &.ui-widget-content {
    background: $content-background-color;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.5);
  }
}

.ui-widget {
  font-family: inherit;
  font-size: inherit;
}

.ui-widget-header {
  border: 0;
  background: $content-contrast2-background-color;
  color: $content-contrast2-text-color;
  font-weight: bold;
  a {
    color: $content-contrast2-text-color;
  }
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  border: 1px solid $jquery-ui-state-default-border-color;
  background: $jquery-ui-state-default-background-color;
  font-weight: bold;
  color: $jquery-ui-state-default-text-color;
  border-radius: 3px;
}

.ui-widget-header .ui-state-default {
  background: none;
  color: $content-contrast2-text-color;
  border: 0;
}

.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus {
  border: 1px solid $jquery-ui-state-hover-border-color;
  background: $jquery-ui-state-hover-background-color;
  font-weight: bold;
  color: $jquery-ui-state-hover-text-color;
}

.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
  border: 1px solid $jquery-ui-state-active-border-color;
  background: $jquery-ui-state-active-background-color;
  font-weight: bold;
  color: $jquery-ui-state-active-text-color;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
  border: 1px solid $jquery-ui-state-highlight-border-color;
  background: $jquery-ui-state-highlight-background-color;
  color: $jquery-ui-state-highlight-text-color;
}

.ui-dialog {
  @include for-phone {
    left: 10px !important;
    right: 10px !important;
    width: auto !important;
  }
}

.ui-dialog-buttonpane {
  background: $jquery-ui-buttonpane-background;
  margin: .5em -0.2em -0.2em -0.2em;

  .ui-button {
    border: 0 !important;
    outline: 0;
  }
}

.ui-icon {
  @include font-icon;
  font-size: 16px;
  font-weight: bold;
  background: none !important;
  text-indent: 0;
  overflow: visible;
}

.ui-icon-circle-triangle-e:before {
  content: $icon-arrow-right;
}

.ui-icon-circle-triangle-w:before {
  content: $icon-arrow-left;
}

.ui-icon-closethick:before {
  content: $icon-cross;
}

.ui-widget-overlay {
  background: $jquery-ui-overlay-color;
  opacity: 0.5;
  filter: Alpha(Opacity=50);
}

.ui-tooltip {
  background: $jquery-ui-tooltip-background-color !important;
  color: $jquery-ui-tooltip-text-color;
  border: 0;
  box-shadow: none !important;
  opacity: 0.8;
  font-size: 13px;
  pointer-events: none;
}

.ui-datepicker, .ui-timepicker {
  table {
    margin: 0 0 .4em;
    background: transparent;
    border-radius: 0;
    box-shadow: none;
  }

  th {
    background: inherit;
    color: inherit;
    text-transform: inherit;
  }

  tbody tr {
    border-bottom: inherit;
  }
}

.ui-datepicker table {
  margin: 0 0 .4em;
}

.ui-timepicker-table table {
  margin: .15em 0 0;
}
