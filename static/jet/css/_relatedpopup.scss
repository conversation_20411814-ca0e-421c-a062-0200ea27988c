@import "globals";

/* POPUP */

.related-popup {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 4;
  padding-left: 250px;
  box-sizing: border-box;
  display: none;
  background: $background-color;
  background-clip: content-box;
  -webkit-overflow-scrolling: touch;
  overflow-y: scroll;

  @include for-mobile {
    padding-left: 0;
  }

  iframe {
    border: 0;
    width: 100%;
    height: 100%;
  }

  &-container {
    display: none;
    background-color: transparentize($sidebar-popup-overlay-color, 0.5);
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 15;

    .loading-indicator {
      display: none;
      font-size: 96px;
      color: $content-contrast2-text-color;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -48px;
      margin-top: -48px;
      animation: spin 4s linear infinite;
    }
  }

  &-back {
    &, &:visited, &:hover {
      display: none;
      background: $content-contrast2-background-color;
      color: $content-contrast2-text-color;
      position: absolute;
      top: 20px;
      left: 250px;
      z-index: 5;
      width: 100px;
      padding: 14px 6px 14px 0;
      text-align: center;
      margin-left: -100px;
      box-sizing: border-box;
      text-transform: uppercase;
      border-radius: 6px 0 0 6px;
      transition: background-color $transitions-duration, color $transitions-duration;

      @include for-mobile {
        margin-left: 0;
        top: auto;
        bottom: 10px;
        left: 10px;
        width: auto;
        padding: 10px;
        border-radius: 6px;
      }
    }

    &:hover, &:focus {
      background: $background-color;
      color: $text-color;

      @include for-mobile {
        background: $button-hover-background-color;
        color: $button-hover-text-color;
      }
    }

    &-icon {
      vertical-align: middle;
      font-weight: bold;
      font-size: 18px;
    }

    &-label {
      @include for-mobile {
        display: none;
      }
    }
  }
}
