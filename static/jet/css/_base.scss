/*
  DJANGO JET Admin styles
*/

@import "globals";

html, body {
  margin: 0;
  padding: 0;
}

html {
  font-size: 87.5%;

  @include for-mobile {
    font-size: 100%;
  }
}

body {
  height: 100%;
  background: $background-color;
  color: $text-color;
  font-family: $font;
  text-size-adjust: 100%;

  @include for-mobile {
    padding-top: $sidebar-header-height;
  }

  &.non-scrollable {
    overflow: hidden;
  }

  &.popup {
    @include for-mobile {
      padding-top: 0;
    }
  }
}

/* PAGE STRUCTURE */

#container {
  padding: 0;
  min-height: 100%;
  transition: padding-left 0.3s;

  body.menu-pinned & {
    padding-left: $sidebar-width;
  }

  body.menu-pinned.popup & {
    padding-left: 0;
  }

  @include for-mobile {
    &, body.menu-pinned & {
      padding-left: 0;
    }
  }

  .popup & {
    padding-left: 0;
  }
}

#content {
  padding: 20px;

  @include for-phone {
    padding: 10px;
  }

  & > h1 {
    display: none;
  }
}

#content-main {
  float: left;
  width: 100%;

  @include for-mobile {
    float: none;
  }
}

#content-related {
  float: right;
  width: 260px;
  position: relative;
  margin-right: -300px;

  @include for-mobile {
    float: none;
    width: 100%;
    margin-left: 0;
    position: static;
  }
}

#footer {
  clear: both;
  padding: 10px;

  &:empty {
    display: none;
  }
}

.dialog-confirm {
  display: none;
}

/* COLUMN TYPES */

.colMS {
  margin-right: 300px;

  @include for-mobile {
    margin-right: 0;
  }
}

.colSM {
  margin-left: 300px;

  @include for-mobile {
    margin-left: 0;
  }
}

.colSM #content-related {
  float: left;
  margin-right: 0;
  margin-left: -300px;

  @include for-mobile {
    float: none;
    margin-left: 0;
  }
}

.colSM #content-main {
  float: right;

  @include for-mobile {
    float: none;
  }
}

.popup .colM {
  width: auto;
}

@import "jquery-ui/jquery-ui.theme";
@import "select2/layout";
@import "content";
@import "forms";
@import "tables";
@import "messages";
@import "header";
@import "breadcrumbs";
@import "modules";
@import "object-tools";
@import "changeform";
@import "changelist";
@import "sidebar";
@import "relatedpopup";
@import "dashboard";
@import "delete-confirmation";
@import "login";
