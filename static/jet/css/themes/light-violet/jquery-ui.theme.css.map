{"version": 3, "sources": ["themes/light-violet/jquery-ui.theme.css", "_helpers.scss", "jquery-ui/_jquery-ui.theme.scss", "themes/light-violet/_variables.scss", "icons/_variables.scss"], "names": [], "mappings": "AAAA,QCAA,YACW,CAAA,YACV,SAGO,UACC,eACK,CAAA,IACb,UAGQ,CAAA,IACR,WAGQ,CAAA,qBAGM,WACJ,aACA,CAAA,UAGR,UACM,CAAA,KACR,aAAA,AAOe,kBAAA,CAAA,KADZ,aAAA,AACY,kBAAA,CAAA,KADZ,aAAA,AACY,kBAAA,CAAA,KADZ,aAAA,AACY,kBAAA,CAAA,KADZ,aAAA,AACY,kBAAA,CAAA,KADZ,aAAA,AACY,kBAAA,CAAA,KADZ,aAAA,AACY,YAAA,CAAA,KADZ,aAAA,AACY,kBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,gBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,kBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,oBAAA,AACY,yBAAA,CAAA,MADZ,oBAAA,AACY,yBAAA,CAAA,MADZ,oBAAA,AACY,yBAAA,CAAA,MADZ,oBAAA,AACY,yBAAA,CAAA,MADZ,oBAAA,AACY,yBAAA,CAAA,MADZ,oBAAA,AACY,yBAAA,CAAA,MADZ,oBAAA,AACY,mBAAA,CAAA,MADZ,oBAAA,AACY,yBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,iBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,KADZ,YAAA,AACY,iBAAA,CAAA,KADZ,YAAA,AACY,iBAAA,CAAA,KADZ,YAAA,AACY,iBAAA,CAAA,KADZ,YAAA,AACY,iBAAA,CAAA,KADZ,YAAA,AACY,iBAAA,CAAA,KADZ,YAAA,AACY,iBAAA,CAAA,KADZ,YAAA,AACY,WAAA,CAAA,KADZ,YAAA,AACY,iBAAA,CAAA,MADZ,gBAAA,AACY,qBAAA,CAAA,MADZ,gBAAA,AACY,qBAAA,CAAA,MADZ,gBAAA,AACY,qBAAA,CAAA,MADZ,gBAAA,AACY,qBAAA,CAAA,MADZ,gBAAA,AACY,qBAAA,CAAA,MADZ,gBAAA,AACY,qBAAA,CAAA,MADZ,gBAAA,AACY,eAAA,CAAA,MADZ,gBAAA,AACY,qBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,kBAAA,AACY,iBAAA,CAAA,MADZ,kBAAA,AACY,uBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,mBAAA,AACY,kBAAA,CAAA,MADZ,mBAAA,AACY,wBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,MADZ,iBAAA,AACY,gBAAA,CAAA,MADZ,iBAAA,AACY,sBAAA,CAAA,SACX,iBAKO,CAAA,SACX,iBAGW,CAAA,YACX,qBAGQ,CAAA,wBA2BT,KAAkB,iCAAA,AAAkB,wBAAA,CAAA,CA3B3B,AA2B2B,gBAApC,KAAkB,iCAAA,AAAkB,wBAAA,CAAA,CAAA,mBCvEpC,cCQa,oBAkFU,CAAA,oEDrFS,gBC+EL,sCAAA,AD5EA,2CAAA,CAAA,WACxB,oBAIY,iBACF,CAAA,kBACZ,SAGO,mBCoE6B,cA9ExB,gBDaE,CAAA,oBAJE,aCTJ,CAAA,2FDmB8D,yBAAA,ACgGtD,gCAAA,gBAFI,iBD3FV,cCtBF,kBAAA,ADwBI,wBAAA,CAAA,oCAGC,gBACJ,cC5BD,QD8BX,CAAM,0KAGuJ,yBAAA,AC6D/H,gCAAA,mBAAA,iBD1DjB,UC6DW,CAAA,wFDzD+C,yBAAA,ACjCxD,gCAAA,mBAAA,iBDoCF,UCuDY,CAAA,iGDnDoD,yBAAA,AC1C5D,gCAAA,gBA4GM,aA5GN,CAAA,0CFmCW,WCa9B,qBAAA,AAEU,2BAAA,sBAAA,AACC,4BAAA,qBACA,CAAA,CAAA,sBAIX,mBCiBqC,gCDf3B,CAAA,iCAER,oBACU,SACR,CAAO,SACR,wBDFY,WACN,kBACK,mBACC,oBACC,oBACE,cACL,mCAGa,kCACC,qBAChB,eAAA,ACJE,qBAAA,iBACE,2BACD,cACD,gBACD,CAAA,kCAGc,WEnEP,CAAA,kCFuEO,WACxB,CAAO,2BAGU,WACjB,CAAA,mBACD,gBAGC,YACA,wBACQ,CAAA,YACT,2BAGiD,WAChD,SACA,2BACY,YACZ,eAAA,AACA,qBAAA,mBACA,CAAA,0CAGc,gBAEJ,uBACI,gBACZ,eACA,CAAA,oCALY,mBASF,cACL,sBACL,CAAc,gDAGhB,qBACE,CAAa,qBACd,eAIK,CAAE,2BACT,gBAGS,CAAA", "file": "themes/light-violet/jquery-ui.theme.css", "sourcesContent": [".hidden{display:none}.clear-list{margin:0;padding:0;list-style:none}.fl{float:left}.fr{float:right}.cf:before,.cf:after{content:\"\";display:table}.cf:after{clear:both}.p10{padding:10px}.p20{padding:20px}.p30{padding:30px}.p40{padding:40px}.p50{padding:50px}.p60{padding:60px}.p70{padding:70px}.p80{padding:80px}.pt10{padding-top:10px}.pt20{padding-top:20px}.pt30{padding-top:30px}.pt40{padding-top:40px}.pt50{padding-top:50px}.pt60{padding-top:60px}.pt70{padding-top:70px}.pt80{padding-top:80px}.pr10{padding-right:10px}.pr20{padding-right:20px}.pr30{padding-right:30px}.pr40{padding-right:40px}.pr50{padding-right:50px}.pr60{padding-right:60px}.pr70{padding-right:70px}.pr80{padding-right:80px}.pb10{padding-bottom:10px}.pb20{padding-bottom:20px}.pb30{padding-bottom:30px}.pb40{padding-bottom:40px}.pb50{padding-bottom:50px}.pb60{padding-bottom:60px}.pb70{padding-bottom:70px}.pb80{padding-bottom:80px}.pl10{padding-left:10px}.pl20{padding-left:20px}.pl30{padding-left:30px}.pl40{padding-left:40px}.pl50{padding-left:50px}.pl60{padding-left:60px}.pl70{padding-left:70px}.pl80{padding-left:80px}.m10{margin:10px}.m20{margin:20px}.m30{margin:30px}.m40{margin:40px}.m50{margin:50px}.m60{margin:60px}.m70{margin:70px}.m80{margin:80px}.mt10{margin-top:10px}.mt20{margin-top:20px}.mt30{margin-top:30px}.mt40{margin-top:40px}.mt50{margin-top:50px}.mt60{margin-top:60px}.mt70{margin-top:70px}.mt80{margin-top:80px}.mr10{margin-right:10px}.mr20{margin-right:20px}.mr30{margin-right:30px}.mr40{margin-right:40px}.mr50{margin-right:50px}.mr60{margin-right:60px}.mr70{margin-right:70px}.mr80{margin-right:80px}.mb10{margin-bottom:10px}.mb20{margin-bottom:20px}.mb30{margin-bottom:30px}.mb40{margin-bottom:40px}.mb50{margin-bottom:50px}.mb60{margin-bottom:60px}.mb70{margin-bottom:70px}.mb80{margin-bottom:80px}.ml10{margin-left:10px}.ml20{margin-left:20px}.ml30{margin-left:30px}.ml40{margin-left:40px}.ml50{margin-left:50px}.ml60{margin-left:60px}.ml70{margin-left:70px}.ml80{margin-left:80px}.pos_rel{position:relative}.pos_abs{position:absolute}.fill_width{width:100% !important}@keyframes spin{100%{transform:rotate(360deg)}}.ui-widget-content{color:#8B9AA7;border-color:#f1f2f4}.ui-widget.ui-widget-content,.ui-timepicker-table.ui-widget-content{background:#fff;box-shadow:0 0 10px 0 rgba(0,0,0,0.5)}.ui-widget{font-family:inherit;font-size:inherit}.ui-widget-header{border:0;background:#E3ECF2;color:#8B9AA7;font-weight:bold}.ui-widget-header a{color:#8B9AA7}.ui-state-default,.ui-widget-content .ui-state-default,.ui-widget-header .ui-state-default{border:1px solid #EDEDED;background:#fff;font-weight:bold;color:#8B9AA7;border-radius:3px}.ui-widget-header .ui-state-default{background:none;color:#8B9AA7;border:0}.ui-state-hover,.ui-widget-content .ui-state-hover,.ui-widget-header .ui-state-hover,.ui-state-focus,.ui-widget-content .ui-state-focus,.ui-widget-header .ui-state-focus{border:1px solid #1cacfc;background:#1cacfc;font-weight:bold;color:#fff}.ui-state-active,.ui-widget-content .ui-state-active,.ui-widget-header .ui-state-active{border:1px solid #A464C4;background:#A464C4;font-weight:bold;color:#fff}.ui-state-highlight,.ui-widget-content .ui-state-highlight,.ui-widget-header .ui-state-highlight{border:1px solid #1cacfc;background:#fff;color:#1cacfc}@media only screen and (max-width: 480px){.ui-dialog{left:10px !important;right:10px !important;width:auto !important}}.ui-dialog-buttonpane{background:#E3ECF2;margin:.5em -0.2em -0.2em -0.2em}.ui-dialog-buttonpane .ui-button{border:0 !important;outline:0}.ui-icon{font-family:'jet-icons';speak:none;font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block;font-size:16px;font-weight:bold;background:none !important;text-indent:0;overflow:visible}.ui-icon-circle-triangle-e:before{content:\"\"}.ui-icon-circle-triangle-w:before{content:\"\"}.ui-icon-closethick:before{content:\"\"}.ui-widget-overlay{background:#000;opacity:0.5;filter:Alpha(Opacity=50)}.ui-tooltip{background:#000 !important;color:#fff;border:0;box-shadow:none !important;opacity:0.8;font-size:13px;pointer-events:none}.ui-datepicker table,.ui-timepicker table{margin:0 0 .4em;background:transparent;border-radius:0;box-shadow:none}.ui-datepicker th,.ui-timepicker th{background:inherit;color:inherit;text-transform:inherit}.ui-datepicker tbody tr,.ui-timepicker tbody tr{border-bottom:inherit}.ui-datepicker table{margin:0 0 .4em}.ui-timepicker-table table{margin:.15em 0 0}\n", ".hidden {\n  display: none;\n}\n\n.clear-list {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n}\n\n.fl {\n  float: left;\n}\n\n.fr {\n  float: right;\n}\n\n.cf:before, .cf:after {\n  content: \"\";\n  display: table;\n}\n\n.cf:after {\n  clear: both;\n}\n\n@each $class, $style in (p, padding), (pt, padding-top), (pr, padding-right), (pb, padding-bottom), (pl, padding-left),\n                        (m, margin), (mt, margin-top), (mr, margin-right), (mb, margin-bottom), (ml, margin-left) {\n  @for $i from 1 through 8 {\n    $value: $i * 10;\n    .#{$class}#{$value} {\n      #{$style}: #{$value}px;\n    }\n  }\n}\n\n.pos_rel {\n  position: relative;\n}\n\n.pos_abs {\n  position: absolute;\n}\n\n.fill_width {\n  width: 100% !important;\n}\n\n@mixin for-width($width) {\n  @media only screen and (max-width: $width) {\n    @content;\n  }\n}\n\n@mixin for-desktop {\n  @media only screen and (min-width: $mobile-max-width) {\n    @content;\n  }\n}\n\n@mixin for-mobile {\n  @include for-width($mobile-max-width) {\n    @content;\n  }\n}\n\n@mixin for-phone {\n  @include for-width($phone-max-width) {\n    @content;\n  }\n}\n\n@keyframes spin { 100% { transform: rotate(360deg); } }\n\n@mixin font-icon {\n  font-family: 'jet-icons';\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n\n  /* Better Font Rendering =========== */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  display: inline-block;\n}\n\n/// Convert angle\n/// <AUTHOR> Eppstein\n/// @param {Number} $value - Value to convert\n/// @param {String} $unit - Unit to convert to\n/// @return {Number} Converted angle\n@function convert-angle($value, $unit) {\n  $convertable-units: deg grad turn rad;\n  $conversion-factors: 1 (10grad/9deg) (1turn/360deg) (3.1415926rad/180deg);\n  @if index($convertable-units, unit($value)) and index($convertable-units, $unit) {\n    @return $value\n             / nth($conversion-factors, index($convertable-units, unit($value)))\n             * nth($conversion-factors, index($convertable-units, $unit));\n  }\n\n  @warn \"Cannot convert `#{unit($value)}` to `#{$unit}`.\";\n}\n\n/// Test if `$value` is an angle\n/// @param {*} $value - Value to test\n/// @return {Bool}\n@function is-direction($value) {\n  $is-direction: index((to top, to top right, to right top, to right, to bottom right, to right bottom, to bottom, to bottom left, to left bottom, to left, to left top, to top left), $value);\n  $is-angle: type-of($value) == 'number' and index('deg' 'grad' 'turn' 'rad', unit($value));\n\n  @return $is-direction or $is-angle;\n}\n\n/// Convert a direction to legacy syntax\n/// @param {Keyword | Angle} $value - Value to convert\n/// @require {function} is-direction\n/// @require {function} convert-angle\n@function legacy-direction($value) {\n  @if is-direction($value) == false {\n    @warn \"Cannot convert `#{$value}` to legacy syntax because it doesn't seem to be an angle or a direction\";\n  }\n\n  $conversion-map: (\n    to top          : bottom,\n    to top right    : bottom left,\n    to right top    : left bottom,\n    to right        : left,\n    to bottom right : top left,\n    to right bottom : left top,\n    to bottom       : top,\n    to bottom left  : top right,\n    to left bottom  : right top,\n    to left         : right,\n    to left top     : right bottom,\n    to top left     : bottom right\n  );\n\n  @if map-has-key($conversion-map, $value) {\n    @return map-get($conversion-map, $value);\n  }\n\n  @return 90deg - convert-angle($value, 'deg');\n}\n\n/// Mixin printing a linear-gradient\n/// as well as a plain color fallback\n/// and the `-webkit-` prefixed declaration\n/// @access public\n/// @param {String | List | Angle} $direction - Linear gradient direction\n/// @param {Arglist} $color-stops - List of color-stops composing the gradient\n@mixin linear-gradient($direction, $color-stops...) {\n  @if is-direction($direction) == false {\n    $color-stops: ($direction, $color-stops);\n    $direction: 180deg;\n  }\n\n  background: nth(nth($color-stops, 1), 1);\n  background: -webkit-linear-gradient(legacy-direction($direction), $color-stops);\n  background: linear-gradient($direction, $color-stops);\n}", "@import \"../globals\";\n\n.ui-widget-content {\n  color: $text-color;\n  border-color: $content-border-color;\n}\n\n.ui-widget, .ui-timepicker-table {\n  &.ui-widget-content {\n    background: $content-background-color;\n    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.5);\n  }\n}\n\n.ui-widget {\n  font-family: inherit;\n  font-size: inherit;\n}\n\n.ui-widget-header {\n  border: 0;\n  background: $content-contrast2-background-color;\n  color: $content-contrast2-text-color;\n  font-weight: bold;\n  a {\n    color: $content-contrast2-text-color;\n  }\n}\n\n.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {\n  border: 1px solid $jquery-ui-state-default-border-color;\n  background: $jquery-ui-state-default-background-color;\n  font-weight: bold;\n  color: $jquery-ui-state-default-text-color;\n  border-radius: 3px;\n}\n\n.ui-widget-header .ui-state-default {\n  background: none;\n  color: $content-contrast2-text-color;\n  border: 0;\n}\n\n.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus {\n  border: 1px solid $jquery-ui-state-hover-border-color;\n  background: $jquery-ui-state-hover-background-color;\n  font-weight: bold;\n  color: $jquery-ui-state-hover-text-color;\n}\n\n.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {\n  border: 1px solid $jquery-ui-state-active-border-color;\n  background: $jquery-ui-state-active-background-color;\n  font-weight: bold;\n  color: $jquery-ui-state-active-text-color;\n}\n\n.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {\n  border: 1px solid $jquery-ui-state-highlight-border-color;\n  background: $jquery-ui-state-highlight-background-color;\n  color: $jquery-ui-state-highlight-text-color;\n}\n\n.ui-dialog {\n  @include for-phone {\n    left: 10px !important;\n    right: 10px !important;\n    width: auto !important;\n  }\n}\n\n.ui-dialog-buttonpane {\n  background: $jquery-ui-buttonpane-background;\n  margin: .5em -0.2em -0.2em -0.2em;\n\n  .ui-button {\n    border: 0 !important;\n    outline: 0;\n  }\n}\n\n.ui-icon {\n  @include font-icon;\n  font-size: 16px;\n  font-weight: bold;\n  background: none !important;\n  text-indent: 0;\n  overflow: visible;\n}\n\n.ui-icon-circle-triangle-e:before {\n  content: $icon-arrow-right;\n}\n\n.ui-icon-circle-triangle-w:before {\n  content: $icon-arrow-left;\n}\n\n.ui-icon-closethick:before {\n  content: $icon-cross;\n}\n\n.ui-widget-overlay {\n  background: $jquery-ui-overlay-color;\n  opacity: 0.5;\n  filter: Alpha(Opacity=50);\n}\n\n.ui-tooltip {\n  background: $jquery-ui-tooltip-background-color !important;\n  color: $jquery-ui-tooltip-text-color;\n  border: 0;\n  box-shadow: none !important;\n  opacity: 0.8;\n  font-size: 13px;\n  pointer-events: none;\n}\n\n.ui-datepicker, .ui-timepicker {\n  table {\n    margin: 0 0 .4em;\n    background: transparent;\n    border-radius: 0;\n    box-shadow: none;\n  }\n\n  th {\n    background: inherit;\n    color: inherit;\n    text-transform: inherit;\n  }\n\n  tbody tr {\n    border-bottom: inherit;\n  }\n}\n\n.ui-datepicker table {\n  margin: 0 0 .4em;\n}\n\n.ui-timepicker-table table {\n  margin: .15em 0 0;\n}\n", "/*\n * Customizable variables\n * Update these variable to create theme\n */\n\n/*\n * General\n */\n\n$background-color: #f8fafc;\n$text-color: #8B9AA7;\n$dim-text-color: #c0c6cc;\n$error-text-color: #c7254e;\n\n$link-color: #79A7D8;\n$hover-link-color: #1cacfc;\n\n$contrast-color: #A464C4;\n\n$font: Arial, sans-serif !default;\n$font-size: 14px;\n\n/*\n * Sidebar\n */\n\n$sidebar-width: 250px;\n\n$sidebar-background-color: #554488;\n$sidebar-contrast-background-color: #4d3b73;\n$sidebar-contrast-text-color: #9988cc;\n\n$sidebar-arrow-color: #40305F;\n$sidebar-hover-arrow-color: #FDBB5E;\n\n$sidebar-action-color: #9988cc;\n$sidebar-hover-action-color: #FDBB5E;\n\n$sidebar-title-action-color: #fff;\n$sidebar-hover-title-action-item-color: #FDBB5E;\n\n$sidebar-text-color: #9988cc;\n$sidebar-icon-color: #9988cc;\n$sidebar-link-color: #C2B8E0;\n$sidebar-hover-link-color: #fff;\n$sidebar-hover-background-color: #443366;\n\n$sidebar-popup-search-input-background-color: #E3ECF2;\n$sidebar-popup-search-input-text-color: #7f8fa4;\n$sidebar-popup-search-input-placeholder-color: #bdcbde;\n\n$sidebar-popup-background-color: #f7f8fa;\n$sidebar-popup-text-color: #7f8fa4;\n$sidebar-popup-overlay-color: #000;\n\n$sidebar-popup-link-text-color: #7f8fa4;\n$sidebar-popup-hover-link-color: #fff;\n$sidebar-popup-hover-link-background-color: #1cacfc;\n\n/*\n * Top\n */\n\n$top-height: 32px;\n\n$top-text-color: #8B9EAB;\n$top-separator-color: #C6D8E4;\n$top-link-color: #C6D8E4;\n$top-hover-link-color: #1cacfc;\n$top-border-color: #dce0e6;\n$top-icon-color: $link-color;\n\n$top-dropdown-background-color: #5e4d99;\n$top-dropdown-text-color: #fff;\n$top-dropdown-contrast-background-color: #4d3b73;\n$top-dropdown-contrast-text-color: #c8beeb;\n$top-dropdown-border-color: #594585;\n$top-dropdown-link-color: #fff;\n$top-dropdown-hover-link-color: #fff;\n$top-dropdown-icon-color: #c8beeb;\n$top-dropdown-selected-color: #EFEDC8;\n\n/*\n * Content\n */\n\n$content-background-color: #fff;\n$content-contrast-background-color: #F8FAFC; //inline list bg\n$content-contrast2-background-color: #E3ECF2; //table header\n$content-contrast3-background-color: #EEF3F7; //dashboard, delete collapsable\n$content-selected-background-color: #FFFDDB;\n$content-contrast2-text-color: $text-color;\n$content-border-color: #f1f2f4; //row bottom\n$content-border2-color: #D5E3EC; //table bottom\n$content-selected-border-color: #EFEDC8;\n\n$tab-selected-border-color: #1cacfc;\n$tab-error-border-color: #c7254e;\n\n/*\n * Buttons\n */\n\n$button-background-color: #E3ECF2;\n$button-hover-background-color: #1cacfc;\n$button-active-background-color: $contrast-color;\n$button-text-color: #7f8fa4;\n$button-hover-text-color: #fff;\n$button-active-text-color: #fff;\n\n$primary-button-background-color: $contrast-color;\n$primary-button-text-color: #fff;\n\n$danger-button-background-color: #c7254e;\n$danger-button-text-color: #fff;\n\n$background-button-background-color: #fff;\n$background-button-text-color: #7f8fa4;\n\n/*\n * Inputs\n */\n\n$input-background-color: #fff;\n$input-contrast-background-color: #E3ECF2;\n$input-border-color: #EDEDED;\n$input-hover-background-color: #1cacfc;\n$input-icon-color: $link-color;\n$input-text-color: $text-color;\n$input-contrast-text-color: #7f8fa4;\n$input-hover-text-color: #fff;\n$input-selected-text-color: $contrast-color;\n$input-disabled-text-color: $dim-text-color;\n$input-placeholder-color: #999;\n$input-shadow-color: transparentize($contrast-color, 0.25);\n\n$background-input-background-color: #fff;\n$background-input-border-color: #fff;\n$background-input-text-color: $text-color;\n\n/*\n * Messages\n */\n\n$warning-color: #e75e40;\n$warning-text-color: #fff;\n$info-color: #FCA326;\n$info-text-color: #fff;\n$success-color: #2d9fd8;\n$success-text-color: #fff;\n\n/*\n * Login\n */\n\n$login-background-color: $sidebar-background-color;\n$login-title-text-color: #aaddcc;\n$login-title-contrast-text-color: #fff;\n$login-header-background-color: $content-contrast2-background-color;\n$login-header-text-color: $content-contrast2-text-color;\n$login-content-background-color: #fff;\n\n/*\n * jQuery UI\n */\n\n$jquery-ui-buttonpane-background: $content-contrast2-background-color;\n\n$jquery-ui-state-default-background-color: $input-background-color;\n$jquery-ui-state-default-border-color: $input-border-color;\n$jquery-ui-state-default-text-color: $input-text-color;\n\n$jquery-ui-state-hover-background-color: $button-hover-background-color;\n$jquery-ui-state-hover-border-color: $button-hover-background-color;\n$jquery-ui-state-hover-text-color: $button-hover-text-color;\n\n$jquery-ui-state-active-background-color: $button-active-background-color;\n$jquery-ui-state-active-border-color: $button-active-background-color;\n$jquery-ui-state-active-text-color: $button-active-text-color;\n\n$jquery-ui-state-highlight-background-color: $input-background-color;\n$jquery-ui-state-highlight-border-color: $hover-link-color;\n$jquery-ui-state-highlight-text-color: $hover-link-color;\n\n$jquery-ui-overlay-color: #000;\n\n$jquery-ui-tooltip-background-color: #000;\n$jquery-ui-tooltip-text-color: #fff;\n\n/*\n * Charts\n */\n\n$chart-fillColor: transparentize($hover-link-color, 0.75);\n$chart-strokeColor: $hover-link-color;\n$chart-pointColor: #fff;\n$chart-pointHighlightFill: $hover-link-color;\n$chart-scaleGridLineColor: transparentize(#000, 0.9);\n$chart-scaleLineColor: transparentize(#000, 0.9);\n$chart-scaleFontColor: $content-contrast2-text-color;\n", "$icomoon-font-path: \"fonts\" !default;\n\n$icon-settings: \"\\e900\";\n$icon-menu: \"\\e901\";\n$icon-reset: \"\\e61e\";\n$icon-search: \"\\e61d\";\n$icon-user: \"\\e61c\";\n$icon-jet: \"\\e61b\";\n$icon-refresh: \"\\e61a\";\n$icon-grid: \"\\e619\";\n$icon-star: \"\\e618\";\n$icon-pin: \"\\e617\";\n$icon-new: \"\\e616\";\n$icon-edit: \"\\e615\";\n$icon-clock: \"\\e611\";\n$icon-calendar: \"\\e612\";\n$icon-book: \"\\e60d\";\n$icon-open-external: \"\\e60e\";\n$icon-data: \"\\e60f\";\n$icon-question: \"\\e613\";\n$icon-tick: \"\\e614\";\n$icon-cross: \"\\e610\";\n$icon-key: \"\\e60c\";\n$icon-arrow-right: \"\\e60b\";\n$icon-arrow-left: \"\\e60a\";\n$icon-arrow-down: \"\\e608\";\n$icon-arrow-up: \"\\e609\";\n$icon-checkbox-outline: \"\\e607\";\n$icon-remove: \"\\e600\";\n$icon-add2: \"\\e601\";\n$icon-exit: \"\\e602\";\n$icon-add: \"\\e603\";\n$icon-add3: \"\\e604\";\n$icon-expand: \"\\e605\";\n$icon-checkbox: \"\\e606\";\n\n"], "sourceRoot": "/source/"}