/*
 * Customizable variables
 * Update these variable to create theme
 */

/*
 * General
 */

$background-color: #f8fafc;
$text-color: #8B9AA7;
$dim-text-color: #c0c6cc;
$error-text-color: #c7254e;

$link-color: #78ACE3;
$hover-link-color: #1cacfc;

$contrast-color: #5EADDE;

$font: Arial, sans-serif !default;
$font-size: 14px;

/*
 * Sidebar
 */

$sidebar-width: 250px;

$sidebar-background-color: #2980B9;
$sidebar-contrast-background-color: #1B75B1;
$sidebar-contrast-text-color: #7BBCE5;

$sidebar-arrow-color: #176497;
$sidebar-hover-arrow-color: #FDBB5E;

$sidebar-action-color: #7BBCE5;
$sidebar-hover-action-color: #FDBB5E;

$sidebar-title-action-color: #fff;
$sidebar-hover-title-action-item-color: #FDBB5E;

$sidebar-text-color: #7BBCE5;
$sidebar-icon-color: #7AB0D1;
$sidebar-link-color: #D1E1EF;
$sidebar-hover-link-color: #fff;
$sidebar-hover-background-color: #0a68a3;

$sidebar-popup-search-input-background-color: #E3ECF2;
$sidebar-popup-search-input-text-color: #7f8fa4;
$sidebar-popup-search-input-placeholder-color: #bdcbde;

$sidebar-popup-background-color: #f7f8fa;
$sidebar-popup-text-color: #7f8fa4;
$sidebar-popup-overlay-color: #000;

$sidebar-popup-link-text-color: #7f8fa4;
$sidebar-popup-hover-link-color: #fff;
$sidebar-popup-hover-link-background-color: #1cacfc;

/*
 * Top
 */

$top-height: 32px;

$top-text-color: #8B9EAB;
$top-separator-color: #C6D8E4;
$top-link-color: #C6D8E4;
$top-hover-link-color: #1cacfc;
$top-border-color: #dce0e6;
$top-icon-color: $link-color;

$top-dropdown-background-color: #2f90cc;
$top-dropdown-text-color: #fff;
$top-dropdown-contrast-background-color: #2980B9;
$top-dropdown-contrast-text-color: #7BBCE5;
$top-dropdown-border-color: #1f81c2;
$top-dropdown-link-color: #fff;
$top-dropdown-hover-link-color: #fff;
$top-dropdown-icon-color: #81c5f0;
$top-dropdown-selected-color: #EFEDC8;

/*
 * Content
 */

$content-background-color: #fff;
$content-contrast-background-color: #F8FAFC; //inline list bg
$content-contrast2-background-color: #E3ECF2; //table header
$content-contrast3-background-color: #EEF3F7; //dashboard, delete collapsable
$content-selected-background-color: #FFFDDB;
$content-contrast2-text-color: $text-color;
$content-border-color: #f1f2f4; //row bottom
$content-border2-color: #D5E3EC; //table bottom
$content-selected-border-color: #EFEDC8;

$tab-selected-border-color: #1cacfc;
$tab-error-border-color: #c7254e;

/*
 * Buttons
 */

$button-background-color: #E3ECF2;
$button-hover-background-color: #1cacfc;
$button-active-background-color: $contrast-color;
$button-text-color: #7f8fa4;
$button-hover-text-color: #fff;
$button-active-text-color: #fff;

$primary-button-background-color: $contrast-color;
$primary-button-text-color: #fff;

$danger-button-background-color: #c7254e;
$danger-button-text-color: #fff;

$background-button-background-color: #fff;
$background-button-text-color: #7f8fa4;

/*
 * Inputs
 */

$input-background-color: #fff;
$input-contrast-background-color: #E3ECF2;
$input-border-color: #EDEDED;
$input-hover-background-color: #1cacfc;
$input-icon-color: $link-color;
$input-text-color: $text-color;
$input-contrast-text-color: #7f8fa4;
$input-hover-text-color: #fff;
$input-selected-text-color: $contrast-color;
$input-disabled-text-color: $dim-text-color;
$input-placeholder-color: #999;
$input-shadow-color: transparentize($contrast-color, 0.25);

$background-input-background-color: #fff;
$background-input-border-color: #fff;
$background-input-text-color: $text-color;

/*
 * Messages
 */

$warning-color: #e75e40;
$warning-text-color: #fff;
$info-color: #FCA326;
$info-text-color: #fff;
$success-color: #2d9fd8;
$success-text-color: #fff;

/*
 * Login
 */

$login-background-color: $sidebar-background-color;
$login-title-text-color: #aaddcc;
$login-title-contrast-text-color: #fff;
$login-header-background-color: $content-contrast2-background-color;
$login-header-text-color: $content-contrast2-text-color;
$login-content-background-color: #fff;

/*
 * jQuery UI
 */

$jquery-ui-buttonpane-background: $content-contrast2-background-color;

$jquery-ui-state-default-background-color: $input-background-color;
$jquery-ui-state-default-border-color: $input-border-color;
$jquery-ui-state-default-text-color: $input-text-color;

$jquery-ui-state-hover-background-color: $button-hover-background-color;
$jquery-ui-state-hover-border-color: $button-hover-background-color;
$jquery-ui-state-hover-text-color: $button-hover-text-color;

$jquery-ui-state-active-background-color: $button-active-background-color;
$jquery-ui-state-active-border-color: $button-active-background-color;
$jquery-ui-state-active-text-color: $button-active-text-color;

$jquery-ui-state-highlight-background-color: $input-background-color;
$jquery-ui-state-highlight-border-color: $hover-link-color;
$jquery-ui-state-highlight-text-color: $hover-link-color;

$jquery-ui-overlay-color: #000;

$jquery-ui-tooltip-background-color: #000;
$jquery-ui-tooltip-text-color: #fff;

/*
 * Charts
 */

$chart-fillColor: transparentize($hover-link-color, 0.75);
$chart-strokeColor: $hover-link-color;
$chart-pointColor: #fff;
$chart-pointHighlightFill: $hover-link-color;
$chart-scaleGridLineColor: transparentize(#000, 0.9);
$chart-scaleLineColor: transparentize(#000, 0.9);
$chart-scaleFontColor: $content-contrast2-text-color;
