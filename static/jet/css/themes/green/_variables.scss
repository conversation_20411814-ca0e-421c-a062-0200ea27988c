/*
 * Customizable variables
 * Update these variable to create theme
 */

/*
 * General
 */

$background-color: #eff6f5;
$text-color: #62a29c;
$dim-text-color: #cceae4;
$error-text-color: #c14747;

$link-color: #7FB1DC;
$hover-link-color: #44b78b;

$font: Arial, sans-serif;
$font-size: 14px;

/*
 * Sidebar
 */

$sidebar-width: 250px;

$sidebar-background-color: #2e5955;
$sidebar-contrast-background-color: #254d49;
$sidebar-contrast-text-color: #62a29c;

$sidebar-arrow-color: #7FB1DC;
$sidebar-hover-arrow-color: #7FB1DC;

$sidebar-action-color: #44b78b;
$sidebar-hover-action-color: #7FB1DC;

$sidebar-title-action-color: #44b78b;
$sidebar-hover-title-action-item-color: #7FB1DC;

$sidebar-text-color: #62a29c;
$sidebar-icon-color: #62a29c;
$sidebar-link-color: #bbddd9;
$sidebar-hover-link-color: #fff;
$sidebar-hover-background-color: #254d49;

$sidebar-popup-search-input-background-color: #cceae4;
$sidebar-popup-search-input-text-color: #62a29c;
$sidebar-popup-search-input-placeholder-color: transparentize(#6f7e95, 0.5);

$sidebar-popup-background-color: #eff6f5;
$sidebar-popup-text-color: #62a29c;
$sidebar-popup-overlay-color: #000;

$sidebar-popup-link-text-color: #62a29c;
$sidebar-popup-hover-link-color: #fff;
$sidebar-popup-hover-link-background-color: #7FB1DC;

/*
 * Top
 */

$top-height: 32px;

$top-text-color: #62a29c;
$top-separator-color: #B4DCD4;
$top-link-color: #B4DCD4;
$top-hover-link-color: #7FB1DC;
$top-border-color: #B4DCD4;
$top-icon-color: #44b78b;

$top-dropdown-background-color: #62a29c;
$top-dropdown-text-color: #eff6f5;
$top-dropdown-contrast-background-color: #3c706b;
$top-dropdown-contrast-text-color: #bbddd9;
$top-dropdown-border-color: #6aa6a1;
$top-dropdown-link-color: #eff6f5;
$top-dropdown-hover-link-color: #eff6f5;
$top-dropdown-icon-color: #eff6f5;
$top-dropdown-selected-color: #e5e2a5;

/*
 * Content
 */

$content-background-color: #fff;
$content-contrast-background-color: #f5fdfa; //inline list bg
$content-contrast2-background-color: #3c706b; //table header
$content-contrast3-background-color: #cceae4; //delete collapsable
$content-selected-background-color: #fffcc0;
$content-contrast2-text-color: #fff;
$content-border-color: #f5f3f4; //row bottom
$content-border2-color: #cceae4; //table bottom
$content-selected-border-color: #e5e2a5;

$tab-selected-border-color: #7FB1DC;
$tab-error-border-color: #c14747;

/*
 * Buttons
 */

$button-background-color: #cceae4;
$button-hover-background-color: #7FB1DC;
$button-active-background-color: #62a29c;
$button-text-color: #62a29c;
$button-hover-text-color: #fff;
$button-active-text-color: #fff;

$primary-button-background-color: #44b78b;
$primary-button-text-color: #fff;

$danger-button-background-color: #c14747;
$danger-button-text-color: #fff;

$background-button-background-color: #fff;
$background-button-text-color: #62a29c;

/*
 * Inputs
 */

$input-background-color: #fff;
$input-contrast-background-color: #cceae4;
$input-border-color: #eff6f5;
$input-hover-background-color: #7FB1DC;
$input-icon-color: #44b78b;
$input-text-color: #62a29c;
$input-contrast-text-color: #62a29c;
$input-hover-text-color: #fff;
$input-selected-text-color: #44b78b;
$input-disabled-text-color: #cceae4;
$input-placeholder-color: #cceae4;
$input-shadow-color: transparentize(#44b78b, 0.25);

$background-input-background-color: #fff;
$background-input-border-color: #fff;
$background-input-text-color: #62a29c;

/*
 * Messages
 */

$warning-color: #f0dada;
$warning-text-color: #dba4a4;
$info-color: #e8e8bd;
$info-text-color: #bebe92;
$success-color: #e0eec5;
$success-text-color: #bcd386;

/*
 * Login
 */

$login-background-color: #2e5955;
$login-title-text-color: #62a29c;
$login-title-contrast-text-color: #fff;
$login-header-background-color: #3c706b;
$login-header-text-color: #fff;
$login-content-background-color: #fff;

/*
 * jQuery UI
 */

$jquery-ui-buttonpane-background: #eff6f5;

$jquery-ui-state-default-background-color: #fff;
$jquery-ui-state-default-border-color: #eff6f5;
$jquery-ui-state-default-text-color: #62a29c;

$jquery-ui-state-hover-background-color: #7FB1DC;
$jquery-ui-state-hover-border-color: #7FB1DC;
$jquery-ui-state-hover-text-color: #fff;

$jquery-ui-state-active-background-color: #44b78b;
$jquery-ui-state-active-border-color: #44b78b;
$jquery-ui-state-active-text-color: #fff;

$jquery-ui-state-highlight-background-color: #fff;
$jquery-ui-state-highlight-border-color: #7FB1DC;
$jquery-ui-state-highlight-text-color: #7FB1DC;

$jquery-ui-overlay-color: #000;

$jquery-ui-tooltip-background-color: #000;
$jquery-ui-tooltip-text-color: #fff;

/*
 * Charts
 */

$chart-fillColor: transparentize($hover-link-color, 0.75);
$chart-strokeColor: $hover-link-color;
$chart-pointColor: $content-contrast2-text-color;
$chart-pointHighlightFill: $hover-link-color;
$chart-scaleGridLineColor: transparentize(#000, 0.9);
$chart-scaleLineColor: transparentize(#000, 0.9);
$chart-scaleFontColor: $content-contrast2-text-color;
