@import "globals";

/* BREADCRUMBS */

div.breadcrumbs {
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  line-height: $top-height;
  color: $top-text-color;
  padding: 10px 175px + 20px + 20px 10px 20px;
  visibility: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  min-height: 32px;

  @include for-mobile {
    padding: 20px 20px 10px 20px;
    white-space: normal;
    text-overflow: clip;
    overflow: auto;
  }

  @include for-phone {
    padding: 10px;
  }

  &.initialized {
    visibility: inherit;
  }

  a {
    &, &:visited {
      color: $top-link-color;
    }

    &:focus, &:hover {
      color: $top-hover-link-color;
    }
  }
}

.breadcrumbs {
  &-separator {
    color: $top-separator-color;
    margin: 0 6px 0 6px;
    font-weight: bold !important;
    font-size: 15px;
    vertical-align: middle;
  }
}
