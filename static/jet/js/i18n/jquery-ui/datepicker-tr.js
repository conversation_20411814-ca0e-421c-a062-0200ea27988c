/* Turkish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> <PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['tr'] = {
	closeText: 'kapat',
	prevText: '&#x3C;geri',
	nextText: 'ileri&#x3e',
	currentText: 'bugün',
	monthNames: ['<PERSON>ca<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
	'<PERSON><PERSON>uz','<PERSON><PERSON>ust<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','Kasım','Aralık'],
	monthNamesShort: ['Oca','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','May','<PERSON><PERSON>',
	'<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>'],
	dayNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON>arşam<PERSON>','<PERSON>şem<PERSON>','<PERSON>uma','Cumartesi'],
	dayNamesShort: ['Pz','Pt','Sa','Ça','Pe','Cu','Ct'],
	dayNamesMin: ['Pz','Pt','Sa','Ça','Pe','Cu','Ct'],
	weekHeader: 'Hf',
	dateFormat: 'dd.mm.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['tr']);

return datepicker.regional['tr'];

}));
