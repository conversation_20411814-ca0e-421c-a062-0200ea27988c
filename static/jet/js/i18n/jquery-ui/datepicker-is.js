/* Icelandic initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> <PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['is'] = {
	closeText: 'Loka',
	prevText: '&#x3C; Fyrri',
	nextText: 'Næsti &#x3E;',
	currentText: 'Í dag',
	monthNames: ['<PERSON><PERSON><PERSON>','Febr<PERSON><PERSON>','<PERSON>','<PERSON>íl','<PERSON><PERSON>','<PERSON><PERSON><PERSON>',
	'<PERSON><PERSON><PERSON>','Ágúst','September','Október','Nóvember','Desember'],
	monthNamesShort: ['Jan','Feb','Mar','Apr','<PERSON><PERSON>','<PERSON><PERSON>',
	'<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>ó<PERSON>','<PERSON>'],
	dayNames: ['<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>ju<PERSON>gur','<PERSON><PERSON><PERSON><PERSON>gur','<PERSON>mmtudagur','Föstudagur','Laugardagur'],
	dayNamesShort: ['Sun','Mán','Þri','Mið','Fim','Fös','Lau'],
	dayNamesMin: ['Su','Má','Þr','Mi','Fi','Fö','La'],
	weekHeader: 'Vika',
	dateFormat: 'dd.mm.yy',
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['is']);

return datepicker.regional['is'];

}));
