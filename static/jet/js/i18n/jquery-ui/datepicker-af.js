/* Afrikaans initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON>. */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['af'] = {
	closeText: 'Selekteer',
	prevText: 'Vorige',
	nextText: 'Volgende',
	currentText: 'Vandag',
	monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','April','<PERSON>','<PERSON><PERSON>',
	'<PERSON>','<PERSON>','September','Okto<PERSON>','November','<PERSON><PERSON><PERSON>'],
	monthNamesShort: ['Jan', 'Feb', 'Mrt', 'Apr', 'Mei', 'Jun',
	'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Des'],
	dayNames: ['Sondag', 'Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', '<PERSON>rydag', 'Saterdag'],
	dayNamesShort: ['Son', 'Ma<PERSON>', '<PERSON>', 'Woe', 'Don', 'Vry', 'Sat'],
	dayNamesMin: ['So','Ma','Di','Wo','Do','Vr','Sa'],
	weekHeader: 'Wk',
	dateFormat: 'dd/mm/yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['af']);

return datepicker.regional['af'];

}));
