/* Indonesian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['id'] = {
	closeText: 'Tutup',
	prevText: '&#x3C;mundur',
	nextText: 'maju&#x3E;',
	currentText: 'hari ini',
	monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','April','<PERSON>','<PERSON><PERSON>',
	'<PERSON><PERSON>','<PERSON><PERSON><PERSON>','September','Oktober','Nopember','Desember'],
	monthNamesShort: ['Jan','Feb','Mar','Apr','<PERSON>','Jun',
	'Jul','<PERSON><PERSON>','Sep','Okt','Nop','<PERSON>'],
	dayNames: ['<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Sabtu'],
	dayNamesShort: ['<PERSON>','<PERSON>','Sel','Rab','kam','Jum','Sab'],
	dayNamesMin: ['Mg','Sn','Sl','Rb','Km','jm','Sb'],
	weekHeader: 'Mg',
	dateFormat: 'dd/mm/yy',
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['id']);

return datepicker.regional['id'];

}));
