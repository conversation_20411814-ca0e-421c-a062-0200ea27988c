/* Estonian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> (mrts.pydev at gmail com). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['et'] = {
	closeText: 'Sulge',
	prevText: 'Eelnev',
	nextText: '<PERSON><PERSON>rgnev',
	currentText: 'Täna',
	monthNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','April<PERSON>','<PERSON>','<PERSON><PERSON>',
	'<PERSON><PERSON>','August','September','Oktoober','November','Detsember'],
	monthNamesShort: ['Jaan', 'Veebr', '<PERSON><PERSON><PERSON>', 'Apr', '<PERSON>', '<PERSON><PERSON>',
	'<PERSON><PERSON>', 'Aug', 'Sept', 'Okt', 'Nov', '<PERSON><PERSON>'],
	dayNames: ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>päev'],
	dayNamesShort: ['Pühap', 'Esmasp', 'Teisip', 'Kolmap', 'Neljap', 'Reede', 'Laup'],
	dayNamesMin: ['P','E','T','K','N','R','L'],
	weekHeader: 'näd',
	dateFormat: 'dd.mm.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['et']);

return datepicker.regional['et'];

}));
