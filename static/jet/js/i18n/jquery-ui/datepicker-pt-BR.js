/* Brazilian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['pt-BR'] = {
	closeText: 'Fechar',
	prevText: '&#x3C;Anterior',
	nextText: 'Próximo&#x3E;',
	currentText: 'Hoje',
	monthNames: ['Janeiro','Fever<PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
	'<PERSON><PERSON>','A<PERSON>to','Setembro','Outubro','Novembro','Dezembro'],
	monthNamesShort: ['Jan','Fev','<PERSON>','Abr','<PERSON>','<PERSON>',
	'<PERSON>','<PERSON><PERSON>','<PERSON>','Out','Nov','De<PERSON>'],
	dayNames: ['<PERSON>','<PERSON>-feira','<PERSON><PERSON><PERSON>-feira','<PERSON>ua<PERSON>-feira','<PERSON>uinta-feira','<PERSON>ta-feira','Sábado'],
	dayNamesShort: ['Dom','Seg','Ter','Qua','Qui','Sex','Sáb'],
	dayNamesMin: ['Dom','Seg','Ter','Qua','Qui','Sex','Sáb'],
	weekHeader: 'Sm',
	dateFormat: 'dd/mm/yy',
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['pt-BR']);

return datepicker.regional['pt-BR'];

}));
