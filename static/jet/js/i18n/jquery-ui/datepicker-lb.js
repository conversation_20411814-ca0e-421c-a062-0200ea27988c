/* Luxembourgish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> <<EMAIL>> */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['lb'] = {
	closeText: 'Fäerdeg',
	prevText: '<PERSON><PERSON><PERSON>',
	nextText: 'Weider',
	currentText: 'Haut',
	monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
	'Juli','August','September','Okto<PERSON>','November','Dezember'],
	monthNamesShort: ['Jan', 'Feb', 'Mäe', 'Abr', 'Mee', 'Jun',
	'Jul', 'Aug', 'Sep', 'Okt', 'Nov', '<PERSON><PERSON>'],
	dayNames: ['Sonndeg', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>chdeg', '<PERSON><PERSON>tt<PERSON><PERSON>', '<PERSON><PERSON><PERSON>de<PERSON>', 'Freideg', 'Samschdeg'],
	dayNamesShort: ['Son', 'Méi', 'Dën', 'Mët', 'Don', 'Fre', 'Sam'],
	dayNamesMin: ['So','Mé','Dë','Më','Do','Fr','Sa'],
	weekHeader: 'W',
	dateFormat: 'dd.mm.yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['lb']);

return datepicker.regional['lb'];

}));
