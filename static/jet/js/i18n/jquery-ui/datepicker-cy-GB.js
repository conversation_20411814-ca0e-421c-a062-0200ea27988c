/* Welsh/UK initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON>. */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['cy-GB'] = {
	closeText: 'Done',
	prevText: 'Prev',
	nextText: 'Next',
	currentText: 'Today',
	monthNames: ['<PERSON><PERSON>r','Chwefror','Maw<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON>he<PERSON>',
	'Go<PERSON><PERSON>naf','Awst','Me<PERSON>','<PERSON>yd<PERSON><PERSON>','<PERSON>chwedd','Rhagfyr'],
	monthNamesShort: ['Ion', 'Chw', 'Maw', 'Ebr', 'Mai', 'Meh',
	'Gor', 'A<PERSON>', 'Med', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>ha'],
	dayNames: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>wrn'],
	dayNamesShort: ['Sul', 'Llu', 'Maw', 'Mer', 'Iau', 'Gwe', 'Sad'],
	dayNamesMin: ['Su','Ll','Ma','Me','Ia','Gw','Sa'],
	weekHeader: 'Wy',
	dateFormat: 'dd/mm/yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['cy-GB']);

return datepicker.regional['cy-GB'];

}));
