/* Croatian i18n for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON><PERSON>. */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define([ "../datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}(function( datepicker ) {

datepicker.regional['hr'] = {
	closeText: 'Zatvori',
	prevText: '&#x3C;',
	nextText: '&#x3E;',
	currentText: '<PERSON><PERSON>',
	monthNames: ['<PERSON><PERSON><PERSON>anj','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>ravanj','<PERSON>vibanj','<PERSON>panj',
	'<PERSON>panj','<PERSON><PERSON><PERSON>','<PERSON>u<PERSON>','Listopad','Studeni','Prosinac'],
	monthNamesShort: ['Sij','Velj','<PERSON><PERSON><PERSON>','<PERSON>ra','<PERSON><PERSON>','<PERSON><PERSON>',
	'<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>'],
	dayNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON>ned<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','Četvrta<PERSON>','<PERSON>ak','Subota'],
	dayNamesShort: ['Ned','Pon','Uto','Sri','Čet','Pet','Sub'],
	dayNamesMin: ['Ne','Po','Ut','Sr','Če','Pe','Su'],
	weekHeader: 'Tje',
	dateFormat: 'dd.mm.yy.',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
datepicker.setDefaults(datepicker.regional['hr']);

return datepicker.regional['hr'];

}));
