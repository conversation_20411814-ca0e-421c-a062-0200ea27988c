!function t(e,i,n){function o(r,a){if(!i[r]){if(!e[r]){var l="function"==typeof require&&require;if(!a&&l)return l(r,!0);if(s)return s(r,!0);var c=new Error("Cannot find module '"+r+"'");throw c.code="MODULE_NOT_FOUND",c}var u=i[r]={exports:{}};e[r][0].call(u.exports,function(t){var i=e[r][1][t];return o(i?i:t)},u,u.exports,t,e,i,n)}return i[r].exports}for(var s="function"==typeof require&&require,r=0;r<n.length;r++)o(n[r]);return o}({1:[function(t,e,i){var n=t("jquery"),o=function(t){this.$changeform=t};o.prototype={getContentWrappers:function(){var t=this.$changeform.find("#content-main > form > div"),e=t.find("> .module"),i=t.find("> .inline-group");return n().add(e).add(i)},getHashSelector:function(t){if(void 0==t)return null;var e=t.match(/^(#(\/tab\/(.+)\/)?)?$/i);return null==e?null:void 0!=e[3]?e[3]:""},showTab:function(t,e){var i=this.$changeform.find(".changeform-tabs-item"),n=this.getContentWrappers(),o=this.getHashSelector(t);if(e||null!=o){null!=o&&0!=o.length||(o=this.getHashSelector(i.first().find(".changeform-tabs-item-link").attr("href")));var s=n.filter("."+o),r=i.find('.changeform-tabs-item-link[href="#/tab/'+o+'/"]').closest(".changeform-tabs-item");i.removeClass("selected"),r.addClass("selected"),n.removeClass("selected"),s.addClass("selected")}},initTabs:function(){var t=this;n(window).on("hashchange",function(){t.showTab(location.hash,!1)}),this.showTab(location.hash,!0)},updateErrorState:function(){var t=this.$changeform.find(".changeform-tabs-item"),e=this.getContentWrappers(),i=this;t.each(function(){var t=n(this),o=i.getHashSelector(t.find(".changeform-tabs-item-link").attr("href"));if(o){var s=e.filter("."+o);s.find(".form-row.errors").length&&t.addClass("errors")}})},run:function(){try{this.initTabs(),this.updateErrorState()}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){n(".change-form").each(function(){new o(n(this)).run()})})},{jquery:69}],2:[function(t,e,i){var n=t("jquery"),o=t("../utils/translate"),s=function(t){this.$changeForm=t};s.prototype={changeDetected:!1,onWindowBeforeUnload:function(){return o("Warning: you have unsaved changes")},onFormInputChanged:function(t){t.off("change",this.onFormInputChanged),self.changeDetected||n(window).bind("beforeunload",this.onWindowBeforeUnload),this.changeDetected=!0},initUnsavedChangesWarning:function(t){var e=this,i=t.find("#content-main form");if(0!=i.length){var o=i.find("input, textarea, select");n(document).on("submit","form",function(){n(window).off("beforeunload",e.onWindowBeforeUnload)}),o.on("change",n.proxy(this.onFormInputChanged,this,o))}},run:function(){try{this.initUnsavedChangesWarning(this.$changeForm)}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){n(".change-form").each(function(){new s(n(this)).run()})})},{"../utils/translate":37,jquery:69}],3:[function(t,e,i){var n=t("jquery"),o=function(t){this.$changelist=t};o.prototype={updateFixedHeaderVisibility:function(t,e){var i=n(window).scrollTop()>e.offset().top;t.closest("table").toggle(i)},updateFixedHeaderWidth:function(t,e){var i=e.find("th"),o=t.find("th");i.each(function(t){o.eq(t).css("width",n(this).width())})},initFixedHeader:function(t){var e=t.find("#result_list thead");if(0!=e.length){var i=e.clone(),o=n("<table>").addClass("helper").append(i);o.find(".action-checkbox-column").empty(),o.appendTo(document.body),n(window).on("scroll",n.proxy(this.updateFixedHeaderVisibility,this,o,e)),n(window).on("resize",n.proxy(this.updateFixedHeaderWidth,this,i,e)),this.updateFixedHeaderWidth(i,e)}},updateFixedFooter:function(t,e){if(n(window).scrollTop()+n(window).height()<t.offset().top+t.outerHeight(!1)+e.innerHeight()){if(!e.hasClass("fixed")){var i=n(window).scrollTop();e.addClass("fixed"),t.css("margin-bottom",e.innerHeight()+"px"),n(window).scrollTop(i)}}else e.hasClass("fixed")&&(e.removeClass("fixed"),t.css("margin-bottom",0))},initFixedFooter:function(t){var e=t.find(".changelist-footer"),i=e.siblings(".results");0!=e.length&&0!=i.length&&(n(window).on("scroll",n.proxy(this.updateFixedFooter,this,i,e)),n(window).on("resize",n.proxy(this.updateFixedFooter,this,i,e)),this.updateFixedFooter(i,e))},initHeaderSortableSelection:function(){n("table thead .sortable").on("click",function(t){if(t.target==this){var e=n(this).find(".text a").get(0);void 0!=e&&e.click()}})},initRowSelection:function(t){t.find("#result_list tbody th, #result_list tbody td").on("click",function(t){t.target==this&&n(this).closest("tr").find(".action-checkbox .action-select").click()})},run:function(){var t=this.$changelist;try{this.initFixedHeader(t),this.initFixedFooter(t),this.initHeaderSortableSelection(t),this.initRowSelection(t)}catch(e){console.error(e,e.stack)}this.$changelist.addClass("initialized")}},n(document).ready(function(){n("#changelist").each(function(){new o(n(this)).run()})})},{jquery:69}],4:[function(t,e,i){var n=t("jquery"),o=function(){};o.prototype={uniqueCheckboxIdCounter:0,uniqueCheckboxIdPrefix:"unique_checkbox_id_",addLabelToCheckbox:function(t){var e=t.attr("id")?t.attr("id"):this.uniqueCheckboxIdPrefix+this.uniqueCheckboxIdCounter++;t.attr("id",e),n("<label>").attr("for",e).insertAfter(t)},addLabelToCheckboxes:function(){var t=this;n('input[type="checkbox"]').each(function(){var e=n(this);void 0!=e.attr("id")&&0!=n('label[for="'+e.attr("id")+'"]').length||t.addLabelToCheckbox(e)})},run:function(){try{this.addLabelToCheckboxes()}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){(new o).run()})},{jquery:69}],5:[function(t,e,i){var n=t("jquery"),o=function(t){this.$inline=t,this.prefix=t.data("inline-prefix"),this.verboseName=t.data("inline-verbose-name"),this.deleteText=t.data("inline-delete-text")};o.prototype={updateLabels:function(t){var e=this,i=t.find(".inline-navigation-item");t.find(".inline-related").each(function(t){var o=n(this),s=o.find(".inline_label"),r=s.html().replace(/(#\d+)/g,"#"+(t+1)),a=i.eq(t),l=o.hasClass("has_original")?r:e.verboseName+" "+r;s.html(r),a.html(l)})},updateFormIndex:function(t,e){var i=new RegExp("("+this.prefix+"-(\\d+|__prefix__))"),o=this.prefix+"-"+e;t.find("*").each(function(){var t=n(this);n.each(["for","id","name"],function(){var e=this;t.attr(e)&&t.attr(e,t.attr(e).replace(i,o))})}),t.hasClass("empty-form")||t.attr("id",this.prefix+"-"+e)},updateFormsIndexes:function(t){var e=this,i=t.find(".inline-navigation-item");t.find(".inline-related").each(function(t){var o=n(this);e.updateFormIndex(o,t),i.eq(t).attr("data-inline-related-id",o.attr("id"))})},updateTotalForms:function(t){var e=t.find('[name="'+this.prefix+'-TOTAL_FORMS"]'),i=t.find('[name="'+this.prefix+'-MAX_NUM_FORMS"]'),n=parseInt(t.find(".inline-related").length),o=i.val()?parseInt(i.val()):1/0;e.val(n),t.find(".add-row").toggle(o>=n)},addNavigationItem:function(t,e){var i=t.find(".inline-navigation-item.empty");return i.clone().removeClass("empty").attr("data-inline-related-id",e.attr("id")).insertBefore(i)},openNavigationItem:function(t,e){t.find(".inline-related").removeClass("selected").filter("#"+e.attr("data-inline-related-id")).addClass("selected"),t.find(".inline-navigation-item").removeClass("selected"),e.addClass("selected")},removeItem:function(t,e){e.remove(),t.find('.inline-navigation-item[data-inline-related-id="'+e.attr("id")+'"]').remove()},openFirstNavigationItem:function(t){var e=t.find(".inline-navigation-item:not(.empty)").first();void 0!=e&&(this.openNavigationItem(t,e),this.scrollNavigationToTop(t))},addItemDeleteButton:function(t){t.children(":first").append('<span><a class="inline-deletelink" href="#">'+this.deleteText+"</a></span>")},scrollNavigationToTop:function(t){var e=t.find(".inline-navigation-content");e.stop().animate({scrollTop:0})},scrollNavigationToBottom:function(t){var e=t.find(".inline-navigation-content");e.stop().animate({scrollTop:e.prop("scrollHeight")})},initAdding:function(t){var e=this;t.find(".add-row a").on("click",function(i){i.preventDefault();var n=t.find(".inline-related.empty-form"),o=parseInt(t.find(".inline-related").length)-1,s=n.clone(!0).removeClass("empty-form").insertBefore(n);e.updateTotalForms(t),e.updateFormIndex(s,o),e.updateFormIndex(n,o+1);var r=e.addNavigationItem(t,s);e.updateLabels(t),e.openNavigationItem(t,r),e.addItemDeleteButton(s),e.scrollNavigationToBottom(t)})},initDeletion:function(t){var e=this;t.on("click",".inline-deletelink",function(i){i.preventDefault();var o=n(this).closest(".inline-related");e.removeItem(t,o),e.updateFormsIndexes(t),e.updateLabels(t),e.updateTotalForms(t),e.openFirstNavigationItem(t)}),t.find(".inline-related").each(function(){var e=n(this);e.find(".delete input").on("change",function(){t.find('.inline-navigation-item[data-inline-related-id="'+e.attr("id")+'"]').toggleClass("delete",n(this).is(":checked"))})})},initNavigation:function(t){var e=this;t.on("click",".inline-navigation-item",function(i){i.preventDefault(),e.openNavigationItem(t,n(this))}),e.openFirstNavigationItem(t)},run:function(){var t=this.$inline;try{this.initAdding(t),this.initDeletion(t),this.initNavigation(t)}catch(e){console.error(e,e.stack)}}},e.exports=o},{jquery:69}],6:[function(t,e,i){t("./../utils/jquery-slidefade");var n=t("jquery"),o=t("../utils/translate");t("jquery-ui/ui/core"),t("jquery-ui/ui/widget"),t("jquery-ui/ui/mouse"),t("jquery-ui/ui/draggable"),t("jquery-ui/ui/droppable"),t("jquery-ui/ui/sortable"),t("jquery-ui/ui/resizable"),t("jquery-ui/ui/button"),t("jquery-ui/ui/dialog");var s=function(t){this.$dashboard=t};s.prototype={initTools:function(t){t.find(".dashboard-tools-toggle").on("click",function(e){e.preventDefault(),t.find(".dashboard-tools").toggleClass("visible")});var e=t.find("#add-dashboard-module-form");e.find(".add-dashboard-link").on("click",function(t){var i=e.find('[name="type"]'),o=e.find('[name="module"] option:selected').data("type");o&&(i.val(o),n.ajax({url:e.attr("action"),method:e.attr("method"),dataType:"json",data:e.serialize(),success:function(t){t.error||(document.location=t.success_url)}})),t.preventDefault()}),t.find(".reset-dashboard-link").on("click",function(e){var i={},s=function(){var e=t.find("#reset-dashboard-form");n.ajax({url:e.attr("action"),method:e.attr("method"),dataType:"json",data:e.serialize(),success:function(t){t.error||location.reload()}})};i[o("Yes")]=function(){s(),n(this).dialog("close")},i[o("Cancel")]=function(){n(this).dialog("close")},t.find("#reset-dashboard-dialog").dialog({resizable:!1,modal:!0,buttons:i}),e.preventDefault()})},updateDashboardModules:function(t){var e=t.find("#update-dashboard-modules-form"),i=[];t.find(".dashboard-column").each(function(){var t=n(this),e=t.closest(".dashboard-column-wrapper").index();t.find(".dashboard-item").each(function(){var t=n(this),o=t.index(),s=t.data("module-id");i.push({id:s,column:e,order:o})})}),e.find('[name="modules"]').val(JSON.stringify(i)),n.ajax({url:e.attr("action"),method:e.attr("method"),dataType:"json",data:e.serialize()})},initModulesDragAndDrop:function(t){var e=this;t.find(".dashboard-column").droppable({activeClass:"active",hoverClass:"hovered",tolerance:"pointer",accept:".dashboard-item"}).sortable({items:".dashboard-item.draggable",handle:".dashboard-item-header",tolerance:"pointer",connectWith:".dashboard-column",cursor:"move",placeholder:"dashboard-item placeholder",forcePlaceholderSize:!0,update:function(i,n){e.updateDashboardModules(t)}})},initCollapsibleModules:function(t){var e=t.find("#update-dashboard-module-collapse-form");t.find(".dashboard-item.collapsible").each(function(){var t=n(this),i=t.find(".dashboard-item-collapse"),o=t.find(".dashboard-item-content"),s=t.data("module-id");i.on("click",function(i){i.preventDefault(),o.slideFadeToggle(200,"swing",function(){var i=0==o.is(":visible");i?t.addClass("collapsed"):t.removeClass("collapsed"),e.find('[name="id"]').val(s),e.find('[name="collapsed"]').val(i?"true":"false"),n.ajax({url:e.attr("action"),method:e.attr("method"),dataType:"json",data:e.serialize()})})})})},initDeletableModules:function(t){var e=t.find("#remove-dashboard-module-form");t.find(".dashboard-item.deletable").each(function(){var i=n(this),s=i.find(".dashboard-item-remove"),r=i.data("module-id");s.on("click",function(s){s.preventDefault();var a={},l=function(){i.fadeOut(200,"swing",function(){e.find('[name="id"]').val(r),n.ajax({url:e.attr("action"),method:e.attr("method"),dataType:"json",data:e.serialize()})})};a[o("Delete")]=function(){l(),n(this).dialog("close")},a[o("Cancel")]=function(){n(this).dialog("close")},t.find("#module-remove-dialog").dialog({resizable:!1,modal:!0,buttons:a})})})},initAjaxModules:function(t){t.find(".dashboard-item.ajax").each(function(){var t=n(this),e=t.find(".dashboard-item-content"),i=t.data("ajax-url");n.ajax({url:i,dataType:"json",success:function(t){if(t.error)return void e.empty();var i=e.height();e.html(t.html);var n=e.height();e.height(i),e.animate({height:n},250,"swing",function(){e.height("auto")})},error:function(){e.empty()}})})},updateModuleChildrenFormsetLabels:function(t){t.find(".inline-related").each(function(t){n(this).find(".inline_label").text("#"+(t+1))})},updateModuleChildrenFormsetFormIndex:function(t,e){var i="children",o=new RegExp("("+i+"-(\\d+|__prefix__))"),s=i+"-"+e;t.find("fieldset.module *").each(function(){var t=n(this);n.each(["for","id","name"],function(){var e=this;t.attr(e)&&t.attr(e,t.attr(e).replace(o,s))})})},updateModuleChildrenFormsetFormsIndexes:function(t){var e=this,i=parseInt(t.find(".inline-related.has_original").length);t.find(".inline-related.last-related").each(function(t){e.updateModuleChildrenFormsetFormIndex(n(this),i+t)})},updateModuleChildrenFormsetTotalForms:function(t){var e=t.find('[name="children-TOTAL_FORMS"]'),i=parseInt(t.find(".inline-related").length);e.val(i)},initModuleChildrenFormsetUpdate:function(t){if(t.hasClass("change-form")){var e=this,i=t.find(".inline-group");i.find(".add-row a").on("click",function(t){t.preventDefault();var n=i.find(".inline-related.empty-form"),o=n.clone(!0).removeClass("empty-form").insertBefore(n);e.updateModuleChildrenFormsetLabels(i),e.updateModuleChildrenFormsetFormIndex(n,parseInt(i.find(".inline-related").length)-1),e.updateModuleChildrenFormsetFormIndex(o,parseInt(i.find(".inline-related").length)-2),e.updateModuleChildrenFormsetTotalForms(i)}),i.find(".inline-deletelink").on("click",function(t){t.preventDefault(),n(this).closest(".inline-related").remove(),e.updateModuleChildrenFormsetFormsIndexes(i),e.updateModuleChildrenFormsetLabels(i),e.updateModuleChildrenFormsetTotalForms(i)})}},run:function(){var t=this.$dashboard;try{this.initTools(t),this.initModulesDragAndDrop(t),this.initCollapsibleModules(t),this.initDeletableModules(t),this.initAjaxModules(t),this.initModuleChildrenFormsetUpdate(t)}catch(e){console.error(e,e.stack)}t.addClass("initialized")}},n(document).ready(function(){n(".dashboard.jet").each(function(){new s(n(this)).run()})})},{"../utils/translate":37,"./../utils/jquery-slidefade":36,jquery:69,"jquery-ui/ui/button":55,"jquery-ui/ui/core":56,"jquery-ui/ui/dialog":58,"jquery-ui/ui/draggable":59,"jquery-ui/ui/droppable":60,"jquery-ui/ui/mouse":61,"jquery-ui/ui/resizable":63,"jquery-ui/ui/sortable":64,"jquery-ui/ui/widget":66}],7:[function(t,e,i){var n=t("jquery");t("jquery-ui/ui/core"),t("jquery-ui/ui/datepicker"),t("timepicker");var o=function(){};o.prototype={removeInputTextNode:function(t){if(0!=t.length){var e=t.get(0).previousSibling;3==e.nodeType&&n(e).remove()}},updateDatetimeLayout:function(){var t=this;n(".form-row .datetime").each(function(){var e=n(this),i=e.find(".vDateField"),o=e.find(".vTimeField");t.removeInputTextNode(i),t.removeInputTextNode(o),i.nextAll("br").first().remove()}),n(".form-row .vDateField").each(function(){var t=n(this),e=n("<span>").addClass("icon-calendar");n("<a>").attr("href","#").addClass("vDateField-link").append(e).insertAfter(t)}),n(".form-row .vTimeField").each(function(){var t=n(this),e=n("<span>").addClass("icon-clock");n("<a>").attr("href","#").addClass("vTimeField-link").append(e).insertAfter(t)})},djangoDateTimeFormatToJs:function(t){return t.toLowerCase().replace(/%\w/g,function(t){return t=t.replace(/%/,""),t+t})},initDateWidgets:function(t){t=t||n(document);var e=this;t.find(".form-row .vDateField").each(function(){var t=n(this),i=t.next(".vDateField-link");t.datepicker({dateFormat:e.djangoDateTimeFormatToJs(DATE_FORMAT),showButtonPanel:!0,nextText:"",prevText:""}),i.on("click",function(e){t.datepicker("widget").is(":visible")?t.datepicker("hide"):t.datepicker("show"),e.preventDefault()})});var i=n.datepicker._gotoToday;n.datepicker._gotoToday=function(t){i.call(this,t),this._selectDate(t)}},initTimeWidgets:function(t){t=t||n(document),t.find(".form-row .vTimeField").each(function(){var t=n(this),e=t.next(".vTimeField-link");t.timepicker({showPeriodLabels:!1,showCloseButton:!0,showNowButton:!0}),e.on("click",function(e){t.datepicker("widget").is(":visible")?t.datepicker("hide"):t.timepicker("show"),e.preventDefault()})})},run:function(){try{this.updateDatetimeLayout(),this.initDateWidgets(),this.initTimeWidgets();var t=this;n(".inline-group").on("inline-group-row:added",function(e,i){i.find(".hasDatepicker").removeClass("hasDatepicker"),i.find(".hasTimepicker").removeClass("hasTimepicker"),t.initDateWidgets(i),t.initTimeWidgets(i)})}catch(e){console.error(e,e.stack)}}},n(document).ready(function(){(new o).run()})},{jquery:69,"jquery-ui/ui/core":56,"jquery-ui/ui/datepicker":57,timepicker:92}],8:[function(t,e,i){var n=t("jquery"),o=function(t){this.$toolbar=t};o.prototype={initFiltersInteraction:function(t){t.find(".changelist-filter-select").each(function(){var t=n(this),e=t.attr("multiple");e&&t.data("previous-options",t.find("option:selected")),t.on("change",function(){var t=n(this),i=t.find("option:selected");e&&(t.data("previous-options").length<i.length?i=i.filter(function(e,i){return 0==t.data("previous-options").filter(function(t,e){return e==i}).length}):t.data("previous-options").length>i.length&&(i=t.data("previous-options").filter(function(t,e){return 0==i.filter(function(t,i){return e==i}).length})),t.data("previous-options",t.find("option:selected")));var o=i.data("url"),s=t.data("queryset--lookup");o?document.location=i.data("url"):s&&(document.location="?"+s+"="+i.val())})})},run:function(){try{this.initFiltersInteraction(this.$toolbar)}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){n("#toolbar").each(function(){new o(n(this)).run()})})},{jquery:69}],9:[function(t,e,i){var n=t("jquery"),o=t("./compact-inline"),s=function(t){this.$inline=t};s.prototype={initAddRow:function(t){t.on("click",".add-row a",function(){var e=t.find(".inline-related:not(.empty-form)").last();t.trigger("inline-group-row:added",[e])})},run:function(){var t=this.$inline;try{t.hasClass("compact")&&new o(t).run(),this.initAddRow(t)}catch(e){console.error(e,e.stack)}t.addClass("initialized")}},n(document).ready(function(){n(".inline-group").each(function(){new s(n(this)).run()})})},{"./compact-inline":5,jquery:69}],10:[function(t,e,i){var n=t("jquery"),o=t("../utils/window-storage"),s=function(){this.windowStorage=new o("relatedWindows")};s.prototype={updateLinks:function(t){t.find("~ .change-related, ~ .delete-related, ~ .add-another").each(function(){var e=n(this),i=e.data("href-template");if(void 0!=i){var o=t.val();o?e.attr("href",i.replace("__fk__",o)):e.removeAttr("href")}})},initLinksForRow:function(t){if(!t.data("related-popups-links-initialized")){var e=this;t.find("select").each(function(){var t=n(this);e.updateLinks(t),t.find("~ .add-related, ~ .change-related, ~ .delete-related, ~ .add-another").each(function(){var i=n(this);i.on("click",function(n){n.preventDefault();var o=i.attr("href");void 0!=o&&(o.indexOf("_popup")==-1&&(o+=o.indexOf("?")==-1?"?_popup=1":"&_popup=1"),e.showPopup(t,o))})})}).on("change",function(){e.updateLinks(n(this))}),t.find("input").each(function(){var t=n(this);t.find("~ .related-lookup").each(function(){var i=n(this);i.on("click",function(n){n.preventDefault();var o=i.attr("href");o+=o.indexOf("?")==-1?"?_popup=1":"&_popup=1",e.showPopup(t,o)})})}),t.data("related-popups-links-initialized",!0)}},initLinks:function(){var t=this;n(".form-row").each(function(){t.initLinksForRow(n(this))}),n(".inline-group").on("inline-group-row:added",function(e,i){i.find(".form-row").each(function(){t.initLinksForRow(n(this))})})},initPopupBackButton:function(){var t=this;n(".related-popup-back").on("click",function(e){e.preventDefault(),t.closePopup()})},showPopup:function(t,e){var i=n(window.top.document),o=i.find(".related-popup-container"),s=o.find(".loading-indicator"),r=i.find("body"),a=n("<div>").addClass("related-popup").data("input",t),l=n("<iframe>").attr("src",e).on("load",function(){a.add(i.find(".related-popup-back")).fadeIn(200,"swing",function(){s.hide()})});a.append(l),s.show(),i.find(".related-popup").add(i.find(".related-popup-back")).fadeOut(200,"swing"),o.fadeIn(200,"swing",function(){o.append(a)}),r.addClass("non-scrollable")},closePopup:function(t){var e=this.windowStorage.previous(),i=this;!function(e){var n=e(window.top.document),o=n.find(".related-popup"),s=n.find(".related-popup-container"),r=o.last();void 0!=t&&i.processPopupResponse(r,t),i.windowStorage.pop(),1==o.length?s.fadeOut(200,"swing",function(){n.find(".related-popup-back").hide(),n.find("body").removeClass("non-scrollable"),r.remove()}):o.length>1&&(r.remove(),o.eq(o.length-2).show())}(e?e.jet.jQuery:n)},findPopupResponse:function(){var t=this;n("#django-admin-popup-response-constants").each(function(){var e=n(this),i=e.data("popup-response");t.closePopup(i)})},processPopupResponse:function(t,e){var i=t.data("input");switch(e.action){case"change":i.find("option").each(function(){var t=n(this);t.val()==e.value&&t.html(e.obj).val(e.new_value)}),i.trigger("change").trigger("select:init");break;case"delete":i.find("option").each(function(){var t=n(this);t.val()==e.value&&t.remove()}),i.trigger("change").trigger("select:init");break;default:if(i.is("select")){var o=n("<option>").val(e.value).html(e.obj);i.append(o),o.attr("selected",!0),i.trigger("change").trigger("select:init")}else i.is("input.vManyToManyRawIdAdminField")&&i.val()?i.val(i.val()+","+e.value):i.is("input")&&i.val(e.value)}},overrideRelatedGlobals:function(){var t=this;window.showRelatedObjectLookupPopup=window.showAddAnotherPopup=window.showRelatedObjectPopup=function(){},window.opener=this.windowStorage.previous()||window.opener,window.dismissRelatedLookupPopup=function(e,i){t.closePopup({action:"lookup",value:i})}},initDeleteRelatedCancellation:function(){var t=this;n(".popup.delete-confirmation .cancel-link").on("click",function(e){e.preventDefault(),t.closePopup()}).removeAttr("onclick")},initLookupLinks:function(){var t=this;n("a[data-popup-opener]").click(function(e){e.preventDefault(),t.closePopup({action:"lookup",value:n(this).data("popup-opener")})})},run:function(){this.windowStorage.push(window);try{this.initLinks(),this.initPopupBackButton(),this.findPopupResponse(),this.overrideRelatedGlobals(),this.initDeleteRelatedCancellation(),this.initLookupLinks()}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){(new s).run()})},{"../utils/window-storage":38,jquery:69}],11:[function(t,e,i){var n=t("jquery"),o=function(){};o.prototype={prevScrollTop:null,initDetector:function(){var t=this;n(window).on("scroll",function(){null!=t.prevScrollTop&&n(window).scrollTop()>t.prevScrollTop&&n(window).scrollTop()>60?n(document.body).addClass("scroll-to-bottom"):n(document.body).removeClass("scroll-to-bottom"),t.prevScrollTop=n(window).scrollTop()})},run:function(){try{this.initDetector()}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){(new o).run()})},{jquery:69}],12:[function(t,e,i){t("select2");var n=t("jquery"),o=t("../utils/translate"),s=function(){};s.prototype={updateAttachBody:function(t){t.prototype._positionDropdown=function(){var t=n(window),e=this.$dropdown.hasClass("select2-dropdown--above"),i=this.$dropdown.hasClass("select2-dropdown--below"),o=null,s=(this.$container.position(),this.$container.offset());s.bottom=s.top+this.$container.outerHeight(!1);var r={height:this.$container.outerHeight(!1)};r.top=s.top,r.bottom=s.top+r.height;var a={height:this.$dropdown.outerHeight(!1)},l={top:t.scrollTop(),bottom:t.scrollTop()+t.height()},c=l.top<s.top-a.height,u=l.bottom>s.bottom+a.height,d={left:s.left,top:r.bottom};if(e||i||(o="below"),u||!c||e?!c&&u&&e&&(o="below"):o="above",("above"==o||e&&"below"!==o)&&(d.top=r.top-a.height),null!=o){this.$dropdown.removeClass("select2-dropdown--below select2-dropdown--above").addClass("select2-dropdown--"+o),this.$container.removeClass("select2-container--below select2-container--above").addClass("select2-container--"+o);var h=this.$dropdown.find(".select2-search");"above"==o&&h.is(":first-child")?h.detach().appendTo(this.$dropdown):"below"==o&&h.is(":last-child")&&h.detach().prependTo(this.$dropdown)}this.$dropdownContainer.css(d)},t.prototype.render=function(t){var e=n("<span></span>"),i=t.call(this);return e.append(i),this.$dropdownContainer=e,this.$element.prop("multiple")?this.$dropdown.addClass("select2-multiple-dropdown"):this.$dropdown.removeClass("select2-multiple-dropdown"),e}},updateDropdownAdapter:function(t){t.prototype.render=function(){var t="";this.options.get("multiple")&&(t='<div class="select2-buttons"><a href="#" class="select2-buttons-button select2-buttons-button-select-all">'+o("select all")+'</a> <a href="#" class="select2-buttons-button select2-buttons-button-deselect-all">'+o("deselect all")+"</a></div>");var e=n('<span class="select2-dropdown">'+t+'<span class="select2-results"></span></span>'),i=this.$element;return e.find(".select2-buttons-button-select-all").on("click",function(t){t.preventDefault();var e=[];i.find("option").each(function(){e.push(n(this).val())}),i.select2("val",e),i.select2("close")}),e.find(".select2-buttons-button-deselect-all").on("click",function(t){t.preventDefault(),i.select2("val",""),i.select2("close")}),e.attr("dir",this.options.get("dir")),this.$dropdown=e,e}},initSelect:function(t,e){var i={theme:"jet",dropdownAdapter:e,width:"auto"};if(t.hasClass("ajax")){var n=t.data("content-type-id"),o=t.data("app-label"),s=t.data("model"),r=t.data("object-id"),a=100;i.ajax={dataType:"json",data:function(t){return{content_type:n,app_label:o,model:s,q:t.term,page:t.page,page_size:a,object_id:r}},processResults:function(t,e){if(t.error)return{};e.page=e.page||1;var i=e.page*a<t.total;return{results:t.items,pagination:{more:i}}}}}t.on("change",function(e){django.jQuery(t.get(0)).trigger(e)}),t.select2(i)},initSelect2:function(){var t=this,e=n.fn.select2.amd.require("select2/dropdown/attachBody"),i=n.fn.select2.amd.require("select2/dropdown"),o=n.fn.select2.amd.require("select2/utils"),s=n.fn.select2.amd.require("select2/dropdown/search"),r=n.fn.select2.amd.require("select2/dropdown/minimumResultsForSearch"),a=n.fn.select2.amd.require("select2/dropdown/closeOnSelect");this.updateAttachBody(e),this.updateDropdownAdapter(i),i=o.Decorate(i,s),i=o.Decorate(i,e),i=o.Decorate(i,r),i=o.Decorate(i,a),n(document).on("select:init","select",function(){var e=n(this);e.parents(".empty-form").length>0||t.initSelect(e,i)}),n("select").trigger("select:init"),n(".inline-group").on("inline-group-row:added",function(t,e){e.find("select").trigger("select:init")})},run:function(){try{this.initSelect2()}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){(new s).run()})},{"../utils/translate":37,jquery:69,select2:91}],13:[function(t,e,i){var n=t("jquery"),o=function(t){this.$siblings=t};o.prototype={moveSiblings:function(t){t.detach().insertBefore(n(".object-tools"))},run:function(){try{this.moveSiblings(this.$siblings)}catch(t){console.error(t,t.stack)}this.$siblings.addClass("initialized")}},n(document).ready(function(){n(".changeform-navigation").each(function(){new o(n(this)).run()})})},{jquery:69}],14:[function(t,e,i){t("./../../utils/jquery-slidefade");var n=t("jquery");t("jquery-ui/ui/core"),t("jquery-ui/ui/widget"),t("jquery-ui/ui/mouse"),t("jquery-ui/ui/draggable"),t("jquery-ui/ui/resizable"),t("jquery-ui/ui/button"),t("jquery-ui/ui/dialog");var o=function(t){this.$sidebar=t};o.prototype={pinToggle:function(t,e,i){var o=this,s=e.find(".apps-list"),r=e.find(".apps-list-pinned");n.ajax({url:t.attr("action"),method:t.attr("method"),dataType:"json",data:t.serialize(),success:function(t){if(!t.error){var n=t.pinned?r:s;i.toggleClass("pinned",t.pinned).detach().appendTo(n),o.updateAppsHide(e)}}})},initApplicationPinning:function(t){var e=this;t.find(".pin-toggle").on("click",function(i){i.preventDefault(),i.stopPropagation();var o=n(this).closest(".app-item"),s=o.data("app-label"),r=t.find("#toggle-application-pin-form");r.find('input[name="app_label"]').val(s),e.pinToggle(r,t,o)}),t.find(".edit-apps-list").on("click",function(t){t.preventDefault(),n(this).parents(".sidebar-section").toggleClass("editing")})},updateAppsHide:function(t){var e=t.find(".apps-list"),i=t.find(".apps-list-pinned"),n=t.find(".apps-hide");0!=e.children().length&&0!=i.children().length||!e.is(":visible")?(n.toggleClass("apps-visible",e.is(":visible")),n.toggleClass("apps-hidden",!e.is(":visible"))):n.removeClass("apps-visible apps-hidden")},initAppsHide:function(t){var e=this,i=t.find(".apps-list"),n=t.find(".apps-list-pinned"),o=t.find(".apps-hide");o.on("click",function(n){n.preventDefault(),i.slideFadeToggle(200,"swing",function(){localStorage.side_menu_apps_list_visible=i.is(":visible"),e.updateAppsHide(t)})}),"false"===localStorage.side_menu_apps_list_visible&&(0!=n.children().length?i.hide():localStorage.side_menu_apps_list_visible=!0),this.updateAppsHide(t)},run:function(){try{this.initApplicationPinning(this.$sidebar),this.initAppsHide(this.$sidebar)}catch(t){console.error(t,t.stack)}}},e.exports=o},{"./../../utils/jquery-slidefade":36,jquery:69,"jquery-ui/ui/button":55,"jquery-ui/ui/core":56,"jquery-ui/ui/dialog":58,"jquery-ui/ui/draggable":59,"jquery-ui/ui/mouse":61,"jquery-ui/ui/resizable":63,"jquery-ui/ui/widget":66}],15:[function(t,e,i){var n=t("jquery"),o=t("../../utils/translate");t("jquery-ui/ui/core"),t("jquery-ui/ui/widget"),t("jquery-ui/ui/mouse"),t("jquery-ui/ui/draggable"),t("jquery-ui/ui/resizable"),t("jquery-ui/ui/button"),t("jquery-ui/ui/dialog");var s=function(t){this.$sidebar=t};s.prototype={addBookmark:function(t,e){n.ajax({url:t.attr("action"),method:t.attr("method"),dataType:"json",data:t.serialize(),success:function(t){if(!t.error){var i=e.find(".bookmark-item.clone").clone().removeClass("clone");i.attr("href",t.url).find(".sidebar-link-label").append(t.title),i.find(".bookmarks-remove").data("bookmark-id",t.id),e.append(i)}}})},deleteBookmark:function(t,e){n.ajax({url:t.attr("action"),method:t.attr("method"),dataType:"json",data:t.serialize(),success:function(t){t.error||e.remove()}})},initBookmarksAdding:function(t){var e=this,i=t.find("#bookmarks-add-form"),s=i.find('input[name="title"]'),r=i.find('input[name="url"]'),a=t.find("#bookmarks-add-dialog"),l=t.find(".bookmarks-list");t.find(".bookmarks-add").on("click",function(t){t.preventDefault();var c=n(this),u=c.data("title")?c.data("title"):document.title,d=window.location.href;s.val(u),r.val(d);var h={};h[o("Add")]=function(){e.addBookmark(i,l),n(this).dialog("close")},h[o("Cancel")]=function(){n(this).dialog("close")},a.dialog({resizable:!1,modal:!0,buttons:h})})},initBookmarksRemoving:function(t){var e=this,i=t.find("#bookmarks-remove-form"),s=i.find('input[name="id"]'),r=t.find("#bookmarks-remove-dialog");t.on("click",".bookmarks-remove",function(t){t.preventDefault();var a=n(this),l=a.closest(".bookmark-item"),c=a.data("bookmark-id");s.val(c);var u={};u[o("Delete")]=function(){e.deleteBookmark(i,l),n(this).dialog("close")},u[o("Cancel")]=function(){n(this).dialog("close")},r.dialog({resizable:!1,modal:!0,buttons:u})})},initBookmarks:function(t){this.initBookmarksAdding(t),this.initBookmarksRemoving(t)},run:function(){
try{this.initBookmarksAdding(this.$sidebar),this.initBookmarksRemoving(this.$sidebar)}catch(t){console.error(t,t.stack)}}},e.exports=s},{"../../utils/translate":37,jquery:69,"jquery-ui/ui/button":55,"jquery-ui/ui/core":56,"jquery-ui/ui/dialog":58,"jquery-ui/ui/draggable":59,"jquery-ui/ui/mouse":61,"jquery-ui/ui/resizable":63,"jquery-ui/ui/widget":66}],16:[function(t,e,i){var n=t("jquery"),o=t("./application-pinning"),s=t("./bookmarks"),r=t("./popup");t("perfect-scrollbar/jquery")(n),t("browsernizr/test/touchevents"),t("browsernizr"),t("jquery.cookie");var a=function(t){this.$sidebar=t};a.prototype={initScrollBars:function(t){n(document.documentElement).hasClass("touchevents")||t.find(".sidebar-wrapper").perfectScrollbar()},initSideBarToggle:function(){var t=function(t){t.preventDefault(),this.sideBarToggle()};n(".sidebar-toggle").on("click",t.bind(this)),n(document.body).on("click",".sidebar-backdrop",t.bind(this))},sideBarToggle:function(){var t=n(".sidebar-dependent"),e=!t.hasClass("sidebar-opened")&&!n(document.body).hasClass("menu-pinned");n(document.body).toggleClass("non-scrollable",e).removeClass("menu-pinned"),t.toggleClass("sidebar-opened",e),this.storePinStatus(!1),this.toggleBackdrop(e)},toggleBackdrop:function(t){if(t){var e=n("<div/>",{"class":"sidebar-backdrop"});n(document.body).append(e),e.animate({opacity:.5},300)}else n(".sidebar-backdrop").animate({opacity:0},300,function(){n(this).remove()})},initPinSideBar:function(t){t.on("click",".sidebar-pin",function(){var t=n(".sidebar-dependent");n(document.body).hasClass("menu-pinned")?(t.removeClass("sidebar-opened"),n(document.body).removeClass("menu-pinned"),this.storePinStatus(!1)):(this.storePinStatus(!0),n(document.body).addClass("menu-pinned").removeClass("non-scrollable")),this.toggleBackdrop(!1),setTimeout(function(){n(window).trigger("resize")},500)}.bind(this))},storePinStatus:function(t){n.cookie("sidebar_pinned",t,{expires:365,path:"/"})},addToggleButton:function(){var t=n("<span>").addClass("sidebar-container-toggle sidebar-header-menu-icon icon-menu sidebar-toggle");n("#container").prepend(t)},run:function(){var t=this.$sidebar;new o(t).run(),new s(t).run(),new r(t).run();try{this.initScrollBars(t),this.addToggleButton(),this.initSideBarToggle(),this.initPinSideBar(t)}catch(e){console.error(e,e.stack)}t.addClass("initialized")}},n(document).ready(function(){n(".sidebar").each(function(){new a(n(this)).run()})}),e.exports=new a},{"./application-pinning":14,"./bookmarks":15,"./popup":17,browsernizr:39,"browsernizr/test/touchevents":54,jquery:69,"jquery.cookie":67,"perfect-scrollbar/jquery":70}],17:[function(t,e,i){t("../../utils/jquery-icontains");var n=t("jquery");t("browsernizr/test/touchevents"),t("browsernizr");var o=function(t){this.$sidebar=t};o.prototype={popupDisplayTimeout:null,$currentSectionLink:null,$currentSection:null,$currentSectionListItem:null,resetPopupDisplayTimeout:function(){null!=this.popupDisplayTimeout&&clearTimeout(this.popupDisplayTimeout)},setCurrentSectionLink:function(t){this.$currentSectionLink&&this.$currentSectionLink.removeClass("selected"),this.$currentSectionLink=t,this.$currentSectionLink&&this.$currentSectionLink.addClass("selected")},openPopup:function(t,e){var i=this;this.resetPopupDisplayTimeout(),e=e&&void 0!=e?e:200,this.popupDisplayTimeout=setTimeout(function(){i.popupDisplayTimeout=null;var e=t.find(".sidebar-popup-section");if(e.hide(),i.$currentSectionLink){var o=e.filter("."+i.$currentSectionLink.data("popup-section-class")),s=o.find(".sidebar-popup-search");o.show(),s.val("").trigger("change").focus(),i.$currentSection=o,i.resetCurrentSectionListItems(),i.$currentSectionListItem=null}t.stop().fadeIn(200,"swing"),n(document.body).addClass("non-scrollable")},e)},closePopup:function(t,e){var i=this;this.resetPopupDisplayTimeout(),e=e&&void 0!=e?e:50,this.popupDisplayTimeout=setTimeout(function(){i.popupDisplayTimeout=null,i.setCurrentSectionLink(null),i.$currentSection=null,t.stop().fadeOut(200,"swing"),n(document.documentElement).hasClass("touchevents")||n(document.body).removeClass("non-scrollable")},e)},onSectionLinkInteracted:function(t,e){var i=this.$currentSectionLink&&e!==this.$currentSectionLink;this.setCurrentSectionLink(e),this.openPopup(t,i?500:null)},initSectionsDisplay:function(t){var e=this,i=t.find(".sidebar-popup-container"),o=t.find(".sidebar-popup");t.find(".popup-section-link").on("mouseenter",function(){n(document.documentElement).hasClass("touchevents")||e.onSectionLinkInteracted(i,n(this))}).on("mouseleave",function(){e.closePopup(i)}).on("click",function(t){t.preventDefault(),!n(document.documentElement).hasClass("touchevents")&&n(this).attr("href")?document.location=n(this).attr("href"):e.onSectionLinkInteracted(i,n(this))}),t.find(".sidebar-back").on("click touchend",function(t){t.preventDefault(),e.closePopup(i)}),o.on("mouseenter",function(){e.openPopup(i,0)}).on("mouseleave",function(){e.closePopup(i)})},initSectionsSearch:function(t){t.find(".sidebar-popup-section").each(function(){var t=n(this),e=t.find(".sidebar-popup-search"),i=t.find(".sidebar-popup-list-item");e.on("change keyup",function(){var t=n(this).val();i.hide().find('.sidebar-popup-list-item-link:icontains("'+t+'")').closest(".sidebar-popup-list-item").show()})})},resetCurrentSectionListItems:function(){this.$currentSection.find(".sidebar-popup-list-item:visible").removeClass("selected")},moveSectionListItemSelection:function(t){if(null!=this.$currentSectionListItem&&(t?this.$currentSectionListItem=this.$currentSectionListItem.nextAll(":visible").first():this.$currentSectionListItem=this.$currentSectionListItem.prevAll(":visible").first()),null==this.$currentSectionListItem||0==this.$currentSectionListItem.length){var e=this.$currentSection.find(".sidebar-popup-list-item:visible");this.$currentSectionListItem=t?e.first():e.last()}this.resetCurrentSectionListItems(),this.$currentSectionListItem.addClass("selected")},initSectionKeyboardControls:function(){var t=this;n(document).keydown(function(e){if(null!=t.$currentSectionLink){if(38==e.which)t.moveSectionListItemSelection(!1);else if(40==e.which)t.moveSectionListItemSelection(!0);else{if(13!=e.which)return;if(t.$currentSectionListItem){var i=t.$currentSectionListItem.find("a");i.attr("href")&&(document.location=i.attr("href"))}}e.preventDefault()}})},initSectionLists:function(t){var e=this;t.find(".sidebar-popup-list-item-link").on("mouseenter",function(){e.$currentSectionListItem=n(this).closest(".sidebar-popup-list-item"),e.resetCurrentSectionListItems(),e.$currentSectionListItem.addClass("selected")}).on("touchmove touchend",function(t){var e=n(this);return"touchmove"==t.type?void e.data("element_swiped",!0):("touchend"==t.type&&!e.data("element_swiped")&&e.attr("href")&&(window.location=e.attr("href")),void e.data("element_swiped",!1))}),this.initSectionKeyboardControls()},run:function(){try{this.initSectionsDisplay(this.$sidebar),this.initSectionsSearch(this.$sidebar),this.initSectionLists(this.$sidebar)}catch(t){console.error(t,t.stack)}}},e.exports=o},{"../../utils/jquery-icontains":35,browsernizr:39,"browsernizr/test/touchevents":54,jquery:69}],18:[function(t,e,i){t("jquery.cookie");var n=t("jquery"),o=function(){};o.prototype={moveChooser:function(t){t.detach().insertAfter(n(".user-tools-welcome-msg")).addClass("initialized")},initChooser:function(t){var e=t.find(".choose-theme");e.on("click",function(t){t.preventDefault();var i=n(this);n.cookie("JET_THEME",i.data("theme"),{expires:365,path:"/"});var o=[{url:i.data("base-stylesheet"),"class":"base-stylesheet"},{url:i.data("select2-stylesheet"),"class":"select2-stylesheet"},{url:i.data("jquery-ui-stylesheet"),"class":"jquery-ui-stylesheet"}],s=0,r=function(){++s,s==o.length&&n(document).trigger("theme:changed")};n.each(o,function(){n("<link>").attr("rel","stylesheet").addClass(this["class"]).attr("href",this.url).load(r).appendTo("head"),n("."+this["class"]).slice(0,-2).remove()}),e.removeClass("selected"),i.addClass("selected")})},run:function(){var t=n(".theme-chooser");try{this.moveChooser(t),this.initChooser(t)}catch(e){console.error(e,e.stack)}}},n(document).ready(function(){(new o).run()})},{jquery:69,"jquery.cookie":67}],19:[function(t,e,i){var n=t("jquery");t("jquery-ui/ui/core"),t("jquery-ui/ui/position"),t("jquery-ui/ui/widget"),t("jquery-ui/ui/tooltip"),t("browsernizr/test/touchevents"),t("browsernizr");var o=function(){};o.prototype={initTooltips:function(){n(document.documentElement).hasClass("touchevents")||n("a[title], .tooltip[title]").tooltip({track:!0})},run:function(){try{this.initTooltips()}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){new o(n(this)).run()})},{browsernizr:39,"browsernizr/test/touchevents":54,jquery:69,"jquery-ui/ui/core":56,"jquery-ui/ui/position":62,"jquery-ui/ui/tooltip":65,"jquery-ui/ui/widget":66}],20:[function(t,e,i){var n=t("jquery"),o=function(){};o.prototype={initTouchMoveHandler:function(){n(document).on("touchmove",function(t){for(var e=!0,i=n(t.target);i.length>0;){if(i.hasClass("non-scrollable")){e=!1;break}if(i.hasClass("scrollable")||i.hasClass("ui-widget-overlay"))break;i=i.parent()}e||t.preventDefault()})},run:function(){try{this.initTouchMoveHandler()}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){(new o).run()})},{jquery:69}],21:[function(t,e,i){var n=t("jquery"),o=function(t){this.$changelist=t};o.prototype={removeLabel:function(t){var e=t.find('[name="action"]').first();if(0!=e.length){var i=n(e[0].previousSibling);3==i.get(0).nodeType&&i.remove()}},wrapLabels:function(t){var e=n("<div>").addClass("labels");t.find("span.all, span.action-counter, span.clear, span.question").wrapAll(e)},moveActions:function(t){var e=this.$changelist.find(".paginator"),i=n("<div>").addClass("changelist-footer");i.insertAfter(e),t.detach(),e.detach(),i.append(t).append(e).append(n("<div>").addClass("cf"))},run:function(){var t=this.$changelist.find(".actions");try{this.removeLabel(t),this.wrapLabels(t),this.moveActions(t)}catch(e){console.error(e,e.stack)}t.addClass("initialized")}},n(document).ready(function(){n("#changelist").each(function(){new o(n(this)).run()})})},{jquery:69}],22:[function(t,e,i){var n=t("jquery"),o=function(t){this.$branding=t};o.prototype={move:function(t){t.detach().prependTo(n(".sidebar-wrapper")).css("height",t.outerHeight())},run:function(){var t=this.$branding;try{this.move(t)}catch(e){console.error(e,e.stack)}t.addClass("initialized")}},n(document).ready(function(){n("#branding").each(function(){new o(n(this)).run()}),0!=n("body.login").length&&n("<img>").attr("src","//jet.geex-arts.com/ping.gif")})},{jquery:69}],23:[function(t,e,i){var n=t("jquery"),o=function(t){this.$breadcrumbs=t};o.prototype={replaceSeparators:function(t){var e=t.html();e=e.replace(/›/g,'<span class="icon-arrow-right breadcrumbs-separator"></span>'),t.html(e)},scrollToEnd:function(t){t.scrollLeft(t[0].scrollWidth-t.width())},run:function(){var t=this.$breadcrumbs;try{this.replaceSeparators(t),this.scrollToEnd(t)}catch(e){console.error(e,e.stack)}t.addClass("initialized")}},n(document).ready(function(){var t=n(".breadcrumbs");0!=t.length&&t.each(function(){new o(n(this)).run()})})},{jquery:69}],24:[function(t,e,i){var n=t("jquery"),o=t("../utils/translate"),s=function(t){this.$changeform=t};s.prototype={findTabs:function(t,e){var i=[];return t.each(function(t){var e=n(this),s=e.find("> h2").first(),r=0!=s.length?s.html():o("General"),a="module_"+t;e.addClass(a),s.remove(),i.push({className:a,title:r})}),e.each(function(t){var e=n(this),s=e.find("> h2, > fieldset.module > h2, .tabular.inline-related > .module > h2").first(),r=0!=s.length?s.html():o("General"),a="inline_"+t;e.addClass(a),s.remove(),i.push({className:a,title:r})}),i},createTabs:function(t,e){if(!(e.length<2)){var i=n("<ul>").addClass("changeform-tabs");n.each(e,function(){var t=this,e=n("<li>").addClass("changeform-tabs-item"),o=n("<a>").addClass("changeform-tabs-item-link").html(t.title).attr("href","#/tab/"+t.className+"/");o.appendTo(e),e.appendTo(i)}),i.insertBefore(t.first())}},run:function(){var t=this.$changeform.find("#content-main > form > div"),e=t.find("> .module"),i=t.find("> .inline-group"),o=n().add(e).add(i);try{var s=this.findTabs(e,i);this.createTabs(o,s)}catch(r){console.error(r,r.stack)}o.addClass("initialized")}},n(document).ready(function(){n(".change-form").each(function(){new s(n(this)).run()})})},{"../utils/translate":37,jquery:69}],25:[function(t,e,i){var n=t("jquery"),o=function(){};o.prototype={run:function(){try{0!=n(".delete-confirmation-marker").length&&n("body").addClass("delete-confirmation")}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){(new o).run()})},{jquery:69}],26:[function(t,e,i){var n=t("jquery"),o=function(){};o.prototype={updateBooleanIcons:function(){n('img[src$="admin/img/icon-yes.gif"]').add('img[src$="admin/img/icon-yes.svg"]').after(n('<span class="icon-tick">')),n('img[src$="admin/img/icon-no.gif"]').add('img[src$="admin/img/icon-no.svg"]').after(n('<span class="icon-cross">')),n('img[src$="admin/img/icon-unknown.gif"]').add('img[src$="admin/img/icon-unknown.svg"]').after(n('<span class="icon-question">'))},run:function(){try{this.updateBooleanIcons()}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){(new o).run()})},{jquery:69}],27:[function(t,e,i){var n=t("jquery"),o=function(t){this.$objectTools=t};o.prototype={run:function(){this.$objectTools.addClass("initialized")}},n(document).ready(function(){n(".object-tools").each(function(){new o(n(this)).run()})})},{jquery:69}],28:[function(t,e,i){var n=t("jquery"),o=function(t){this.$paginator=t};o.prototype={removeSpacesBetweenPages:function(){this.$paginator.contents().each(function(){if(3==this.nodeType){var t=n(this);"A"!=t.prev().prop("tagName")&&"SPAN"!=t.prev().prop("tagName")||"A"!=t.next().prop("tagName")&&"SPAN"!=t.next().prop("tagName")||("..."==n.trim(t.text())?t.wrap(n("<span>").addClass("disabled")):""==n.trim(t.text())&&t.remove())}})},wrapPages:function(){var t=!1,e=!1,i=n([]);this.$paginator.contents().each(function(){var o=n(this),s="A"==this.tagName&&!o.hasClass("showall")||"SPAN"==this.tagName;s&&(t=!0),t&&(s&&!e?(o.detach(),i=i.add(o)):e=!0)}),this.$paginator.prepend(n("<span>").addClass("pages-wrapper").append(i))},wrapTextNodes:function(){var t=!1,e=n([]);this.$paginator.contents().each(function(){var i=n(this),o="A"==this.tagName&&!i.hasClass("showall")||"SPAN"==this.tagName;o?t=!0:t&&!o&&"INPUT"!=this.tagName&&(i.detach(),e=e.add(i))}),n("<div>").addClass("label").append(e).appendTo(this.$paginator)},run:function(){try{this.removeSpacesBetweenPages(),this.wrapPages(),this.wrapTextNodes()}catch(t){console.error(t,t.stack)}this.$paginator.addClass("initialized")}},n(document).ready(function(){n(".paginator").each(function(){new o(n(this)).run()})})},{jquery:69}],29:[function(t,e,i){var n=t("jquery"),o=function(){};o.prototype={replaceLinkIcon:function(t){var e=n(t);n("<span>").addClass("related-widget-wrapper-icon").insertAfter(e),e.remove()},updateLinkIcons:function(){this.replaceLinkIcon('img[src*="admin/img/icon-addlink"], img[src*="admin/img/icon_addlink"]'),this.replaceLinkIcon('img[src*="admin/img/icon-changelink"], img[src*="admin/img/icon_changelink"]'),this.replaceLinkIcon('img[src*="admin/img/icon-deletelink"], img[src*="admin/img/icon_deletelink"]'),n('img[src*="admin/img/selector-search"]').remove(),n(".add-related, .add-another, .change-related, .delete-related, .related-lookup").addClass("initialized")},run:function(){try{this.updateLinkIcons()}catch(t){console.error(t,t.stack)}}},n(document).ready(function(){(new o).run()})},{jquery:69}],30:[function(t,e,i){var n=t("jquery"),o=function(t){this.$inline=t};o.prototype={updateObjectLinks:function(){var t=this.$inline.find(".inline_label"),e=t.find("> .inlinechangelink");t.find("+ a").addClass("inlineviewlink").text(""),e.text("").detach().insertAfter(t)},run:function(){try{this.updateObjectLinks()}catch(t){console.error(t,t.stack)}this.$inline.addClass("initialized")}},n(document).ready(function(){n(".inline-related:not(.tabular)").each(function(){new o(n(this)).run()})})},{jquery:69}],31:[function(t,e,i){var n=t("jquery"),o=function(t){this.$inline=t};o.prototype={updateOriginalCell:function(){this.$inline.find("tr").each(function(){var t=n(this).find("td.original p");t.contents().each(function(){var t=n(this);return 3==t.get(0).nodeType?void t.remove():t.hasClass("inlinechangelink")?void 0:(t.addClass("inlineviewlink"),!1)}),t.find("a").text(""),0==t.children().length&&t.parent().addClass("empty")})},run:function(){try{this.updateOriginalCell()}catch(t){console.error(t,t.stack)}this.$inline.addClass("initialized")}},n(document).ready(function(){n(".inline-related.tabular").each(function(){new o(n(this)).run()})})},{jquery:69}],32:[function(t,e,i){var n=t("jquery"),o=function(t){this.$changelist=t};o.prototype={getToolbar:function(t){var e=t.find("#toolbar");return 0==e.length&&(e=n("<div>").attr("id","toolbar"),n("#changelist").prepend(e)),e},updateToolbar:function(t){var e=t.find('input[type="submit"]').val();t.find("#searchbar").attr("placeholder",e)},moveFilters:function(t,e){var i,o=e.find("#searchbar");t.find("#changelist-filter").children().each(function(){var t=n(this);if("H3"==t.prop("tagName"))i=t.text();else if("UL"==t.prop("tagName")){var s=n("<select>"),r=t.find("li");n.each(t.prop("attributes"),function(){s.attr(this.name,this.value)}),s.addClass("changelist-filter-select"),r.filter(".selected").length>1&&s.attr("multiple",!0),r.each(function(t){var e=n(this),o=e.find("a"),r=n("<option>").text(o.text()).attr("data-url",o.attr("href")).attr("selected",e.hasClass("selected"));if(0==t){null!=i&&r.text(i);var a=n("<option>").attr("disabled",!0).text("---");r=r.add(a)}s.append(r)});var a=n("<span>").addClass("changelist-filter-select-wrapper").append(s);o.length?a.insertAfter(o):e.append(a),i=null}else if(t.hasClass("changelist-filter-popup")){var l=t.find(".changelist-filter-popup-toggle"),c=t.find(".changelist-filter-popup-content"),a=n("<span>").addClass("changelist-filter-select-wrapper").append(t);o.length?a.insertAfter(o):e.append(a),l.on("click",function(t){t.preventDefault(),t.stopPropagation(),c.toggleClass("visible")}),c.on("click",function(t){t.stopPropagation()}),n(document.body).on("click",function(){c.removeClass("visible")})}}),t.find("#changelist-filter").remove()},fixFloatLineBreak:function(){n("#content-main").each(function(){var t=n(this);n.each(["#toolbar",".object-tools","changeform-navigation"],function(e,i){var o=t.find(i).first();if(0!=o.length)return n("<div>").addClass("clear").insertAfter(o),!1})})},run:function(){var t=this.getToolbar(this.$changelist);try{this.updateToolbar(t),this.moveFilters(this.$changelist,t)}catch(e){console.error(e,e.stack)}try{this.fixFloatLineBreak()}catch(e){console.error(e,e.stack)}t.addClass("initialized")}},n(document).ready(function(){n("#changelist").each(function(){new o(n(this)).run()})})},{jquery:69}],33:[function(t,e,i){var n=t("jquery");t("browsernizr/test/touchevents"),t("browsernizr");var o=function(t){this.$usertools=t};o.prototype={updateUserTools:function(t){var e=n("<ul>").addClass("sidebar-dependent"),i=t.find("strong").first().text();n("<li>").addClass("user-tools-welcome-msg").text(i).appendTo(e).on("click",function(){n(document.documentElement).hasClass("touchevents")&&e.toggleClass("opened")}),t.find("a").each(function(){var t=n(this);n("<li>").addClass("user-tools-link").html(t).appendTo(e)}),t.empty().addClass("user-tools").append(e),e.on("mouseenter",function(){e.addClass("opened")}).on("mouseleave",function(){e.removeClass("opened")})},run:function(){try{this.updateUserTools(this.$usertools)}catch(t){console.error(t,t.stack)}this.$usertools.addClass("initialized")}},n(document).ready(function(){n("#user-tools").each(function(){new o(n(this)).run()})})},{browsernizr:39,"browsernizr/test/touchevents":54,jquery:69}],34:[function(t,e,i){var n=window.jQuery=t("jquery");jet={jQuery:n},t("./layout-updaters/actions"),t("./layout-updaters/breadcrumbs"),t("./layout-updaters/paginator"),t("./layout-updaters/toolbar"),t("./layout-updaters/object-tools"),t("./layout-updaters/user-tools"),t("./layout-updaters/changeform-tabs"),t("./layout-updaters/tabular-inline"),t("./layout-updaters/stacked-inline"),t("./layout-updaters/related-widget-wrapper"),t("./layout-updaters/delete-confirmation"),t("./layout-updaters/branding"),t("./layout-updaters/icons"),t("./features/sidebar/main"),t("./features/filters"),t("./features/changeform-tabs"),t("./features/checkboxes"),t("./features/date-time-widgets"),t("./features/inlines"),t("./features/changelist"),t("./features/tooltips"),t("./features/dashboard"),t("./features/changeform"),t("./features/themes"),t("./features/siblings"),t("./features/selects"),t("./features/related-popups"),t("./features/scroll-to-bottom-detector"),t("./features/touchmove-non-scrollable")},{"./features/changeform":2,"./features/changeform-tabs":1,"./features/changelist":3,"./features/checkboxes":4,"./features/dashboard":6,"./features/date-time-widgets":7,"./features/filters":8,"./features/inlines":9,"./features/related-popups":10,"./features/scroll-to-bottom-detector":11,"./features/selects":12,"./features/siblings":13,"./features/sidebar/main":16,"./features/themes":18,"./features/tooltips":19,"./features/touchmove-non-scrollable":20,"./layout-updaters/actions":21,"./layout-updaters/branding":22,"./layout-updaters/breadcrumbs":23,"./layout-updaters/changeform-tabs":24,"./layout-updaters/delete-confirmation":25,"./layout-updaters/icons":26,"./layout-updaters/object-tools":27,"./layout-updaters/paginator":28,"./layout-updaters/related-widget-wrapper":29,"./layout-updaters/stacked-inline":30,"./layout-updaters/tabular-inline":31,"./layout-updaters/toolbar":32,"./layout-updaters/user-tools":33,jquery:69}],35:[function(t,e,i){var n=t("jquery");n.expr[":"].icontains=n.expr.createPseudo(function(t){return function(e){return n(e).text().toUpperCase().indexOf(t.toUpperCase())>=0}})},{jquery:69}],36:[function(t,e,i){var n=t("jquery");n.fn.slideFadeToggle=function(t,e,i){return this.animate({opacity:"toggle",height:"toggle"},t,e,i)}},{jquery:69}],37:[function(t,e,i){e.exports=function(t){return void 0==window.django?t:django.gettext(t)}},{}],38:[function(t,e,i){var n=t("jquery"),o=function(t){void 0==window.top[t]&&(window.top[t]=[]),this.name=t};o.prototype={push:function(t){t.top[this.name][t.top[this.name].length-1]!=t&&t.top[this.name].push(t)},pop:function(){window.top[this.name].pop()},previous:function(){return void 0==window.top[this.name]||!n.isArray(window.top[this.name])||window.top[this.name].length<2?null:window.top[this.name][window.top[this.name].length-2]}},e.exports=o},{jquery:69}],39:[function(t,e,i){var n=t("./lib/Modernizr"),o=t("./lib/ModernizrProto"),s=t("./lib/classes"),r=t("./lib/testRunner"),a=t("./lib/setClasses");r(),a(s),delete o.addTest,delete o.addAsyncTest;for(var l=0;l<n._q.length;l++)n._q[l]();e.exports=n},{"./lib/Modernizr":40,"./lib/ModernizrProto":41,"./lib/classes":42,"./lib/setClasses":50,"./lib/testRunner":51}],40:[function(t,e,i){var n=t("./ModernizrProto.js"),o=function(){};o.prototype=n,o=new o,e.exports=o},{"./ModernizrProto.js":41}],41:[function(t,e,i){var n=t("./tests.js"),o={_version:"3.3.1 (browsernizr 2.1.0)",_config:{classPrefix:"",enableClasses:!0,enableJSClass:!0,usePrefixes:!0},_q:[],on:function(t,e){var i=this;setTimeout(function(){e(i[t])},0)},addTest:function(t,e,i){n.push({name:t,fn:e,options:i})},addAsyncTest:function(t){n.push({name:null,fn:t})}};e.exports=o},{"./tests.js":53}],42:[function(t,e,i){var n=[];e.exports=n},{}],43:[function(t,e,i){function n(){return"function"!=typeof document.createElement?document.createElement(arguments[0]):o?document.createElementNS.call(document,"http://www.w3.org/2000/svg",arguments[0]):document.createElement.apply(document,arguments)}var o=t("./isSVG.js");e.exports=n},{"./isSVG.js":48}],44:[function(t,e,i){var n=document.documentElement;e.exports=n},{}],45:[function(t,e,i){function n(){var t=document.body;return t||(t=o(s?"svg":"body"),t.fake=!0),t}var o=t("./createElement.js"),s=t("./isSVG.js");e.exports=n},{"./createElement.js":43,"./isSVG.js":48}],46:[function(t,e,i){function n(t,e,i,n){var a,l,c,u,d="modernizr",h=s("div"),p=r();if(parseInt(i,10))for(;i--;)c=s("div"),c.id=n?n[i]:d+(i+1),h.appendChild(c);return a=s("style"),a.type="text/css",a.id="s"+d,(p.fake?p:h).appendChild(a),p.appendChild(h),a.styleSheet?a.styleSheet.cssText=t:a.appendChild(document.createTextNode(t)),h.id=d,p.fake&&(p.style.background="",p.style.overflow="hidden",u=o.style.overflow,o.style.overflow="hidden",o.appendChild(p)),l=e(h,t),p.fake?(p.parentNode.removeChild(p),o.style.overflow=u,o.offsetHeight):h.parentNode.removeChild(h),!!l}var o=(t("./ModernizrProto.js"),t("./docElement.js")),s=t("./createElement.js"),r=t("./getBody.js");e.exports=n},{"./ModernizrProto.js":41,"./createElement.js":43,"./docElement.js":44,"./getBody.js":45}],47:[function(t,e,i){function n(t,e){return typeof t===e}e.exports=n},{}],48:[function(t,e,i){var n=t("./docElement.js"),o="svg"===n.nodeName.toLowerCase();e.exports=o},{"./docElement.js":44}],49:[function(t,e,i){var n=t("./ModernizrProto.js"),o=n._config.usePrefixes?" -webkit- -moz- -o- -ms- ".split(" "):["",""];n._prefixes=o,e.exports=o},{"./ModernizrProto.js":41}],50:[function(t,e,i){function n(t){var e=s.className,i=o._config.classPrefix||"";if(r&&(e=e.baseVal),o._config.enableJSClass){var n=new RegExp("(^|\\s)"+i+"no-js(\\s|$)");e=e.replace(n,"$1"+i+"js$2")}o._config.enableClasses&&(e+=" "+i+t.join(" "+i),r?s.className.baseVal=e:s.className=e)}var o=t("./Modernizr.js"),s=t("./docElement.js"),r=t("./isSVG.js");e.exports=n},{"./Modernizr.js":40,"./docElement.js":44,"./isSVG.js":48}],51:[function(t,e,i){function n(){var t,e,i,n,l,c,u;for(var d in o)if(o.hasOwnProperty(d)){if(t=[],e=o[d],e.name&&(t.push(e.name.toLowerCase()),e.options&&e.options.aliases&&e.options.aliases.length))for(i=0;i<e.options.aliases.length;i++)t.push(e.options.aliases[i].toLowerCase());for(n=a(e.fn,"function")?e.fn():e.fn,l=0;l<t.length;l++)c=t[l],u=c.split("."),1===u.length?s[u[0]]=n:(!s[u[0]]||s[u[0]]instanceof Boolean||(s[u[0]]=new Boolean(s[u[0]])),s[u[0]][u[1]]=n),r.push((n?"":"no-")+u.join("-"))}}var o=t("./tests.js"),s=t("./Modernizr.js"),r=t("./classes.js"),a=t("./is.js");e.exports=n},{"./Modernizr.js":40,"./classes.js":42,"./is.js":47,"./tests.js":53}],52:[function(t,e,i){var n=t("./ModernizrProto.js"),o=t("./injectElementWithStyles.js"),s=n.testStyles=o;e.exports=s},{"./ModernizrProto.js":41,"./injectElementWithStyles.js":46}],53:[function(t,e,i){var n=[];e.exports=n},{}],54:[function(t,e,i){var n=t("./../lib/Modernizr.js"),o=t("./../lib/prefixes.js"),s=t("./../lib/testStyles.js");n.addTest("touchevents",function(){var t;if("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)t=!0;else{var e=["@media (",o.join("touch-enabled),("),"heartz",")","{#modernizr{top:9px;position:absolute}}"].join("");s(e,function(e){t=9===e.offsetTop})}return t})},{"./../lib/Modernizr.js":40,"./../lib/prefixes.js":49,"./../lib/testStyles.js":52}],55:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery","./core","./widget"],t):t(jQuery)}(function(t){var e,i="ui-button ui-widget ui-state-default ui-corner-all",n="ui-button-icons-only ui-button-icon-only ui-button-text-icons ui-button-text-icon-primary ui-button-text-icon-secondary ui-button-text-only",o=function(){var e=t(this);setTimeout(function(){e.find(":ui-button").button("refresh")},1)},s=function(e){var i=e.name,n=e.form,o=t([]);return i&&(i=i.replace(/'/g,"\\'"),o=n?t(n).find("[name='"+i+"'][type=radio]"):t("[name='"+i+"'][type=radio]",e.ownerDocument).filter(function(){return!this.form})),o};return t.widget("ui.button",{version:"1.11.4",defaultElement:"<button>",options:{disabled:null,text:!0,label:null,icons:{primary:null,secondary:null}},_create:function(){this.element.closest("form").unbind("reset"+this.eventNamespace).bind("reset"+this.eventNamespace,o),"boolean"!=typeof this.options.disabled?this.options.disabled=!!this.element.prop("disabled"):this.element.prop("disabled",this.options.disabled),this._determineButtonType(),this.hasTitle=!!this.buttonElement.attr("title");var n=this,r=this.options,a="checkbox"===this.type||"radio"===this.type,l=a?"":"ui-state-active";null===r.label&&(r.label="input"===this.type?this.buttonElement.val():this.buttonElement.html()),this._hoverable(this.buttonElement),this.buttonElement.addClass(i).attr("role","button").bind("mouseenter"+this.eventNamespace,function(){r.disabled||this===e&&t(this).addClass("ui-state-active")}).bind("mouseleave"+this.eventNamespace,function(){r.disabled||t(this).removeClass(l)}).bind("click"+this.eventNamespace,function(t){r.disabled&&(t.preventDefault(),t.stopImmediatePropagation())}),this._on({focus:function(){this.buttonElement.addClass("ui-state-focus")},blur:function(){this.buttonElement.removeClass("ui-state-focus")}}),a&&this.element.bind("change"+this.eventNamespace,function(){n.refresh()}),"checkbox"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){if(r.disabled)return!1}):"radio"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){if(r.disabled)return!1;t(this).addClass("ui-state-active"),n.buttonElement.attr("aria-pressed","true");var e=n.element[0];s(e).not(e).map(function(){return t(this).button("widget")[0]}).removeClass("ui-state-active").attr("aria-pressed","false")}):(this.buttonElement.bind("mousedown"+this.eventNamespace,function(){return!r.disabled&&(t(this).addClass("ui-state-active"),e=this,void n.document.one("mouseup",function(){e=null}))}).bind("mouseup"+this.eventNamespace,function(){return!r.disabled&&void t(this).removeClass("ui-state-active")}).bind("keydown"+this.eventNamespace,function(e){return!r.disabled&&void(e.keyCode!==t.ui.keyCode.SPACE&&e.keyCode!==t.ui.keyCode.ENTER||t(this).addClass("ui-state-active"))}).bind("keyup"+this.eventNamespace+" blur"+this.eventNamespace,function(){t(this).removeClass("ui-state-active")}),this.buttonElement.is("a")&&this.buttonElement.keyup(function(e){e.keyCode===t.ui.keyCode.SPACE&&t(this).click()})),this._setOption("disabled",r.disabled),this._resetButton()},_determineButtonType:function(){var t,e,i;this.element.is("[type=checkbox]")?this.type="checkbox":this.element.is("[type=radio]")?this.type="radio":this.element.is("input")?this.type="input":this.type="button","checkbox"===this.type||"radio"===this.type?(t=this.element.parents().last(),e="label[for='"+this.element.attr("id")+"']",this.buttonElement=t.find(e),this.buttonElement.length||(t=t.length?t.siblings():this.element.siblings(),this.buttonElement=t.filter(e),this.buttonElement.length||(this.buttonElement=t.find(e))),this.element.addClass("ui-helper-hidden-accessible"),i=this.element.is(":checked"),i&&this.buttonElement.addClass("ui-state-active"),this.buttonElement.prop("aria-pressed",i)):this.buttonElement=this.element},widget:function(){return this.buttonElement},_destroy:function(){this.element.removeClass("ui-helper-hidden-accessible"),this.buttonElement.removeClass(i+" ui-state-active "+n).removeAttr("role").removeAttr("aria-pressed").html(this.buttonElement.find(".ui-button-text").html()),this.hasTitle||this.buttonElement.removeAttr("title")},_setOption:function(t,e){return this._super(t,e),"disabled"===t?(this.widget().toggleClass("ui-state-disabled",!!e),this.element.prop("disabled",!!e),void(e&&("checkbox"===this.type||"radio"===this.type?this.buttonElement.removeClass("ui-state-focus"):this.buttonElement.removeClass("ui-state-focus ui-state-active")))):void this._resetButton()},refresh:function(){var e=this.element.is("input, button")?this.element.is(":disabled"):this.element.hasClass("ui-button-disabled");e!==this.options.disabled&&this._setOption("disabled",e),"radio"===this.type?s(this.element[0]).each(function(){
t(this).is(":checked")?t(this).button("widget").addClass("ui-state-active").attr("aria-pressed","true"):t(this).button("widget").removeClass("ui-state-active").attr("aria-pressed","false")}):"checkbox"===this.type&&(this.element.is(":checked")?this.buttonElement.addClass("ui-state-active").attr("aria-pressed","true"):this.buttonElement.removeClass("ui-state-active").attr("aria-pressed","false"))},_resetButton:function(){if("input"===this.type)return void(this.options.label&&this.element.val(this.options.label));var e=this.buttonElement.removeClass(n),i=t("<span></span>",this.document[0]).addClass("ui-button-text").html(this.options.label).appendTo(e.empty()).text(),o=this.options.icons,s=o.primary&&o.secondary,r=[];o.primary||o.secondary?(this.options.text&&r.push("ui-button-text-icon"+(s?"s":o.primary?"-primary":"-secondary")),o.primary&&e.prepend("<span class='ui-button-icon-primary ui-icon "+o.primary+"'></span>"),o.secondary&&e.append("<span class='ui-button-icon-secondary ui-icon "+o.secondary+"'></span>"),this.options.text||(r.push(s?"ui-button-icons-only":"ui-button-icon-only"),this.hasTitle||e.attr("title",t.trim(i)))):r.push("ui-button-text-only"),e.addClass(r.join(" "))}}),t.widget("ui.buttonset",{version:"1.11.4",options:{items:"button, input[type=button], input[type=submit], input[type=reset], input[type=checkbox], input[type=radio], a, :data(ui-button)"},_create:function(){this.element.addClass("ui-buttonset")},_init:function(){this.refresh()},_setOption:function(t,e){"disabled"===t&&this.buttons.button("option",t,e),this._super(t,e)},refresh:function(){var e="rtl"===this.element.css("direction"),i=this.element.find(this.options.items),n=i.filter(":ui-button");i.not(":ui-button").button(),n.button("refresh"),this.buttons=i.map(function(){return t(this).button("widget")[0]}).removeClass("ui-corner-all ui-corner-left ui-corner-right").filter(":first").addClass(e?"ui-corner-right":"ui-corner-left").end().filter(":last").addClass(e?"ui-corner-left":"ui-corner-right").end().end()},_destroy:function(){this.element.removeClass("ui-buttonset"),this.buttons.map(function(){return t(this).button("widget")[0]}).removeClass("ui-corner-left ui-corner-right").end().button("destroy")}}),t.ui.button})},{}],56:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(function(t){function e(e,n){var o,s,r,a=e.nodeName.toLowerCase();return"area"===a?(o=e.parentNode,s=o.name,!(!e.href||!s||"map"!==o.nodeName.toLowerCase())&&(r=t("img[usemap='#"+s+"']")[0],!!r&&i(r))):(/^(input|select|textarea|button|object)$/.test(a)?!e.disabled:"a"===a?e.href||n:n)&&i(e)}function i(e){return t.expr.filters.visible(e)&&!t(e).parents().addBack().filter(function(){return"hidden"===t.css(this,"visibility")}).length}t.ui=t.ui||{},t.extend(t.ui,{version:"1.11.4",keyCode:{BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38}}),t.fn.extend({scrollParent:function(e){var i=this.css("position"),n="absolute"===i,o=e?/(auto|scroll|hidden)/:/(auto|scroll)/,s=this.parents().filter(function(){var e=t(this);return(!n||"static"!==e.css("position"))&&o.test(e.css("overflow")+e.css("overflow-y")+e.css("overflow-x"))}).eq(0);return"fixed"!==i&&s.length?s:t(this[0].ownerDocument||document)},uniqueId:function(){var t=0;return function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++t)})}}(),removeUniqueId:function(){return this.each(function(){/^ui-id-\d+$/.test(this.id)&&t(this).removeAttr("id")})}}),t.extend(t.expr[":"],{data:t.expr.createPseudo?t.expr.createPseudo(function(e){return function(i){return!!t.data(i,e)}}):function(e,i,n){return!!t.data(e,n[3])},focusable:function(i){return e(i,!isNaN(t.attr(i,"tabindex")))},tabbable:function(i){var n=t.attr(i,"tabindex"),o=isNaN(n);return(o||n>=0)&&e(i,!o)}}),t("<a>").outerWidth(1).jquery||t.each(["Width","Height"],function(e,i){function n(e,i,n,s){return t.each(o,function(){i-=parseFloat(t.css(e,"padding"+this))||0,n&&(i-=parseFloat(t.css(e,"border"+this+"Width"))||0),s&&(i-=parseFloat(t.css(e,"margin"+this))||0)}),i}var o="Width"===i?["Left","Right"]:["Top","Bottom"],s=i.toLowerCase(),r={innerWidth:t.fn.innerWidth,innerHeight:t.fn.innerHeight,outerWidth:t.fn.outerWidth,outerHeight:t.fn.outerHeight};t.fn["inner"+i]=function(e){return void 0===e?r["inner"+i].call(this):this.each(function(){t(this).css(s,n(this,e)+"px")})},t.fn["outer"+i]=function(e,o){return"number"!=typeof e?r["outer"+i].call(this,e):this.each(function(){t(this).css(s,n(this,e,!0,o)+"px")})}}),t.fn.addBack||(t.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),t("<a>").data("a-b","a").removeData("a-b").data("a-b")&&(t.fn.removeData=function(e){return function(i){return arguments.length?e.call(this,t.camelCase(i)):e.call(this)}}(t.fn.removeData)),t.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase()),t.fn.extend({focus:function(e){return function(i,n){return"number"==typeof i?this.each(function(){var e=this;setTimeout(function(){t(e).focus(),n&&n.call(e)},i)}):e.apply(this,arguments)}}(t.fn.focus),disableSelection:function(){var t="onselectstart"in document.createElement("div")?"selectstart":"mousedown";return function(){return this.bind(t+".ui-disableSelection",function(t){t.preventDefault()})}}(),enableSelection:function(){return this.unbind(".ui-disableSelection")},zIndex:function(e){if(void 0!==e)return this.css("zIndex",e);if(this.length)for(var i,n,o=t(this[0]);o.length&&o[0]!==document;){if(i=o.css("position"),("absolute"===i||"relative"===i||"fixed"===i)&&(n=parseInt(o.css("zIndex"),10),!isNaN(n)&&0!==n))return n;o=o.parent()}return 0}}),t.ui.plugin={add:function(e,i,n){var o,s=t.ui[e].prototype;for(o in n)s.plugins[o]=s.plugins[o]||[],s.plugins[o].push([i,n[o]])},call:function(t,e,i,n){var o,s=t.plugins[e];if(s&&(n||t.element[0].parentNode&&11!==t.element[0].parentNode.nodeType))for(o=0;o<s.length;o++)t.options[s[o][0]]&&s[o][1].apply(t.element,i)}}})},{}],57:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery","./core"],t):t(jQuery)}(function(t){function e(t){for(var e,i;t.length&&t[0]!==document;){if(e=t.css("position"),("absolute"===e||"relative"===e||"fixed"===e)&&(i=parseInt(t.css("zIndex"),10),!isNaN(i)&&0!==i))return i;t=t.parent()}return 0}function i(){this._curInst=null,this._keyEvent=!1,this._disabledInputs=[],this._datepickerShowing=!1,this._inDialog=!1,this._mainDivId="ui-datepicker-div",this._inlineClass="ui-datepicker-inline",this._appendClass="ui-datepicker-append",this._triggerClass="ui-datepicker-trigger",this._dialogClass="ui-datepicker-dialog",this._disableClass="ui-datepicker-disabled",this._unselectableClass="ui-datepicker-unselectable",this._currentClass="ui-datepicker-current-day",this._dayOverClass="ui-datepicker-days-cell-over",this.regional=[],this.regional[""]={closeText:"Done",prevText:"Prev",nextText:"Next",currentText:"Today",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],weekHeader:"Wk",dateFormat:"mm/dd/yy",firstDay:0,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""},this._defaults={showOn:"focus",showAnim:"fadeIn",showOptions:{},defaultDate:null,appendText:"",buttonText:"...",buttonImage:"",buttonImageOnly:!1,hideIfNoPrevNext:!1,navigationAsDateFormat:!1,gotoCurrent:!1,changeMonth:!1,changeYear:!1,yearRange:"c-10:c+10",showOtherMonths:!1,selectOtherMonths:!1,showWeek:!1,calculateWeek:this.iso8601Week,shortYearCutoff:"+10",minDate:null,maxDate:null,duration:"fast",beforeShowDay:null,beforeShow:null,onSelect:null,onChangeMonthYear:null,onClose:null,numberOfMonths:1,showCurrentAtPos:0,stepMonths:1,stepBigMonths:12,altField:"",altFormat:"",constrainInput:!0,showButtonPanel:!1,autoSize:!1,disabled:!1},t.extend(this._defaults,this.regional[""]),this.regional.en=t.extend(!0,{},this.regional[""]),this.regional["en-US"]=t.extend(!0,{},this.regional.en),this.dpDiv=n(t("<div id='"+this._mainDivId+"' class='ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>"))}function n(e){var i="button, .ui-datepicker-prev, .ui-datepicker-next, .ui-datepicker-calendar td a";return e.delegate(i,"mouseout",function(){t(this).removeClass("ui-state-hover"),this.className.indexOf("ui-datepicker-prev")!==-1&&t(this).removeClass("ui-datepicker-prev-hover"),this.className.indexOf("ui-datepicker-next")!==-1&&t(this).removeClass("ui-datepicker-next-hover")}).delegate(i,"mouseover",o)}function o(){t.datepicker._isDisabledDatepicker(r.inline?r.dpDiv.parent()[0]:r.input[0])||(t(this).parents(".ui-datepicker-calendar").find("a").removeClass("ui-state-hover"),t(this).addClass("ui-state-hover"),this.className.indexOf("ui-datepicker-prev")!==-1&&t(this).addClass("ui-datepicker-prev-hover"),this.className.indexOf("ui-datepicker-next")!==-1&&t(this).addClass("ui-datepicker-next-hover"))}function s(e,i){t.extend(e,i);for(var n in i)null==i[n]&&(e[n]=i[n]);return e}t.extend(t.ui,{datepicker:{version:"1.11.4"}});var r;return t.extend(i.prototype,{markerClassName:"hasDatepicker",maxRows:4,_widgetDatepicker:function(){return this.dpDiv},setDefaults:function(t){return s(this._defaults,t||{}),this},_attachDatepicker:function(e,i){var n,o,s;n=e.nodeName.toLowerCase(),o="div"===n||"span"===n,e.id||(this.uuid+=1,e.id="dp"+this.uuid),s=this._newInst(t(e),o),s.settings=t.extend({},i||{}),"input"===n?this._connectDatepicker(e,s):o&&this._inlineDatepicker(e,s)},_newInst:function(e,i){var o=e[0].id.replace(/([^A-Za-z0-9_\-])/g,"\\\\$1");return{id:o,input:e,selectedDay:0,selectedMonth:0,selectedYear:0,drawMonth:0,drawYear:0,inline:i,dpDiv:i?n(t("<div class='"+this._inlineClass+" ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>")):this.dpDiv}},_connectDatepicker:function(e,i){var n=t(e);i.append=t([]),i.trigger=t([]),n.hasClass(this.markerClassName)||(this._attachments(n,i),n.addClass(this.markerClassName).keydown(this._doKeyDown).keypress(this._doKeyPress).keyup(this._doKeyUp),this._autoSize(i),t.data(e,"datepicker",i),i.settings.disabled&&this._disableDatepicker(e))},_attachments:function(e,i){var n,o,s,r=this._get(i,"appendText"),a=this._get(i,"isRTL");i.append&&i.append.remove(),r&&(i.append=t("<span class='"+this._appendClass+"'>"+r+"</span>"),e[a?"before":"after"](i.append)),e.unbind("focus",this._showDatepicker),i.trigger&&i.trigger.remove(),n=this._get(i,"showOn"),"focus"!==n&&"both"!==n||e.focus(this._showDatepicker),"button"!==n&&"both"!==n||(o=this._get(i,"buttonText"),s=this._get(i,"buttonImage"),i.trigger=t(this._get(i,"buttonImageOnly")?t("<img/>").addClass(this._triggerClass).attr({src:s,alt:o,title:o}):t("<button type='button'></button>").addClass(this._triggerClass).html(s?t("<img/>").attr({src:s,alt:o,title:o}):o)),e[a?"before":"after"](i.trigger),i.trigger.click(function(){return t.datepicker._datepickerShowing&&t.datepicker._lastInput===e[0]?t.datepicker._hideDatepicker():t.datepicker._datepickerShowing&&t.datepicker._lastInput!==e[0]?(t.datepicker._hideDatepicker(),t.datepicker._showDatepicker(e[0])):t.datepicker._showDatepicker(e[0]),!1}))},_autoSize:function(t){if(this._get(t,"autoSize")&&!t.inline){var e,i,n,o,s=new Date(2009,11,20),r=this._get(t,"dateFormat");r.match(/[DM]/)&&(e=function(t){for(i=0,n=0,o=0;o<t.length;o++)t[o].length>i&&(i=t[o].length,n=o);return n},s.setMonth(e(this._get(t,r.match(/MM/)?"monthNames":"monthNamesShort"))),s.setDate(e(this._get(t,r.match(/DD/)?"dayNames":"dayNamesShort"))+20-s.getDay())),t.input.attr("size",this._formatDate(t,s).length)}},_inlineDatepicker:function(e,i){var n=t(e);n.hasClass(this.markerClassName)||(n.addClass(this.markerClassName).append(i.dpDiv),t.data(e,"datepicker",i),this._setDate(i,this._getDefaultDate(i),!0),this._updateDatepicker(i),this._updateAlternate(i),i.settings.disabled&&this._disableDatepicker(e),i.dpDiv.css("display","block"))},_dialogDatepicker:function(e,i,n,o,r){var a,l,c,u,d,h=this._dialogInst;return h||(this.uuid+=1,a="dp"+this.uuid,this._dialogInput=t("<input type='text' id='"+a+"' style='position: absolute; top: -100px; width: 0px;'/>"),this._dialogInput.keydown(this._doKeyDown),t("body").append(this._dialogInput),h=this._dialogInst=this._newInst(this._dialogInput,!1),h.settings={},t.data(this._dialogInput[0],"datepicker",h)),s(h.settings,o||{}),i=i&&i.constructor===Date?this._formatDate(h,i):i,this._dialogInput.val(i),this._pos=r?r.length?r:[r.pageX,r.pageY]:null,this._pos||(l=document.documentElement.clientWidth,c=document.documentElement.clientHeight,u=document.documentElement.scrollLeft||document.body.scrollLeft,d=document.documentElement.scrollTop||document.body.scrollTop,this._pos=[l/2-100+u,c/2-150+d]),this._dialogInput.css("left",this._pos[0]+20+"px").css("top",this._pos[1]+"px"),h.settings.onSelect=n,this._inDialog=!0,this.dpDiv.addClass(this._dialogClass),this._showDatepicker(this._dialogInput[0]),t.blockUI&&t.blockUI(this.dpDiv),t.data(this._dialogInput[0],"datepicker",h),this},_destroyDatepicker:function(e){var i,n=t(e),o=t.data(e,"datepicker");n.hasClass(this.markerClassName)&&(i=e.nodeName.toLowerCase(),t.removeData(e,"datepicker"),"input"===i?(o.append.remove(),o.trigger.remove(),n.removeClass(this.markerClassName).unbind("focus",this._showDatepicker).unbind("keydown",this._doKeyDown).unbind("keypress",this._doKeyPress).unbind("keyup",this._doKeyUp)):"div"!==i&&"span"!==i||n.removeClass(this.markerClassName).empty(),r===o&&(r=null))},_enableDatepicker:function(e){var i,n,o=t(e),s=t.data(e,"datepicker");o.hasClass(this.markerClassName)&&(i=e.nodeName.toLowerCase(),"input"===i?(e.disabled=!1,s.trigger.filter("button").each(function(){this.disabled=!1}).end().filter("img").css({opacity:"1.0",cursor:""})):"div"!==i&&"span"!==i||(n=o.children("."+this._inlineClass),n.children().removeClass("ui-state-disabled"),n.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!1)),this._disabledInputs=t.map(this._disabledInputs,function(t){return t===e?null:t}))},_disableDatepicker:function(e){var i,n,o=t(e),s=t.data(e,"datepicker");o.hasClass(this.markerClassName)&&(i=e.nodeName.toLowerCase(),"input"===i?(e.disabled=!0,s.trigger.filter("button").each(function(){this.disabled=!0}).end().filter("img").css({opacity:"0.5",cursor:"default"})):"div"!==i&&"span"!==i||(n=o.children("."+this._inlineClass),n.children().addClass("ui-state-disabled"),n.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!0)),this._disabledInputs=t.map(this._disabledInputs,function(t){return t===e?null:t}),this._disabledInputs[this._disabledInputs.length]=e)},_isDisabledDatepicker:function(t){if(!t)return!1;for(var e=0;e<this._disabledInputs.length;e++)if(this._disabledInputs[e]===t)return!0;return!1},_getInst:function(e){try{return t.data(e,"datepicker")}catch(i){throw"Missing instance data for this datepicker"}},_optionDatepicker:function(e,i,n){var o,r,a,l,c=this._getInst(e);return 2===arguments.length&&"string"==typeof i?"defaults"===i?t.extend({},t.datepicker._defaults):c?"all"===i?t.extend({},c.settings):this._get(c,i):null:(o=i||{},"string"==typeof i&&(o={},o[i]=n),void(c&&(this._curInst===c&&this._hideDatepicker(),r=this._getDateDatepicker(e,!0),a=this._getMinMaxDate(c,"min"),l=this._getMinMaxDate(c,"max"),s(c.settings,o),null!==a&&void 0!==o.dateFormat&&void 0===o.minDate&&(c.settings.minDate=this._formatDate(c,a)),null!==l&&void 0!==o.dateFormat&&void 0===o.maxDate&&(c.settings.maxDate=this._formatDate(c,l)),"disabled"in o&&(o.disabled?this._disableDatepicker(e):this._enableDatepicker(e)),this._attachments(t(e),c),this._autoSize(c),this._setDate(c,r),this._updateAlternate(c),this._updateDatepicker(c))))},_changeDatepicker:function(t,e,i){this._optionDatepicker(t,e,i)},_refreshDatepicker:function(t){var e=this._getInst(t);e&&this._updateDatepicker(e)},_setDateDatepicker:function(t,e){var i=this._getInst(t);i&&(this._setDate(i,e),this._updateDatepicker(i),this._updateAlternate(i))},_getDateDatepicker:function(t,e){var i=this._getInst(t);return i&&!i.inline&&this._setDateFromField(i,e),i?this._getDate(i):null},_doKeyDown:function(e){var i,n,o,s=t.datepicker._getInst(e.target),r=!0,a=s.dpDiv.is(".ui-datepicker-rtl");if(s._keyEvent=!0,t.datepicker._datepickerShowing)switch(e.keyCode){case 9:t.datepicker._hideDatepicker(),r=!1;break;case 13:return o=t("td."+t.datepicker._dayOverClass+":not(."+t.datepicker._currentClass+")",s.dpDiv),o[0]&&t.datepicker._selectDay(e.target,s.selectedMonth,s.selectedYear,o[0]),i=t.datepicker._get(s,"onSelect"),i?(n=t.datepicker._formatDate(s),i.apply(s.input?s.input[0]:null,[n,s])):t.datepicker._hideDatepicker(),!1;case 27:t.datepicker._hideDatepicker();break;case 33:t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(s,"stepBigMonths"):-t.datepicker._get(s,"stepMonths"),"M");break;case 34:t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(s,"stepBigMonths"):+t.datepicker._get(s,"stepMonths"),"M");break;case 35:(e.ctrlKey||e.metaKey)&&t.datepicker._clearDate(e.target),r=e.ctrlKey||e.metaKey;break;case 36:(e.ctrlKey||e.metaKey)&&t.datepicker._gotoToday(e.target),r=e.ctrlKey||e.metaKey;break;case 37:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,a?1:-1,"D"),r=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(s,"stepBigMonths"):-t.datepicker._get(s,"stepMonths"),"M");break;case 38:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,-7,"D"),r=e.ctrlKey||e.metaKey;break;case 39:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,a?-1:1,"D"),r=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(s,"stepBigMonths"):+t.datepicker._get(s,"stepMonths"),"M");break;case 40:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,7,"D"),r=e.ctrlKey||e.metaKey;break;default:r=!1}else 36===e.keyCode&&e.ctrlKey?t.datepicker._showDatepicker(this):r=!1;r&&(e.preventDefault(),e.stopPropagation())},_doKeyPress:function(e){var i,n,o=t.datepicker._getInst(e.target);if(t.datepicker._get(o,"constrainInput"))return i=t.datepicker._possibleChars(t.datepicker._get(o,"dateFormat")),n=String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),e.ctrlKey||e.metaKey||n<" "||!i||i.indexOf(n)>-1},_doKeyUp:function(e){var i,n=t.datepicker._getInst(e.target);if(n.input.val()!==n.lastVal)try{i=t.datepicker.parseDate(t.datepicker._get(n,"dateFormat"),n.input?n.input.val():null,t.datepicker._getFormatConfig(n)),i&&(t.datepicker._setDateFromField(n),t.datepicker._updateAlternate(n),t.datepicker._updateDatepicker(n))}catch(o){}return!0},_showDatepicker:function(i){if(i=i.target||i,"input"!==i.nodeName.toLowerCase()&&(i=t("input",i.parentNode)[0]),!t.datepicker._isDisabledDatepicker(i)&&t.datepicker._lastInput!==i){var n,o,r,a,l,c,u;n=t.datepicker._getInst(i),t.datepicker._curInst&&t.datepicker._curInst!==n&&(t.datepicker._curInst.dpDiv.stop(!0,!0),n&&t.datepicker._datepickerShowing&&t.datepicker._hideDatepicker(t.datepicker._curInst.input[0])),o=t.datepicker._get(n,"beforeShow"),r=o?o.apply(i,[i,n]):{},r!==!1&&(s(n.settings,r),n.lastVal=null,t.datepicker._lastInput=i,t.datepicker._setDateFromField(n),t.datepicker._inDialog&&(i.value=""),t.datepicker._pos||(t.datepicker._pos=t.datepicker._findPos(i),t.datepicker._pos[1]+=i.offsetHeight),a=!1,t(i).parents().each(function(){return a|="fixed"===t(this).css("position"),!a}),l={left:t.datepicker._pos[0],top:t.datepicker._pos[1]},t.datepicker._pos=null,n.dpDiv.empty(),n.dpDiv.css({position:"absolute",display:"block",top:"-1000px"}),t.datepicker._updateDatepicker(n),l=t.datepicker._checkOffset(n,l,a),n.dpDiv.css({position:t.datepicker._inDialog&&t.blockUI?"static":a?"fixed":"absolute",display:"none",left:l.left+"px",top:l.top+"px"}),n.inline||(c=t.datepicker._get(n,"showAnim"),u=t.datepicker._get(n,"duration"),n.dpDiv.css("z-index",e(t(i))+1),t.datepicker._datepickerShowing=!0,t.effects&&t.effects.effect[c]?n.dpDiv.show(c,t.datepicker._get(n,"showOptions"),u):n.dpDiv[c||"show"](c?u:null),t.datepicker._shouldFocusInput(n)&&n.input.focus(),t.datepicker._curInst=n))}},_updateDatepicker:function(e){this.maxRows=4,r=e,e.dpDiv.empty().append(this._generateHTML(e)),this._attachHandlers(e);var i,n=this._getNumberOfMonths(e),s=n[1],a=17,l=e.dpDiv.find("."+this._dayOverClass+" a");l.length>0&&o.apply(l.get(0)),e.dpDiv.removeClass("ui-datepicker-multi-2 ui-datepicker-multi-3 ui-datepicker-multi-4").width(""),s>1&&e.dpDiv.addClass("ui-datepicker-multi-"+s).css("width",a*s+"em"),e.dpDiv[(1!==n[0]||1!==n[1]?"add":"remove")+"Class"]("ui-datepicker-multi"),e.dpDiv[(this._get(e,"isRTL")?"add":"remove")+"Class"]("ui-datepicker-rtl"),e===t.datepicker._curInst&&t.datepicker._datepickerShowing&&t.datepicker._shouldFocusInput(e)&&e.input.focus(),e.yearshtml&&(i=e.yearshtml,setTimeout(function(){i===e.yearshtml&&e.yearshtml&&e.dpDiv.find("select.ui-datepicker-year:first").replaceWith(e.yearshtml),i=e.yearshtml=null},0))},_shouldFocusInput:function(t){return t.input&&t.input.is(":visible")&&!t.input.is(":disabled")&&!t.input.is(":focus")},_checkOffset:function(e,i,n){var o=e.dpDiv.outerWidth(),s=e.dpDiv.outerHeight(),r=e.input?e.input.outerWidth():0,a=e.input?e.input.outerHeight():0,l=document.documentElement.clientWidth+(n?0:t(document).scrollLeft()),c=document.documentElement.clientHeight+(n?0:t(document).scrollTop());return i.left-=this._get(e,"isRTL")?o-r:0,i.left-=n&&i.left===e.input.offset().left?t(document).scrollLeft():0,i.top-=n&&i.top===e.input.offset().top+a?t(document).scrollTop():0,i.left-=Math.min(i.left,i.left+o>l&&l>o?Math.abs(i.left+o-l):0),i.top-=Math.min(i.top,i.top+s>c&&c>s?Math.abs(s+a):0),i},_findPos:function(e){for(var i,n=this._getInst(e),o=this._get(n,"isRTL");e&&("hidden"===e.type||1!==e.nodeType||t.expr.filters.hidden(e));)e=e[o?"previousSibling":"nextSibling"];return i=t(e).offset(),[i.left,i.top]},_hideDatepicker:function(e){var i,n,o,s,r=this._curInst;!r||e&&r!==t.data(e,"datepicker")||this._datepickerShowing&&(i=this._get(r,"showAnim"),n=this._get(r,"duration"),o=function(){t.datepicker._tidyDialog(r)},t.effects&&(t.effects.effect[i]||t.effects[i])?r.dpDiv.hide(i,t.datepicker._get(r,"showOptions"),n,o):r.dpDiv["slideDown"===i?"slideUp":"fadeIn"===i?"fadeOut":"hide"](i?n:null,o),i||o(),this._datepickerShowing=!1,s=this._get(r,"onClose"),s&&s.apply(r.input?r.input[0]:null,[r.input?r.input.val():"",r]),this._lastInput=null,this._inDialog&&(this._dialogInput.css({position:"absolute",left:"0",top:"-100px"}),t.blockUI&&(t.unblockUI(),t("body").append(this.dpDiv))),this._inDialog=!1)},_tidyDialog:function(t){t.dpDiv.removeClass(this._dialogClass).unbind(".ui-datepicker-calendar")},_checkExternalClick:function(e){if(t.datepicker._curInst){var i=t(e.target),n=t.datepicker._getInst(i[0]);(i[0].id===t.datepicker._mainDivId||0!==i.parents("#"+t.datepicker._mainDivId).length||i.hasClass(t.datepicker.markerClassName)||i.closest("."+t.datepicker._triggerClass).length||!t.datepicker._datepickerShowing||t.datepicker._inDialog&&t.blockUI)&&(!i.hasClass(t.datepicker.markerClassName)||t.datepicker._curInst===n)||t.datepicker._hideDatepicker()}},_adjustDate:function(e,i,n){var o=t(e),s=this._getInst(o[0]);this._isDisabledDatepicker(o[0])||(this._adjustInstDate(s,i+("M"===n?this._get(s,"showCurrentAtPos"):0),n),this._updateDatepicker(s))},_gotoToday:function(e){var i,n=t(e),o=this._getInst(n[0]);this._get(o,"gotoCurrent")&&o.currentDay?(o.selectedDay=o.currentDay,o.drawMonth=o.selectedMonth=o.currentMonth,o.drawYear=o.selectedYear=o.currentYear):(i=new Date,o.selectedDay=i.getDate(),o.drawMonth=o.selectedMonth=i.getMonth(),o.drawYear=o.selectedYear=i.getFullYear()),this._notifyChange(o),this._adjustDate(n)},_selectMonthYear:function(e,i,n){var o=t(e),s=this._getInst(o[0]);s["selected"+("M"===n?"Month":"Year")]=s["draw"+("M"===n?"Month":"Year")]=parseInt(i.options[i.selectedIndex].value,10),this._notifyChange(s),this._adjustDate(o)},_selectDay:function(e,i,n,o){var s,r=t(e);t(o).hasClass(this._unselectableClass)||this._isDisabledDatepicker(r[0])||(s=this._getInst(r[0]),s.selectedDay=s.currentDay=t("a",o).html(),s.selectedMonth=s.currentMonth=i,s.selectedYear=s.currentYear=n,this._selectDate(e,this._formatDate(s,s.currentDay,s.currentMonth,s.currentYear)))},_clearDate:function(e){var i=t(e);this._selectDate(i,"")},_selectDate:function(e,i){var n,o=t(e),s=this._getInst(o[0]);i=null!=i?i:this._formatDate(s),s.input&&s.input.val(i),this._updateAlternate(s),n=this._get(s,"onSelect"),n?n.apply(s.input?s.input[0]:null,[i,s]):s.input&&s.input.trigger("change"),s.inline?this._updateDatepicker(s):(this._hideDatepicker(),this._lastInput=s.input[0],"object"!=typeof s.input[0]&&s.input.focus(),this._lastInput=null)},_updateAlternate:function(e){var i,n,o,s=this._get(e,"altField");s&&(i=this._get(e,"altFormat")||this._get(e,"dateFormat"),n=this._getDate(e),o=this.formatDate(i,n,this._getFormatConfig(e)),t(s).each(function(){t(this).val(o)}))},noWeekends:function(t){var e=t.getDay();return[e>0&&e<6,""]},iso8601Week:function(t){var e,i=new Date(t.getTime());return i.setDate(i.getDate()+4-(i.getDay()||7)),e=i.getTime(),i.setMonth(0),i.setDate(1),Math.floor(Math.round((e-i)/864e5)/7)+1},parseDate:function(e,i,n){if(null==e||null==i)throw"Invalid arguments";if(i="object"==typeof i?i.toString():i+"",""===i)return null;var o,s,r,a,l=0,c=(n?n.shortYearCutoff:null)||this._defaults.shortYearCutoff,u="string"!=typeof c?c:(new Date).getFullYear()%100+parseInt(c,10),d=(n?n.dayNamesShort:null)||this._defaults.dayNamesShort,h=(n?n.dayNames:null)||this._defaults.dayNames,p=(n?n.monthNamesShort:null)||this._defaults.monthNamesShort,f=(n?n.monthNames:null)||this._defaults.monthNames,g=-1,m=-1,v=-1,y=-1,b=!1,_=function(t){var i=o+1<e.length&&e.charAt(o+1)===t;return i&&o++,i},w=function(t){var e=_(t),n="@"===t?14:"!"===t?20:"y"===t&&e?4:"o"===t?3:2,o="y"===t?n:1,s=new RegExp("^\\d{"+o+","+n+"}"),r=i.substring(l).match(s);if(!r)throw"Missing number at position "+l;return l+=r[0].length,parseInt(r[0],10)},k=function(e,n,o){var s=-1,r=t.map(_(e)?o:n,function(t,e){return[[e,t]]}).sort(function(t,e){return-(t[1].length-e[1].length)});if(t.each(r,function(t,e){var n=e[1];if(i.substr(l,n.length).toLowerCase()===n.toLowerCase())return s=e[0],l+=n.length,!1}),s!==-1)return s+1;throw"Unknown name at position "+l},x=function(){if(i.charAt(l)!==e.charAt(o))throw"Unexpected literal at position "+l;l++};for(o=0;o<e.length;o++)if(b)"'"!==e.charAt(o)||_("'")?x():b=!1;else switch(e.charAt(o)){case"d":v=w("d");break;case"D":k("D",d,h);break;case"o":y=w("o");break;case"m":m=w("m");break;case"M":m=k("M",p,f);break;case"y":g=w("y");break;case"@":a=new Date(w("@")),g=a.getFullYear(),m=a.getMonth()+1,v=a.getDate();break;case"!":a=new Date((w("!")-this._ticksTo1970)/1e4),g=a.getFullYear(),m=a.getMonth()+1,v=a.getDate();break;case"'":_("'")?x():b=!0;break;default:x()}if(l<i.length&&(r=i.substr(l),!/^\s+/.test(r)))throw"Extra/unparsed characters found in date: "+r;if(g===-1?g=(new Date).getFullYear():g<100&&(g+=(new Date).getFullYear()-(new Date).getFullYear()%100+(g<=u?0:-100)),y>-1)for(m=1,v=y;;){if(s=this._getDaysInMonth(g,m-1),v<=s)break;m++,v-=s}if(a=this._daylightSavingAdjust(new Date(g,m-1,v)),a.getFullYear()!==g||a.getMonth()+1!==m||a.getDate()!==v)throw"Invalid date";return a},ATOM:"yy-mm-dd",COOKIE:"D, dd M yy",ISO_8601:"yy-mm-dd",RFC_822:"D, d M y",RFC_850:"DD, dd-M-y",RFC_1036:"D, d M y",RFC_1123:"D, d M yy",RFC_2822:"D, d M yy",RSS:"D, d M y",TICKS:"!",TIMESTAMP:"@",W3C:"yy-mm-dd",_ticksTo1970:24*(718685+Math.floor(492.5)-Math.floor(19.7)+Math.floor(4.925))*60*60*1e7,formatDate:function(t,e,i){if(!e)return"";var n,o=(i?i.dayNamesShort:null)||this._defaults.dayNamesShort,s=(i?i.dayNames:null)||this._defaults.dayNames,r=(i?i.monthNamesShort:null)||this._defaults.monthNamesShort,a=(i?i.monthNames:null)||this._defaults.monthNames,l=function(e){var i=n+1<t.length&&t.charAt(n+1)===e;return i&&n++,i},c=function(t,e,i){var n=""+e;if(l(t))for(;n.length<i;)n="0"+n;return n},u=function(t,e,i,n){return l(t)?n[e]:i[e]},d="",h=!1;if(e)for(n=0;n<t.length;n++)if(h)"'"!==t.charAt(n)||l("'")?d+=t.charAt(n):h=!1;else switch(t.charAt(n)){case"d":d+=c("d",e.getDate(),2);break;case"D":d+=u("D",e.getDay(),o,s);break;case"o":d+=c("o",Math.round((new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime()-new Date(e.getFullYear(),0,0).getTime())/864e5),3);break;case"m":d+=c("m",e.getMonth()+1,2);break;case"M":d+=u("M",e.getMonth(),r,a);break;case"y":d+=l("y")?e.getFullYear():(e.getYear()%100<10?"0":"")+e.getYear()%100;break;case"@":d+=e.getTime();break;case"!":d+=1e4*e.getTime()+this._ticksTo1970;break;case"'":l("'")?d+="'":h=!0;break;default:d+=t.charAt(n)}return d},_possibleChars:function(t){var e,i="",n=!1,o=function(i){var n=e+1<t.length&&t.charAt(e+1)===i;return n&&e++,n};for(e=0;e<t.length;e++)if(n)"'"!==t.charAt(e)||o("'")?i+=t.charAt(e):n=!1;else switch(t.charAt(e)){case"d":case"m":case"y":case"@":i+="0123456789";break;case"D":case"M":return null;case"'":o("'")?i+="'":n=!0;break;default:i+=t.charAt(e)}return i},_get:function(t,e){return void 0!==t.settings[e]?t.settings[e]:this._defaults[e]},_setDateFromField:function(t,e){if(t.input.val()!==t.lastVal){var i=this._get(t,"dateFormat"),n=t.lastVal=t.input?t.input.val():null,o=this._getDefaultDate(t),s=o,r=this._getFormatConfig(t);try{s=this.parseDate(i,n,r)||o}catch(a){n=e?"":n}t.selectedDay=s.getDate(),t.drawMonth=t.selectedMonth=s.getMonth(),t.drawYear=t.selectedYear=s.getFullYear(),t.currentDay=n?s.getDate():0,t.currentMonth=n?s.getMonth():0,t.currentYear=n?s.getFullYear():0,this._adjustInstDate(t)}},_getDefaultDate:function(t){return this._restrictMinMax(t,this._determineDate(t,this._get(t,"defaultDate"),new Date))},_determineDate:function(e,i,n){var o=function(t){var e=new Date;return e.setDate(e.getDate()+t),e},s=function(i){try{return t.datepicker.parseDate(t.datepicker._get(e,"dateFormat"),i,t.datepicker._getFormatConfig(e))}catch(n){}for(var o=(i.toLowerCase().match(/^c/)?t.datepicker._getDate(e):null)||new Date,s=o.getFullYear(),r=o.getMonth(),a=o.getDate(),l=/([+\-]?[0-9]+)\s*(d|D|w|W|m|M|y|Y)?/g,c=l.exec(i);c;){switch(c[2]||"d"){case"d":case"D":a+=parseInt(c[1],10);break;case"w":case"W":a+=7*parseInt(c[1],10);break;case"m":case"M":r+=parseInt(c[1],10),a=Math.min(a,t.datepicker._getDaysInMonth(s,r));break;case"y":case"Y":s+=parseInt(c[1],10),a=Math.min(a,t.datepicker._getDaysInMonth(s,r))}c=l.exec(i)}return new Date(s,r,a)},r=null==i||""===i?n:"string"==typeof i?s(i):"number"==typeof i?isNaN(i)?n:o(i):new Date(i.getTime());return r=r&&"Invalid Date"===r.toString()?n:r,r&&(r.setHours(0),r.setMinutes(0),r.setSeconds(0),r.setMilliseconds(0)),this._daylightSavingAdjust(r)},_daylightSavingAdjust:function(t){return t?(t.setHours(t.getHours()>12?t.getHours()+2:0),t):null},_setDate:function(t,e,i){var n=!e,o=t.selectedMonth,s=t.selectedYear,r=this._restrictMinMax(t,this._determineDate(t,e,new Date));t.selectedDay=t.currentDay=r.getDate(),t.drawMonth=t.selectedMonth=t.currentMonth=r.getMonth(),t.drawYear=t.selectedYear=t.currentYear=r.getFullYear(),o===t.selectedMonth&&s===t.selectedYear||i||this._notifyChange(t),this._adjustInstDate(t),t.input&&t.input.val(n?"":this._formatDate(t))},_getDate:function(t){var e=!t.currentYear||t.input&&""===t.input.val()?null:this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay));return e},_attachHandlers:function(e){var i=this._get(e,"stepMonths"),n="#"+e.id.replace(/\\\\/g,"\\");e.dpDiv.find("[data-handler]").map(function(){var e={prev:function(){t.datepicker._adjustDate(n,-i,"M")},next:function(){t.datepicker._adjustDate(n,+i,"M")},hide:function(){t.datepicker._hideDatepicker();
},today:function(){t.datepicker._gotoToday(n)},selectDay:function(){return t.datepicker._selectDay(n,+this.getAttribute("data-month"),+this.getAttribute("data-year"),this),!1},selectMonth:function(){return t.datepicker._selectMonthYear(n,this,"M"),!1},selectYear:function(){return t.datepicker._selectMonthYear(n,this,"Y"),!1}};t(this).bind(this.getAttribute("data-event"),e[this.getAttribute("data-handler")])})},_generateHTML:function(t){var e,i,n,o,s,r,a,l,c,u,d,h,p,f,g,m,v,y,b,_,w,k,x,C,D,T,S,I,P,j,N,A,$,E,M,H,L,z,O,q=new Date,R=this._daylightSavingAdjust(new Date(q.getFullYear(),q.getMonth(),q.getDate())),W=this._get(t,"isRTL"),F=this._get(t,"showButtonPanel"),Y=this._get(t,"hideIfNoPrevNext"),B=this._get(t,"navigationAsDateFormat"),X=this._getNumberOfMonths(t),U=this._get(t,"showCurrentAtPos"),K=this._get(t,"stepMonths"),V=1!==X[0]||1!==X[1],Q=this._daylightSavingAdjust(t.currentDay?new Date(t.currentYear,t.currentMonth,t.currentDay):new Date(9999,9,9)),G=this._getMinMaxDate(t,"min"),Z=this._getMinMaxDate(t,"max"),J=t.drawMonth-U,tt=t.drawYear;if(J<0&&(J+=12,tt--),Z)for(e=this._daylightSavingAdjust(new Date(Z.getFullYear(),Z.getMonth()-X[0]*X[1]+1,Z.getDate())),e=G&&e<G?G:e;this._daylightSavingAdjust(new Date(tt,J,1))>e;)J--,J<0&&(J=11,tt--);for(t.drawMonth=J,t.drawYear=tt,i=this._get(t,"prevText"),i=B?this.formatDate(i,this._daylightSavingAdjust(new Date(tt,J-K,1)),this._getFormatConfig(t)):i,n=this._canAdjustMonth(t,-1,tt,J)?"<a class='ui-datepicker-prev ui-corner-all' data-handler='prev' data-event='click' title='"+i+"'><span class='ui-icon ui-icon-circle-triangle-"+(W?"e":"w")+"'>"+i+"</span></a>":Y?"":"<a class='ui-datepicker-prev ui-corner-all ui-state-disabled' title='"+i+"'><span class='ui-icon ui-icon-circle-triangle-"+(W?"e":"w")+"'>"+i+"</span></a>",o=this._get(t,"nextText"),o=B?this.formatDate(o,this._daylightSavingAdjust(new Date(tt,J+K,1)),this._getFormatConfig(t)):o,s=this._canAdjustMonth(t,1,tt,J)?"<a class='ui-datepicker-next ui-corner-all' data-handler='next' data-event='click' title='"+o+"'><span class='ui-icon ui-icon-circle-triangle-"+(W?"w":"e")+"'>"+o+"</span></a>":Y?"":"<a class='ui-datepicker-next ui-corner-all ui-state-disabled' title='"+o+"'><span class='ui-icon ui-icon-circle-triangle-"+(W?"w":"e")+"'>"+o+"</span></a>",r=this._get(t,"currentText"),a=this._get(t,"gotoCurrent")&&t.currentDay?Q:R,r=B?this.formatDate(r,a,this._getFormatConfig(t)):r,l=t.inline?"":"<button type='button' class='ui-datepicker-close ui-state-default ui-priority-primary ui-corner-all' data-handler='hide' data-event='click'>"+this._get(t,"closeText")+"</button>",c=F?"<div class='ui-datepicker-buttonpane ui-widget-content'>"+(W?l:"")+(this._isInRange(t,a)?"<button type='button' class='ui-datepicker-current ui-state-default ui-priority-secondary ui-corner-all' data-handler='today' data-event='click'>"+r+"</button>":"")+(W?"":l)+"</div>":"",u=parseInt(this._get(t,"firstDay"),10),u=isNaN(u)?0:u,d=this._get(t,"showWeek"),h=this._get(t,"dayNames"),p=this._get(t,"dayNamesMin"),f=this._get(t,"monthNames"),g=this._get(t,"monthNamesShort"),m=this._get(t,"beforeShowDay"),v=this._get(t,"showOtherMonths"),y=this._get(t,"selectOtherMonths"),b=this._getDefaultDate(t),_="",k=0;k<X[0];k++){for(x="",this.maxRows=4,C=0;C<X[1];C++){if(D=this._daylightSavingAdjust(new Date(tt,J,t.selectedDay)),T=" ui-corner-all",S="",V){if(S+="<div class='ui-datepicker-group",X[1]>1)switch(C){case 0:S+=" ui-datepicker-group-first",T=" ui-corner-"+(W?"right":"left");break;case X[1]-1:S+=" ui-datepicker-group-last",T=" ui-corner-"+(W?"left":"right");break;default:S+=" ui-datepicker-group-middle",T=""}S+="'>"}for(S+="<div class='ui-datepicker-header ui-widget-header ui-helper-clearfix"+T+"'>"+(/all|left/.test(T)&&0===k?W?s:n:"")+(/all|right/.test(T)&&0===k?W?n:s:"")+this._generateMonthYearHeader(t,J,tt,G,Z,k>0||C>0,f,g)+"</div><table class='ui-datepicker-calendar'><thead><tr>",I=d?"<th class='ui-datepicker-week-col'>"+this._get(t,"weekHeader")+"</th>":"",w=0;w<7;w++)P=(w+u)%7,I+="<th scope='col'"+((w+u+6)%7>=5?" class='ui-datepicker-week-end'":"")+"><span title='"+h[P]+"'>"+p[P]+"</span></th>";for(S+=I+"</tr></thead><tbody>",j=this._getDaysInMonth(tt,J),tt===t.selectedYear&&J===t.selectedMonth&&(t.selectedDay=Math.min(t.selectedDay,j)),N=(this._getFirstDayOfMonth(tt,J)-u+7)%7,A=Math.ceil((N+j)/7),$=V&&this.maxRows>A?this.maxRows:A,this.maxRows=$,E=this._daylightSavingAdjust(new Date(tt,J,1-N)),M=0;M<$;M++){for(S+="<tr>",H=d?"<td class='ui-datepicker-week-col'>"+this._get(t,"calculateWeek")(E)+"</td>":"",w=0;w<7;w++)L=m?m.apply(t.input?t.input[0]:null,[E]):[!0,""],z=E.getMonth()!==J,O=z&&!y||!L[0]||G&&E<G||Z&&E>Z,H+="<td class='"+((w+u+6)%7>=5?" ui-datepicker-week-end":"")+(z?" ui-datepicker-other-month":"")+(E.getTime()===D.getTime()&&J===t.selectedMonth&&t._keyEvent||b.getTime()===E.getTime()&&b.getTime()===D.getTime()?" "+this._dayOverClass:"")+(O?" "+this._unselectableClass+" ui-state-disabled":"")+(z&&!v?"":" "+L[1]+(E.getTime()===Q.getTime()?" "+this._currentClass:"")+(E.getTime()===R.getTime()?" ui-datepicker-today":""))+"'"+(z&&!v||!L[2]?"":" title='"+L[2].replace(/'/g,"&#39;")+"'")+(O?"":" data-handler='selectDay' data-event='click' data-month='"+E.getMonth()+"' data-year='"+E.getFullYear()+"'")+">"+(z&&!v?"&#xa0;":O?"<span class='ui-state-default'>"+E.getDate()+"</span>":"<a class='ui-state-default"+(E.getTime()===R.getTime()?" ui-state-highlight":"")+(E.getTime()===Q.getTime()?" ui-state-active":"")+(z?" ui-priority-secondary":"")+"' href='#'>"+E.getDate()+"</a>")+"</td>",E.setDate(E.getDate()+1),E=this._daylightSavingAdjust(E);S+=H+"</tr>"}J++,J>11&&(J=0,tt++),S+="</tbody></table>"+(V?"</div>"+(X[0]>0&&C===X[1]-1?"<div class='ui-datepicker-row-break'></div>":""):""),x+=S}_+=x}return _+=c,t._keyEvent=!1,_},_generateMonthYearHeader:function(t,e,i,n,o,s,r,a){var l,c,u,d,h,p,f,g,m=this._get(t,"changeMonth"),v=this._get(t,"changeYear"),y=this._get(t,"showMonthAfterYear"),b="<div class='ui-datepicker-title'>",_="";if(s||!m)_+="<span class='ui-datepicker-month'>"+r[e]+"</span>";else{for(l=n&&n.getFullYear()===i,c=o&&o.getFullYear()===i,_+="<select class='ui-datepicker-month' data-handler='selectMonth' data-event='change'>",u=0;u<12;u++)(!l||u>=n.getMonth())&&(!c||u<=o.getMonth())&&(_+="<option value='"+u+"'"+(u===e?" selected='selected'":"")+">"+a[u]+"</option>");_+="</select>"}if(y||(b+=_+(!s&&m&&v?"":"&#xa0;")),!t.yearshtml)if(t.yearshtml="",s||!v)b+="<span class='ui-datepicker-year'>"+i+"</span>";else{for(d=this._get(t,"yearRange").split(":"),h=(new Date).getFullYear(),p=function(t){var e=t.match(/c[+\-].*/)?i+parseInt(t.substring(1),10):t.match(/[+\-].*/)?h+parseInt(t,10):parseInt(t,10);return isNaN(e)?h:e},f=p(d[0]),g=Math.max(f,p(d[1]||"")),f=n?Math.max(f,n.getFullYear()):f,g=o?Math.min(g,o.getFullYear()):g,t.yearshtml+="<select class='ui-datepicker-year' data-handler='selectYear' data-event='change'>";f<=g;f++)t.yearshtml+="<option value='"+f+"'"+(f===i?" selected='selected'":"")+">"+f+"</option>";t.yearshtml+="</select>",b+=t.yearshtml,t.yearshtml=null}return b+=this._get(t,"yearSuffix"),y&&(b+=(!s&&m&&v?"":"&#xa0;")+_),b+="</div>"},_adjustInstDate:function(t,e,i){var n=t.drawYear+("Y"===i?e:0),o=t.drawMonth+("M"===i?e:0),s=Math.min(t.selectedDay,this._getDaysInMonth(n,o))+("D"===i?e:0),r=this._restrictMinMax(t,this._daylightSavingAdjust(new Date(n,o,s)));t.selectedDay=r.getDate(),t.drawMonth=t.selectedMonth=r.getMonth(),t.drawYear=t.selectedYear=r.getFullYear(),"M"!==i&&"Y"!==i||this._notifyChange(t)},_restrictMinMax:function(t,e){var i=this._getMinMaxDate(t,"min"),n=this._getMinMaxDate(t,"max"),o=i&&e<i?i:e;return n&&o>n?n:o},_notifyChange:function(t){var e=this._get(t,"onChangeMonthYear");e&&e.apply(t.input?t.input[0]:null,[t.selectedYear,t.selectedMonth+1,t])},_getNumberOfMonths:function(t){var e=this._get(t,"numberOfMonths");return null==e?[1,1]:"number"==typeof e?[1,e]:e},_getMinMaxDate:function(t,e){return this._determineDate(t,this._get(t,e+"Date"),null)},_getDaysInMonth:function(t,e){return 32-this._daylightSavingAdjust(new Date(t,e,32)).getDate()},_getFirstDayOfMonth:function(t,e){return new Date(t,e,1).getDay()},_canAdjustMonth:function(t,e,i,n){var o=this._getNumberOfMonths(t),s=this._daylightSavingAdjust(new Date(i,n+(e<0?e:o[0]*o[1]),1));return e<0&&s.setDate(this._getDaysInMonth(s.getFullYear(),s.getMonth())),this._isInRange(t,s)},_isInRange:function(t,e){var i,n,o=this._getMinMaxDate(t,"min"),s=this._getMinMaxDate(t,"max"),r=null,a=null,l=this._get(t,"yearRange");return l&&(i=l.split(":"),n=(new Date).getFullYear(),r=parseInt(i[0],10),a=parseInt(i[1],10),i[0].match(/[+\-].*/)&&(r+=n),i[1].match(/[+\-].*/)&&(a+=n)),(!o||e.getTime()>=o.getTime())&&(!s||e.getTime()<=s.getTime())&&(!r||e.getFullYear()>=r)&&(!a||e.getFullYear()<=a)},_getFormatConfig:function(t){var e=this._get(t,"shortYearCutoff");return e="string"!=typeof e?e:(new Date).getFullYear()%100+parseInt(e,10),{shortYearCutoff:e,dayNamesShort:this._get(t,"dayNamesShort"),dayNames:this._get(t,"dayNames"),monthNamesShort:this._get(t,"monthNamesShort"),monthNames:this._get(t,"monthNames")}},_formatDate:function(t,e,i,n){e||(t.currentDay=t.selectedDay,t.currentMonth=t.selectedMonth,t.currentYear=t.selectedYear);var o=e?"object"==typeof e?e:this._daylightSavingAdjust(new Date(n,i,e)):this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay));return this.formatDate(this._get(t,"dateFormat"),o,this._getFormatConfig(t))}}),t.fn.datepicker=function(e){if(!this.length)return this;t.datepicker.initialized||(t(document).mousedown(t.datepicker._checkExternalClick),t.datepicker.initialized=!0),0===t("#"+t.datepicker._mainDivId).length&&t("body").append(t.datepicker.dpDiv);var i=Array.prototype.slice.call(arguments,1);return"string"!=typeof e||"isDisabled"!==e&&"getDate"!==e&&"widget"!==e?"option"===e&&2===arguments.length&&"string"==typeof arguments[1]?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this[0]].concat(i)):this.each(function(){"string"==typeof e?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this].concat(i)):t.datepicker._attachDatepicker(this,e)}):t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this[0]].concat(i))},t.datepicker=new i,t.datepicker.initialized=!1,t.datepicker.uuid=(new Date).getTime(),t.datepicker.version="1.11.4",t.datepicker})},{}],58:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery","./core","./widget","./button","./draggable","./mouse","./position","./resizable"],t):t(jQuery)}(function(t){return t.widget("ui.dialog",{version:"1.11.4",options:{appendTo:"body",autoOpen:!0,buttons:[],closeOnEscape:!0,closeText:"Close",dialogClass:"",draggable:!0,hide:null,height:"auto",maxHeight:null,maxWidth:null,minHeight:150,minWidth:150,modal:!1,position:{my:"center",at:"center",of:window,collision:"fit",using:function(e){var i=t(this).css(e).offset().top;i<0&&t(this).css("top",e.top-i)}},resizable:!0,show:null,title:null,width:300,beforeClose:null,close:null,drag:null,dragStart:null,dragStop:null,focus:null,open:null,resize:null,resizeStart:null,resizeStop:null},sizeRelatedOptions:{buttons:!0,height:!0,maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0,width:!0},resizableRelatedOptions:{maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0},_create:function(){this.originalCss={display:this.element[0].style.display,width:this.element[0].style.width,minHeight:this.element[0].style.minHeight,maxHeight:this.element[0].style.maxHeight,height:this.element[0].style.height},this.originalPosition={parent:this.element.parent(),index:this.element.parent().children().index(this.element)},this.originalTitle=this.element.attr("title"),this.options.title=this.options.title||this.originalTitle,this._createWrapper(),this.element.show().removeAttr("title").addClass("ui-dialog-content ui-widget-content").appendTo(this.uiDialog),this._createTitlebar(),this._createButtonPane(),this.options.draggable&&t.fn.draggable&&this._makeDraggable(),this.options.resizable&&t.fn.resizable&&this._makeResizable(),this._isOpen=!1,this._trackFocus()},_init:function(){this.options.autoOpen&&this.open()},_appendTo:function(){var e=this.options.appendTo;return e&&(e.jquery||e.nodeType)?t(e):this.document.find(e||"body").eq(0)},_destroy:function(){var t,e=this.originalPosition;this._untrackInstance(),this._destroyOverlay(),this.element.removeUniqueId().removeClass("ui-dialog-content ui-widget-content").css(this.originalCss).detach(),this.uiDialog.stop(!0,!0).remove(),this.originalTitle&&this.element.attr("title",this.originalTitle),t=e.parent.children().eq(e.index),t.length&&t[0]!==this.element[0]?t.before(this.element):e.parent.append(this.element)},widget:function(){return this.uiDialog},disable:t.noop,enable:t.noop,close:function(e){var i,n=this;if(this._isOpen&&this._trigger("beforeClose",e)!==!1){if(this._isOpen=!1,this._focusedElement=null,this._destroyOverlay(),this._untrackInstance(),!this.opener.filter(":focusable").focus().length)try{i=this.document[0].activeElement,i&&"body"!==i.nodeName.toLowerCase()&&t(i).blur()}catch(o){}this._hide(this.uiDialog,this.options.hide,function(){n._trigger("close",e)})}},isOpen:function(){return this._isOpen},moveToTop:function(){this._moveToTop()},_moveToTop:function(e,i){var n=!1,o=this.uiDialog.siblings(".ui-front:visible").map(function(){return+t(this).css("z-index")}).get(),s=Math.max.apply(null,o);return s>=+this.uiDialog.css("z-index")&&(this.uiDialog.css("z-index",s+1),n=!0),n&&!i&&this._trigger("focus",e),n},open:function(){var e=this;return this._isOpen?void(this._moveToTop()&&this._focusTabbable()):(this._isOpen=!0,this.opener=t(this.document[0].activeElement),this._size(),this._position(),this._createOverlay(),this._moveToTop(null,!0),this.overlay&&this.overlay.css("z-index",this.uiDialog.css("z-index")-1),this._show(this.uiDialog,this.options.show,function(){e._focusTabbable(),e._trigger("focus")}),this._makeFocusTarget(),void this._trigger("open"))},_focusTabbable:function(){var t=this._focusedElement;t||(t=this.element.find("[autofocus]")),t.length||(t=this.element.find(":tabbable")),t.length||(t=this.uiDialogButtonPane.find(":tabbable")),t.length||(t=this.uiDialogTitlebarClose.filter(":tabbable")),t.length||(t=this.uiDialog),t.eq(0).focus()},_keepFocus:function(e){function i(){var e=this.document[0].activeElement,i=this.uiDialog[0]===e||t.contains(this.uiDialog[0],e);i||this._focusTabbable()}e.preventDefault(),i.call(this),this._delay(i)},_createWrapper:function(){this.uiDialog=t("<div>").addClass("ui-dialog ui-widget ui-widget-content ui-corner-all ui-front "+this.options.dialogClass).hide().attr({tabIndex:-1,role:"dialog"}).appendTo(this._appendTo()),this._on(this.uiDialog,{keydown:function(e){if(this.options.closeOnEscape&&!e.isDefaultPrevented()&&e.keyCode&&e.keyCode===t.ui.keyCode.ESCAPE)return e.preventDefault(),void this.close(e);if(e.keyCode===t.ui.keyCode.TAB&&!e.isDefaultPrevented()){var i=this.uiDialog.find(":tabbable"),n=i.filter(":first"),o=i.filter(":last");e.target!==o[0]&&e.target!==this.uiDialog[0]||e.shiftKey?e.target!==n[0]&&e.target!==this.uiDialog[0]||!e.shiftKey||(this._delay(function(){o.focus()}),e.preventDefault()):(this._delay(function(){n.focus()}),e.preventDefault())}},mousedown:function(t){this._moveToTop(t)&&this._focusTabbable()}}),this.element.find("[aria-describedby]").length||this.uiDialog.attr({"aria-describedby":this.element.uniqueId().attr("id")})},_createTitlebar:function(){var e;this.uiDialogTitlebar=t("<div>").addClass("ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix").prependTo(this.uiDialog),this._on(this.uiDialogTitlebar,{mousedown:function(e){t(e.target).closest(".ui-dialog-titlebar-close")||this.uiDialog.focus()}}),this.uiDialogTitlebarClose=t("<button type='button'></button>").button({label:this.options.closeText,icons:{primary:"ui-icon-closethick"},text:!1}).addClass("ui-dialog-titlebar-close").appendTo(this.uiDialogTitlebar),this._on(this.uiDialogTitlebarClose,{click:function(t){t.preventDefault(),this.close(t)}}),e=t("<span>").uniqueId().addClass("ui-dialog-title").prependTo(this.uiDialogTitlebar),this._title(e),this.uiDialog.attr({"aria-labelledby":e.attr("id")})},_title:function(t){this.options.title||t.html("&#160;"),t.text(this.options.title)},_createButtonPane:function(){this.uiDialogButtonPane=t("<div>").addClass("ui-dialog-buttonpane ui-widget-content ui-helper-clearfix"),this.uiButtonSet=t("<div>").addClass("ui-dialog-buttonset").appendTo(this.uiDialogButtonPane),this._createButtons()},_createButtons:function(){var e=this,i=this.options.buttons;return this.uiDialogButtonPane.remove(),this.uiButtonSet.empty(),t.isEmptyObject(i)||t.isArray(i)&&!i.length?void this.uiDialog.removeClass("ui-dialog-buttons"):(t.each(i,function(i,n){var o,s;n=t.isFunction(n)?{click:n,text:i}:n,n=t.extend({type:"button"},n),o=n.click,n.click=function(){o.apply(e.element[0],arguments)},s={icons:n.icons,text:n.showText},delete n.icons,delete n.showText,t("<button></button>",n).button(s).appendTo(e.uiButtonSet)}),this.uiDialog.addClass("ui-dialog-buttons"),void this.uiDialogButtonPane.appendTo(this.uiDialog))},_makeDraggable:function(){function e(t){return{position:t.position,offset:t.offset}}var i=this,n=this.options;this.uiDialog.draggable({cancel:".ui-dialog-content, .ui-dialog-titlebar-close",handle:".ui-dialog-titlebar",containment:"document",start:function(n,o){t(this).addClass("ui-dialog-dragging"),i._blockFrames(),i._trigger("dragStart",n,e(o))},drag:function(t,n){i._trigger("drag",t,e(n))},stop:function(o,s){var r=s.offset.left-i.document.scrollLeft(),a=s.offset.top-i.document.scrollTop();n.position={my:"left top",at:"left"+(r>=0?"+":"")+r+" top"+(a>=0?"+":"")+a,of:i.window},t(this).removeClass("ui-dialog-dragging"),i._unblockFrames(),i._trigger("dragStop",o,e(s))}})},_makeResizable:function(){function e(t){return{originalPosition:t.originalPosition,originalSize:t.originalSize,position:t.position,size:t.size}}var i=this,n=this.options,o=n.resizable,s=this.uiDialog.css("position"),r="string"==typeof o?o:"n,e,s,w,se,sw,ne,nw";this.uiDialog.resizable({cancel:".ui-dialog-content",containment:"document",alsoResize:this.element,maxWidth:n.maxWidth,maxHeight:n.maxHeight,minWidth:n.minWidth,minHeight:this._minHeight(),handles:r,start:function(n,o){t(this).addClass("ui-dialog-resizing"),i._blockFrames(),i._trigger("resizeStart",n,e(o))},resize:function(t,n){i._trigger("resize",t,e(n))},stop:function(o,s){var r=i.uiDialog.offset(),a=r.left-i.document.scrollLeft(),l=r.top-i.document.scrollTop();n.height=i.uiDialog.height(),n.width=i.uiDialog.width(),n.position={my:"left top",at:"left"+(a>=0?"+":"")+a+" top"+(l>=0?"+":"")+l,of:i.window},t(this).removeClass("ui-dialog-resizing"),i._unblockFrames(),i._trigger("resizeStop",o,e(s))}}).css("position",s)},_trackFocus:function(){this._on(this.widget(),{focusin:function(e){this._makeFocusTarget(),this._focusedElement=t(e.target)}})},_makeFocusTarget:function(){this._untrackInstance(),this._trackingInstances().unshift(this)},_untrackInstance:function(){var e=this._trackingInstances(),i=t.inArray(this,e);i!==-1&&e.splice(i,1)},_trackingInstances:function(){var t=this.document.data("ui-dialog-instances");return t||(t=[],this.document.data("ui-dialog-instances",t)),t},_minHeight:function(){var t=this.options;return"auto"===t.height?t.minHeight:Math.min(t.minHeight,t.height)},_position:function(){var t=this.uiDialog.is(":visible");t||this.uiDialog.show(),this.uiDialog.position(this.options.position),t||this.uiDialog.hide()},_setOptions:function(e){var i=this,n=!1,o={};t.each(e,function(t,e){i._setOption(t,e),t in i.sizeRelatedOptions&&(n=!0),t in i.resizableRelatedOptions&&(o[t]=e)}),n&&(this._size(),this._position()),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option",o)},_setOption:function(t,e){var i,n,o=this.uiDialog;"dialogClass"===t&&o.removeClass(this.options.dialogClass).addClass(e),"disabled"!==t&&(this._super(t,e),"appendTo"===t&&this.uiDialog.appendTo(this._appendTo()),"buttons"===t&&this._createButtons(),"closeText"===t&&this.uiDialogTitlebarClose.button({label:""+e}),"draggable"===t&&(i=o.is(":data(ui-draggable)"),i&&!e&&o.draggable("destroy"),!i&&e&&this._makeDraggable()),"position"===t&&this._position(),"resizable"===t&&(n=o.is(":data(ui-resizable)"),n&&!e&&o.resizable("destroy"),n&&"string"==typeof e&&o.resizable("option","handles",e),n||e===!1||this._makeResizable()),"title"===t&&this._title(this.uiDialogTitlebar.find(".ui-dialog-title")))},_size:function(){var t,e,i,n=this.options;this.element.show().css({width:"auto",minHeight:0,maxHeight:"none",height:0}),n.minWidth>n.width&&(n.width=n.minWidth),t=this.uiDialog.css({height:"auto",width:n.width}).outerHeight(),e=Math.max(0,n.minHeight-t),i="number"==typeof n.maxHeight?Math.max(0,n.maxHeight-t):"none","auto"===n.height?this.element.css({minHeight:e,maxHeight:i,height:"auto"}):this.element.height(Math.max(0,n.height-t)),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option","minHeight",this._minHeight())},_blockFrames:function(){this.iframeBlocks=this.document.find("iframe").map(function(){var e=t(this);return t("<div>").css({position:"absolute",width:e.outerWidth(),height:e.outerHeight()}).appendTo(e.parent()).offset(e.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_allowInteraction:function(e){return!!t(e.target).closest(".ui-dialog").length||!!t(e.target).closest(".ui-datepicker").length},_createOverlay:function(){if(this.options.modal){var e=!0;this._delay(function(){e=!1}),this.document.data("ui-dialog-overlays")||this._on(this.document,{focusin:function(t){e||this._allowInteraction(t)||(t.preventDefault(),this._trackingInstances()[0]._focusTabbable())}}),this.overlay=t("<div>").addClass("ui-widget-overlay ui-front").appendTo(this._appendTo()),this._on(this.overlay,{mousedown:"_keepFocus"}),this.document.data("ui-dialog-overlays",(this.document.data("ui-dialog-overlays")||0)+1)}},_destroyOverlay:function(){if(this.options.modal&&this.overlay){var t=this.document.data("ui-dialog-overlays")-1;t?this.document.data("ui-dialog-overlays",t):this.document.unbind("focusin").removeData("ui-dialog-overlays"),this.overlay.remove(),this.overlay=null}}})})},{}],59:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery","./core","./mouse","./widget"],t):t(jQuery)}(function(t){return t.widget("ui.draggable",t.ui.mouse,{version:"1.11.4",widgetEventPrefix:"drag",options:{addClasses:!0,appendTo:"parent",axis:!1,connectToSortable:!1,containment:!1,cursor:"auto",cursorAt:!1,grid:!1,handle:!1,helper:"original",iframeFix:!1,opacity:!1,refreshPositions:!1,revert:!1,revertDuration:500,scope:"default",scroll:!0,scrollSensitivity:20,scrollSpeed:20,snap:!1,snapMode:"both",snapTolerance:20,stack:!1,zIndex:!1,drag:null,start:null,stop:null},_create:function(){"original"===this.options.helper&&this._setPositionRelative(),this.options.addClasses&&this.element.addClass("ui-draggable"),this.options.disabled&&this.element.addClass("ui-draggable-disabled"),this._setHandleClassName(),this._mouseInit()},_setOption:function(t,e){this._super(t,e),"handle"===t&&(this._removeHandleClassName(),this._setHandleClassName())},_destroy:function(){return(this.helper||this.element).is(".ui-draggable-dragging")?void(this.destroyOnClear=!0):(this.element.removeClass("ui-draggable ui-draggable-dragging ui-draggable-disabled"),this._removeHandleClassName(),void this._mouseDestroy())},_mouseCapture:function(e){var i=this.options;return this._blurActiveElement(e),!(this.helper||i.disabled||t(e.target).closest(".ui-resizable-handle").length>0)&&(this.handle=this._getHandle(e),!!this.handle&&(this._blockFrames(i.iframeFix===!0?"iframe":i.iframeFix),!0))},_blockFrames:function(e){this.iframeBlocks=this.document.find(e).map(function(){var e=t(this);return t("<div>").css("position","absolute").appendTo(e.parent()).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()).offset(e.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_blurActiveElement:function(e){var i=this.document[0];if(this.handleElement.is(e.target))try{i.activeElement&&"body"!==i.activeElement.nodeName.toLowerCase()&&t(i.activeElement).blur()}catch(n){}},_mouseStart:function(e){var i=this.options;return this.helper=this._createHelper(e),this.helper.addClass("ui-draggable-dragging"),this._cacheHelperProportions(),t.ui.ddmanager&&(t.ui.ddmanager.current=this),this._cacheMargins(),this.cssPosition=this.helper.css("position"),this.scrollParent=this.helper.scrollParent(!0),this.offsetParent=this.helper.offsetParent(),this.hasFixedAncestor=this.helper.parents().filter(function(){return"fixed"===t(this).css("position")}).length>0,this.positionAbs=this.element.offset(),this._refreshOffsets(e),this.originalPosition=this.position=this._generatePosition(e,!1),this.originalPageX=e.pageX,this.originalPageY=e.pageY,i.cursorAt&&this._adjustOffsetFromHelper(i.cursorAt),this._setContainment(),this._trigger("start",e)===!1?(this._clear(),!1):(this._cacheHelperProportions(),t.ui.ddmanager&&!i.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this._normalizeRightBottom(),this._mouseDrag(e,!0),t.ui.ddmanager&&t.ui.ddmanager.dragStart(this,e),!0)},_refreshOffsets:function(t){this.offset={top:this.positionAbs.top-this.margins.top,left:this.positionAbs.left-this.margins.left,scroll:!1,parent:this._getParentOffset(),relative:this._getRelativeOffset()},this.offset.click={left:t.pageX-this.offset.left,top:t.pageY-this.offset.top}},_mouseDrag:function(e,i){if(this.hasFixedAncestor&&(this.offset.parent=this._getParentOffset()),this.position=this._generatePosition(e,!0),this.positionAbs=this._convertPositionTo("absolute"),!i){var n=this._uiHash();if(this._trigger("drag",e,n)===!1)return this._mouseUp({}),!1;this.position=n.position}return this.helper[0].style.left=this.position.left+"px",this.helper[0].style.top=this.position.top+"px",t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),!1},_mouseStop:function(e){var i=this,n=!1;return t.ui.ddmanager&&!this.options.dropBehaviour&&(n=t.ui.ddmanager.drop(this,e)),this.dropped&&(n=this.dropped,this.dropped=!1),"invalid"===this.options.revert&&!n||"valid"===this.options.revert&&n||this.options.revert===!0||t.isFunction(this.options.revert)&&this.options.revert.call(this.element,n)?t(this.helper).animate(this.originalPosition,parseInt(this.options.revertDuration,10),function(){i._trigger("stop",e)!==!1&&i._clear()}):this._trigger("stop",e)!==!1&&this._clear(),!1},_mouseUp:function(e){return this._unblockFrames(),t.ui.ddmanager&&t.ui.ddmanager.dragStop(this,e),this.handleElement.is(e.target)&&this.element.focus(),t.ui.mouse.prototype._mouseUp.call(this,e)},cancel:function(){return this.helper.is(".ui-draggable-dragging")?this._mouseUp({}):this._clear(),this},_getHandle:function(e){return!this.options.handle||!!t(e.target).closest(this.element.find(this.options.handle)).length},_setHandleClassName:function(){this.handleElement=this.options.handle?this.element.find(this.options.handle):this.element,this.handleElement.addClass("ui-draggable-handle")},_removeHandleClassName:function(){this.handleElement.removeClass("ui-draggable-handle")},_createHelper:function(e){var i=this.options,n=t.isFunction(i.helper),o=n?t(i.helper.apply(this.element[0],[e])):"clone"===i.helper?this.element.clone().removeAttr("id"):this.element;return o.parents("body").length||o.appendTo("parent"===i.appendTo?this.element[0].parentNode:i.appendTo),n&&o[0]===this.element[0]&&this._setPositionRelative(),o[0]===this.element[0]||/(fixed|absolute)/.test(o.css("position"))||o.css("position","absolute"),o},_setPositionRelative:function(){/^(?:r|a|f)/.test(this.element.css("position"))||(this.element[0].style.position="relative")},_adjustOffsetFromHelper:function(e){"string"==typeof e&&(e=e.split(" ")),t.isArray(e)&&(e={left:+e[0],top:+e[1]||0}),"left"in e&&(this.offset.click.left=e.left+this.margins.left),"right"in e&&(this.offset.click.left=this.helperProportions.width-e.right+this.margins.left),"top"in e&&(this.offset.click.top=e.top+this.margins.top),"bottom"in e&&(this.offset.click.top=this.helperProportions.height-e.bottom+this.margins.top)},_isRootNode:function(t){return/(html|body)/i.test(t.tagName)||t===this.document[0]},_getParentOffset:function(){var e=this.offsetParent.offset(),i=this.document[0];return"absolute"===this.cssPosition&&this.scrollParent[0]!==i&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),this._isRootNode(this.offsetParent[0])&&(e={top:0,left:0}),{top:e.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"!==this.cssPosition)return{top:0,left:0};var t=this.element.position(),e=this._isRootNode(this.scrollParent[0]);return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+(e?0:this.scrollParent.scrollTop()),left:t.left-(parseInt(this.helper.css("left"),10)||0)+(e?0:this.scrollParent.scrollLeft())}},_cacheMargins:function(){this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i,n,o=this.options,s=this.document[0];return this.relativeContainer=null,o.containment?"window"===o.containment?void(this.containment=[t(window).scrollLeft()-this.offset.relative.left-this.offset.parent.left,t(window).scrollTop()-this.offset.relative.top-this.offset.parent.top,t(window).scrollLeft()+t(window).width()-this.helperProportions.width-this.margins.left,t(window).scrollTop()+(t(window).height()||s.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]):"document"===o.containment?void(this.containment=[0,0,t(s).width()-this.helperProportions.width-this.margins.left,(t(s).height()||s.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]):o.containment.constructor===Array?void(this.containment=o.containment):("parent"===o.containment&&(o.containment=this.helper[0].parentNode),i=t(o.containment),n=i[0],void(n&&(e=/(scroll|auto)/.test(i.css("overflow")),this.containment=[(parseInt(i.css("borderLeftWidth"),10)||0)+(parseInt(i.css("paddingLeft"),10)||0),(parseInt(i.css("borderTopWidth"),10)||0)+(parseInt(i.css("paddingTop"),10)||0),(e?Math.max(n.scrollWidth,n.offsetWidth):n.offsetWidth)-(parseInt(i.css("borderRightWidth"),10)||0)-(parseInt(i.css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left-this.margins.right,(e?Math.max(n.scrollHeight,n.offsetHeight):n.offsetHeight)-(parseInt(i.css("borderBottomWidth"),10)||0)-(parseInt(i.css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top-this.margins.bottom],this.relativeContainer=i))):void(this.containment=null)},_convertPositionTo:function(t,e){e||(e=this.position);var i="absolute"===t?1:-1,n=this._isRootNode(this.scrollParent[0]);return{top:e.top+this.offset.relative.top*i+this.offset.parent.top*i-("fixed"===this.cssPosition?-this.offset.scroll.top:n?0:this.offset.scroll.top)*i,left:e.left+this.offset.relative.left*i+this.offset.parent.left*i-("fixed"===this.cssPosition?-this.offset.scroll.left:n?0:this.offset.scroll.left)*i}},_generatePosition:function(t,e){var i,n,o,s,r=this.options,a=this._isRootNode(this.scrollParent[0]),l=t.pageX,c=t.pageY;return a&&this.offset.scroll||(this.offset.scroll={top:this.scrollParent.scrollTop(),left:this.scrollParent.scrollLeft()}),e&&(this.containment&&(this.relativeContainer?(n=this.relativeContainer.offset(),i=[this.containment[0]+n.left,this.containment[1]+n.top,this.containment[2]+n.left,this.containment[3]+n.top]):i=this.containment,
t.pageX-this.offset.click.left<i[0]&&(l=i[0]+this.offset.click.left),t.pageY-this.offset.click.top<i[1]&&(c=i[1]+this.offset.click.top),t.pageX-this.offset.click.left>i[2]&&(l=i[2]+this.offset.click.left),t.pageY-this.offset.click.top>i[3]&&(c=i[3]+this.offset.click.top)),r.grid&&(o=r.grid[1]?this.originalPageY+Math.round((c-this.originalPageY)/r.grid[1])*r.grid[1]:this.originalPageY,c=i?o-this.offset.click.top>=i[1]||o-this.offset.click.top>i[3]?o:o-this.offset.click.top>=i[1]?o-r.grid[1]:o+r.grid[1]:o,s=r.grid[0]?this.originalPageX+Math.round((l-this.originalPageX)/r.grid[0])*r.grid[0]:this.originalPageX,l=i?s-this.offset.click.left>=i[0]||s-this.offset.click.left>i[2]?s:s-this.offset.click.left>=i[0]?s-r.grid[0]:s+r.grid[0]:s),"y"===r.axis&&(l=this.originalPageX),"x"===r.axis&&(c=this.originalPageY)),{top:c-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.offset.scroll.top:a?0:this.offset.scroll.top),left:l-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.offset.scroll.left:a?0:this.offset.scroll.left)}},_clear:function(){this.helper.removeClass("ui-draggable-dragging"),this.helper[0]===this.element[0]||this.cancelHelperRemoval||this.helper.remove(),this.helper=null,this.cancelHelperRemoval=!1,this.destroyOnClear&&this.destroy()},_normalizeRightBottom:function(){"y"!==this.options.axis&&"auto"!==this.helper.css("right")&&(this.helper.width(this.helper.width()),this.helper.css("right","auto")),"x"!==this.options.axis&&"auto"!==this.helper.css("bottom")&&(this.helper.height(this.helper.height()),this.helper.css("bottom","auto"))},_trigger:function(e,i,n){return n=n||this._uiHash(),t.ui.plugin.call(this,e,[i,n,this],!0),/^(drag|start|stop)/.test(e)&&(this.positionAbs=this._convertPositionTo("absolute"),n.offset=this.positionAbs),t.Widget.prototype._trigger.call(this,e,i,n)},plugins:{},_uiHash:function(){return{helper:this.helper,position:this.position,originalPosition:this.originalPosition,offset:this.positionAbs}}}),t.ui.plugin.add("draggable","connectToSortable",{start:function(e,i,n){var o=t.extend({},i,{item:n.element});n.sortables=[],t(n.options.connectToSortable).each(function(){var i=t(this).sortable("instance");i&&!i.options.disabled&&(n.sortables.push(i),i.refreshPositions(),i._trigger("activate",e,o))})},stop:function(e,i,n){var o=t.extend({},i,{item:n.element});n.cancelHelperRemoval=!1,t.each(n.sortables,function(){var t=this;t.isOver?(t.isOver=0,n.cancelHelperRemoval=!0,t.cancelHelperRemoval=!1,t._storedCSS={position:t.placeholder.css("position"),top:t.placeholder.css("top"),left:t.placeholder.css("left")},t._mouseStop(e),t.options.helper=t.options._helper):(t.cancelHelperRemoval=!0,t._trigger("deactivate",e,o))})},drag:function(e,i,n){t.each(n.sortables,function(){var o=!1,s=this;s.positionAbs=n.positionAbs,s.helperProportions=n.helperProportions,s.offset.click=n.offset.click,s._intersectsWith(s.containerCache)&&(o=!0,t.each(n.sortables,function(){return this.positionAbs=n.positionAbs,this.helperProportions=n.helperProportions,this.offset.click=n.offset.click,this!==s&&this._intersectsWith(this.containerCache)&&t.contains(s.element[0],this.element[0])&&(o=!1),o})),o?(s.isOver||(s.isOver=1,n._parent=i.helper.parent(),s.currentItem=i.helper.appendTo(s.element).data("ui-sortable-item",!0),s.options._helper=s.options.helper,s.options.helper=function(){return i.helper[0]},e.target=s.currentItem[0],s._mouseCapture(e,!0),s._mouseStart(e,!0,!0),s.offset.click.top=n.offset.click.top,s.offset.click.left=n.offset.click.left,s.offset.parent.left-=n.offset.parent.left-s.offset.parent.left,s.offset.parent.top-=n.offset.parent.top-s.offset.parent.top,n._trigger("toSortable",e),n.dropped=s.element,t.each(n.sortables,function(){this.refreshPositions()}),n.currentItem=n.element,s.fromOutside=n),s.currentItem&&(s._mouseDrag(e),i.position=s.position)):s.isOver&&(s.isOver=0,s.cancelHelperRemoval=!0,s.options._revert=s.options.revert,s.options.revert=!1,s._trigger("out",e,s._uiHash(s)),s._mouseStop(e,!0),s.options.revert=s.options._revert,s.options.helper=s.options._helper,s.placeholder&&s.placeholder.remove(),i.helper.appendTo(n._parent),n._refreshOffsets(e),i.position=n._generatePosition(e,!0),n._trigger("fromSortable",e),n.dropped=!1,t.each(n.sortables,function(){this.refreshPositions()}))})}}),t.ui.plugin.add("draggable","cursor",{start:function(e,i,n){var o=t("body"),s=n.options;o.css("cursor")&&(s._cursor=o.css("cursor")),o.css("cursor",s.cursor)},stop:function(e,i,n){var o=n.options;o._cursor&&t("body").css("cursor",o._cursor)}}),t.ui.plugin.add("draggable","opacity",{start:function(e,i,n){var o=t(i.helper),s=n.options;o.css("opacity")&&(s._opacity=o.css("opacity")),o.css("opacity",s.opacity)},stop:function(e,i,n){var o=n.options;o._opacity&&t(i.helper).css("opacity",o._opacity)}}),t.ui.plugin.add("draggable","scroll",{start:function(t,e,i){i.scrollParentNotHidden||(i.scrollParentNotHidden=i.helper.scrollParent(!1)),i.scrollParentNotHidden[0]!==i.document[0]&&"HTML"!==i.scrollParentNotHidden[0].tagName&&(i.overflowOffset=i.scrollParentNotHidden.offset())},drag:function(e,i,n){var o=n.options,s=!1,r=n.scrollParentNotHidden[0],a=n.document[0];r!==a&&"HTML"!==r.tagName?(o.axis&&"x"===o.axis||(n.overflowOffset.top+r.offsetHeight-e.pageY<o.scrollSensitivity?r.scrollTop=s=r.scrollTop+o.scrollSpeed:e.pageY-n.overflowOffset.top<o.scrollSensitivity&&(r.scrollTop=s=r.scrollTop-o.scrollSpeed)),o.axis&&"y"===o.axis||(n.overflowOffset.left+r.offsetWidth-e.pageX<o.scrollSensitivity?r.scrollLeft=s=r.scrollLeft+o.scrollSpeed:e.pageX-n.overflowOffset.left<o.scrollSensitivity&&(r.scrollLeft=s=r.scrollLeft-o.scrollSpeed))):(o.axis&&"x"===o.axis||(e.pageY-t(a).scrollTop()<o.scrollSensitivity?s=t(a).scrollTop(t(a).scrollTop()-o.scrollSpeed):t(window).height()-(e.pageY-t(a).scrollTop())<o.scrollSensitivity&&(s=t(a).scrollTop(t(a).scrollTop()+o.scrollSpeed))),o.axis&&"y"===o.axis||(e.pageX-t(a).scrollLeft()<o.scrollSensitivity?s=t(a).scrollLeft(t(a).scrollLeft()-o.scrollSpeed):t(window).width()-(e.pageX-t(a).scrollLeft())<o.scrollSensitivity&&(s=t(a).scrollLeft(t(a).scrollLeft()+o.scrollSpeed)))),s!==!1&&t.ui.ddmanager&&!o.dropBehaviour&&t.ui.ddmanager.prepareOffsets(n,e)}}),t.ui.plugin.add("draggable","snap",{start:function(e,i,n){var o=n.options;n.snapElements=[],t(o.snap.constructor!==String?o.snap.items||":data(ui-draggable)":o.snap).each(function(){var e=t(this),i=e.offset();this!==n.element[0]&&n.snapElements.push({item:this,width:e.outerWidth(),height:e.outerHeight(),top:i.top,left:i.left})})},drag:function(e,i,n){var o,s,r,a,l,c,u,d,h,p,f=n.options,g=f.snapTolerance,m=i.offset.left,v=m+n.helperProportions.width,y=i.offset.top,b=y+n.helperProportions.height;for(h=n.snapElements.length-1;h>=0;h--)l=n.snapElements[h].left-n.margins.left,c=l+n.snapElements[h].width,u=n.snapElements[h].top-n.margins.top,d=u+n.snapElements[h].height,v<l-g||m>c+g||b<u-g||y>d+g||!t.contains(n.snapElements[h].item.ownerDocument,n.snapElements[h].item)?(n.snapElements[h].snapping&&n.options.snap.release&&n.options.snap.release.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[h].item})),n.snapElements[h].snapping=!1):("inner"!==f.snapMode&&(o=Math.abs(u-b)<=g,s=Math.abs(d-y)<=g,r=Math.abs(l-v)<=g,a=Math.abs(c-m)<=g,o&&(i.position.top=n._convertPositionTo("relative",{top:u-n.helperProportions.height,left:0}).top),s&&(i.position.top=n._convertPositionTo("relative",{top:d,left:0}).top),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l-n.helperProportions.width}).left),a&&(i.position.left=n._convertPositionTo("relative",{top:0,left:c}).left)),p=o||s||r||a,"outer"!==f.snapMode&&(o=Math.abs(u-y)<=g,s=Math.abs(d-b)<=g,r=Math.abs(l-m)<=g,a=Math.abs(c-v)<=g,o&&(i.position.top=n._convertPositionTo("relative",{top:u,left:0}).top),s&&(i.position.top=n._convertPositionTo("relative",{top:d-n.helperProportions.height,left:0}).top),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l}).left),a&&(i.position.left=n._convertPositionTo("relative",{top:0,left:c-n.helperProportions.width}).left)),!n.snapElements[h].snapping&&(o||s||r||a||p)&&n.options.snap.snap&&n.options.snap.snap.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[h].item})),n.snapElements[h].snapping=o||s||r||a||p)}}),t.ui.plugin.add("draggable","stack",{start:function(e,i,n){var o,s=n.options,r=t.makeArray(t(s.stack)).sort(function(e,i){return(parseInt(t(e).css("zIndex"),10)||0)-(parseInt(t(i).css("zIndex"),10)||0)});r.length&&(o=parseInt(t(r[0]).css("zIndex"),10)||0,t(r).each(function(e){t(this).css("zIndex",o+e)}),this.css("zIndex",o+r.length))}}),t.ui.plugin.add("draggable","zIndex",{start:function(e,i,n){var o=t(i.helper),s=n.options;o.css("zIndex")&&(s._zIndex=o.css("zIndex")),o.css("zIndex",s.zIndex)},stop:function(e,i,n){var o=n.options;o._zIndex&&t(i.helper).css("zIndex",o._zIndex)}}),t.ui.draggable})},{}],60:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery","./core","./widget","./mouse","./draggable"],t):t(jQuery)}(function(t){return t.widget("ui.droppable",{version:"1.11.4",widgetEventPrefix:"drop",options:{accept:"*",activeClass:!1,addClasses:!0,greedy:!1,hoverClass:!1,scope:"default",tolerance:"intersect",activate:null,deactivate:null,drop:null,out:null,over:null},_create:function(){var e,i=this.options,n=i.accept;this.isover=!1,this.isout=!0,this.accept=t.isFunction(n)?n:function(t){return t.is(n)},this.proportions=function(){return arguments.length?void(e=arguments[0]):e?e:e={width:this.element[0].offsetWidth,height:this.element[0].offsetHeight}},this._addToManager(i.scope),i.addClasses&&this.element.addClass("ui-droppable")},_addToManager:function(e){t.ui.ddmanager.droppables[e]=t.ui.ddmanager.droppables[e]||[],t.ui.ddmanager.droppables[e].push(this)},_splice:function(t){for(var e=0;e<t.length;e++)t[e]===this&&t.splice(e,1)},_destroy:function(){var e=t.ui.ddmanager.droppables[this.options.scope];this._splice(e),this.element.removeClass("ui-droppable ui-droppable-disabled")},_setOption:function(e,i){if("accept"===e)this.accept=t.isFunction(i)?i:function(t){return t.is(i)};else if("scope"===e){var n=t.ui.ddmanager.droppables[this.options.scope];this._splice(n),this._addToManager(i)}this._super(e,i)},_activate:function(e){var i=t.ui.ddmanager.current;this.options.activeClass&&this.element.addClass(this.options.activeClass),i&&this._trigger("activate",e,this.ui(i))},_deactivate:function(e){var i=t.ui.ddmanager.current;this.options.activeClass&&this.element.removeClass(this.options.activeClass),i&&this._trigger("deactivate",e,this.ui(i))},_over:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this.options.hoverClass&&this.element.addClass(this.options.hoverClass),this._trigger("over",e,this.ui(i)))},_out:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("out",e,this.ui(i)))},_drop:function(e,i){var n=i||t.ui.ddmanager.current,o=!1;return!(!n||(n.currentItem||n.element)[0]===this.element[0])&&(this.element.find(":data(ui-droppable)").not(".ui-draggable-dragging").each(function(){var i=t(this).droppable("instance");if(i.options.greedy&&!i.options.disabled&&i.options.scope===n.options.scope&&i.accept.call(i.element[0],n.currentItem||n.element)&&t.ui.intersect(n,t.extend(i,{offset:i.element.offset()}),i.options.tolerance,e))return o=!0,!1}),!o&&(!!this.accept.call(this.element[0],n.currentItem||n.element)&&(this.options.activeClass&&this.element.removeClass(this.options.activeClass),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("drop",e,this.ui(n)),this.element)))},ui:function(t){return{draggable:t.currentItem||t.element,helper:t.helper,position:t.position,offset:t.positionAbs}}}),t.ui.intersect=function(){function t(t,e,i){return t>=e&&t<e+i}return function(e,i,n,o){if(!i.offset)return!1;var s=(e.positionAbs||e.position.absolute).left+e.margins.left,r=(e.positionAbs||e.position.absolute).top+e.margins.top,a=s+e.helperProportions.width,l=r+e.helperProportions.height,c=i.offset.left,u=i.offset.top,d=c+i.proportions().width,h=u+i.proportions().height;switch(n){case"fit":return c<=s&&a<=d&&u<=r&&l<=h;case"intersect":return c<s+e.helperProportions.width/2&&a-e.helperProportions.width/2<d&&u<r+e.helperProportions.height/2&&l-e.helperProportions.height/2<h;case"pointer":return t(o.pageY,u,i.proportions().height)&&t(o.pageX,c,i.proportions().width);case"touch":return(r>=u&&r<=h||l>=u&&l<=h||r<u&&l>h)&&(s>=c&&s<=d||a>=c&&a<=d||s<c&&a>d);default:return!1}}}(),t.ui.ddmanager={current:null,droppables:{"default":[]},prepareOffsets:function(e,i){var n,o,s=t.ui.ddmanager.droppables[e.options.scope]||[],r=i?i.type:null,a=(e.currentItem||e.element).find(":data(ui-droppable)").addBack();t:for(n=0;n<s.length;n++)if(!(s[n].options.disabled||e&&!s[n].accept.call(s[n].element[0],e.currentItem||e.element))){for(o=0;o<a.length;o++)if(a[o]===s[n].element[0]){s[n].proportions().height=0;continue t}s[n].visible="none"!==s[n].element.css("display"),s[n].visible&&("mousedown"===r&&s[n]._activate.call(s[n],i),s[n].offset=s[n].element.offset(),s[n].proportions({width:s[n].element[0].offsetWidth,height:s[n].element[0].offsetHeight}))}},drop:function(e,i){var n=!1;return t.each((t.ui.ddmanager.droppables[e.options.scope]||[]).slice(),function(){this.options&&(!this.options.disabled&&this.visible&&t.ui.intersect(e,this,this.options.tolerance,i)&&(n=this._drop.call(this,i)||n),!this.options.disabled&&this.visible&&this.accept.call(this.element[0],e.currentItem||e.element)&&(this.isout=!0,this.isover=!1,this._deactivate.call(this,i)))}),n},dragStart:function(e,i){e.element.parentsUntil("body").bind("scroll.droppable",function(){e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)})},drag:function(e,i){e.options.refreshPositions&&t.ui.ddmanager.prepareOffsets(e,i),t.each(t.ui.ddmanager.droppables[e.options.scope]||[],function(){if(!this.options.disabled&&!this.greedyChild&&this.visible){var n,o,s,r=t.ui.intersect(e,this,this.options.tolerance,i),a=!r&&this.isover?"isout":r&&!this.isover?"isover":null;a&&(this.options.greedy&&(o=this.options.scope,s=this.element.parents(":data(ui-droppable)").filter(function(){return t(this).droppable("instance").options.scope===o}),s.length&&(n=t(s[0]).droppable("instance"),n.greedyChild="isover"===a)),n&&"isover"===a&&(n.isover=!1,n.isout=!0,n._out.call(n,i)),this[a]=!0,this["isout"===a?"isover":"isout"]=!1,this["isover"===a?"_over":"_out"].call(this,i),n&&"isout"===a&&(n.isout=!1,n.isover=!0,n._over.call(n,i)))}})},dragStop:function(e,i){e.element.parentsUntil("body").unbind("scroll.droppable"),e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)}},t.ui.droppable})},{}],61:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery","./widget"],t):t(jQuery)}(function(t){var e=!1;return t(document).mouseup(function(){e=!1}),t.widget("ui.mouse",{version:"1.11.4",options:{cancel:"input,textarea,button,select,option",distance:1,delay:0},_mouseInit:function(){var e=this;this.element.bind("mousedown."+this.widgetName,function(t){return e._mouseDown(t)}).bind("click."+this.widgetName,function(i){if(!0===t.data(i.target,e.widgetName+".preventClickEvent"))return t.removeData(i.target,e.widgetName+".preventClickEvent"),i.stopImmediatePropagation(),!1}),this.started=!1},_mouseDestroy:function(){this.element.unbind("."+this.widgetName),this._mouseMoveDelegate&&this.document.unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(i){if(!e){this._mouseMoved=!1,this._mouseStarted&&this._mouseUp(i),this._mouseDownEvent=i;var n=this,o=1===i.which,s=!("string"!=typeof this.options.cancel||!i.target.nodeName)&&t(i.target).closest(this.options.cancel).length;return!(o&&!s&&this._mouseCapture(i))||(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){n.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(i)&&this._mouseDelayMet(i)&&(this._mouseStarted=this._mouseStart(i)!==!1,!this._mouseStarted)?(i.preventDefault(),!0):(!0===t.data(i.target,this.widgetName+".preventClickEvent")&&t.removeData(i.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(t){return n._mouseMove(t)},this._mouseUpDelegate=function(t){return n._mouseUp(t)},this.document.bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+this.widgetName,this._mouseUpDelegate),i.preventDefault(),e=!0,!0))}},_mouseMove:function(e){if(this._mouseMoved){if(t.ui.ie&&(!document.documentMode||document.documentMode<9)&&!e.button)return this._mouseUp(e);if(!e.which)return this._mouseUp(e)}return(e.which||e.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(e),e.preventDefault()):(this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=this._mouseStart(this._mouseDownEvent,e)!==!1,this._mouseStarted?this._mouseDrag(e):this._mouseUp(e)),!this._mouseStarted)},_mouseUp:function(i){return this.document.unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,i.target===this._mouseDownEvent.target&&t.data(i.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(i)),e=!1,!1},_mouseDistanceMet:function(t){return Math.max(Math.abs(this._mouseDownEvent.pageX-t.pageX),Math.abs(this._mouseDownEvent.pageY-t.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}})})},{}],62:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(function(t){return function(){function e(t,e,i){return[parseFloat(t[0])*(p.test(t[0])?e/100:1),parseFloat(t[1])*(p.test(t[1])?i/100:1)]}function i(e,i){return parseInt(t.css(e,i),10)||0}function n(e){var i=e[0];return 9===i.nodeType?{width:e.width(),height:e.height(),offset:{top:0,left:0}}:t.isWindow(i)?{width:e.width(),height:e.height(),offset:{top:e.scrollTop(),left:e.scrollLeft()}}:i.preventDefault?{width:0,height:0,offset:{top:i.pageY,left:i.pageX}}:{width:e.outerWidth(),height:e.outerHeight(),offset:e.offset()}}t.ui=t.ui||{};var o,s,r=Math.max,a=Math.abs,l=Math.round,c=/left|center|right/,u=/top|center|bottom/,d=/[\+\-]\d+(\.[\d]+)?%?/,h=/^\w+/,p=/%$/,f=t.fn.position;t.position={scrollbarWidth:function(){if(void 0!==o)return o;var e,i,n=t("<div style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'><div style='height:100px;width:auto;'></div></div>"),s=n.children()[0];return t("body").append(n),e=s.offsetWidth,n.css("overflow","scroll"),i=s.offsetWidth,e===i&&(i=n[0].clientWidth),n.remove(),o=e-i},getScrollInfo:function(e){var i=e.isWindow||e.isDocument?"":e.element.css("overflow-x"),n=e.isWindow||e.isDocument?"":e.element.css("overflow-y"),o="scroll"===i||"auto"===i&&e.width<e.element[0].scrollWidth,s="scroll"===n||"auto"===n&&e.height<e.element[0].scrollHeight;return{width:s?t.position.scrollbarWidth():0,height:o?t.position.scrollbarWidth():0}},getWithinInfo:function(e){var i=t(e||window),n=t.isWindow(i[0]),o=!!i[0]&&9===i[0].nodeType;return{element:i,isWindow:n,isDocument:o,offset:i.offset()||{left:0,top:0},scrollLeft:i.scrollLeft(),scrollTop:i.scrollTop(),width:n||o?i.width():i.outerWidth(),height:n||o?i.height():i.outerHeight()}}},t.fn.position=function(o){if(!o||!o.of)return f.apply(this,arguments);o=t.extend({},o);var p,g,m,v,y,b,_=t(o.of),w=t.position.getWithinInfo(o.within),k=t.position.getScrollInfo(w),x=(o.collision||"flip").split(" "),C={};return b=n(_),_[0].preventDefault&&(o.at="left top"),g=b.width,m=b.height,v=b.offset,y=t.extend({},v),t.each(["my","at"],function(){var t,e,i=(o[this]||"").split(" ");1===i.length&&(i=c.test(i[0])?i.concat(["center"]):u.test(i[0])?["center"].concat(i):["center","center"]),i[0]=c.test(i[0])?i[0]:"center",i[1]=u.test(i[1])?i[1]:"center",t=d.exec(i[0]),e=d.exec(i[1]),C[this]=[t?t[0]:0,e?e[0]:0],o[this]=[h.exec(i[0])[0],h.exec(i[1])[0]]}),1===x.length&&(x[1]=x[0]),"right"===o.at[0]?y.left+=g:"center"===o.at[0]&&(y.left+=g/2),"bottom"===o.at[1]?y.top+=m:"center"===o.at[1]&&(y.top+=m/2),p=e(C.at,g,m),y.left+=p[0],y.top+=p[1],this.each(function(){var n,c,u=t(this),d=u.outerWidth(),h=u.outerHeight(),f=i(this,"marginLeft"),b=i(this,"marginTop"),D=d+f+i(this,"marginRight")+k.width,T=h+b+i(this,"marginBottom")+k.height,S=t.extend({},y),I=e(C.my,u.outerWidth(),u.outerHeight());"right"===o.my[0]?S.left-=d:"center"===o.my[0]&&(S.left-=d/2),"bottom"===o.my[1]?S.top-=h:"center"===o.my[1]&&(S.top-=h/2),S.left+=I[0],S.top+=I[1],s||(S.left=l(S.left),S.top=l(S.top)),n={marginLeft:f,marginTop:b},t.each(["left","top"],function(e,i){t.ui.position[x[e]]&&t.ui.position[x[e]][i](S,{targetWidth:g,targetHeight:m,elemWidth:d,elemHeight:h,collisionPosition:n,collisionWidth:D,collisionHeight:T,offset:[p[0]+I[0],p[1]+I[1]],my:o.my,at:o.at,within:w,elem:u})}),o.using&&(c=function(t){var e=v.left-S.left,i=e+g-d,n=v.top-S.top,s=n+m-h,l={target:{element:_,left:v.left,top:v.top,width:g,height:m},element:{element:u,left:S.left,top:S.top,width:d,height:h},horizontal:i<0?"left":e>0?"right":"center",vertical:s<0?"top":n>0?"bottom":"middle"};g<d&&a(e+i)<g&&(l.horizontal="center"),m<h&&a(n+s)<m&&(l.vertical="middle"),r(a(e),a(i))>r(a(n),a(s))?l.important="horizontal":l.important="vertical",o.using.call(this,t,l)}),u.offset(t.extend(S,{using:c}))})},t.ui.position={fit:{left:function(t,e){var i,n=e.within,o=n.isWindow?n.scrollLeft:n.offset.left,s=n.width,a=t.left-e.collisionPosition.marginLeft,l=o-a,c=a+e.collisionWidth-s-o;e.collisionWidth>s?l>0&&c<=0?(i=t.left+l+e.collisionWidth-s-o,t.left+=l-i):c>0&&l<=0?t.left=o:l>c?t.left=o+s-e.collisionWidth:t.left=o:l>0?t.left+=l:c>0?t.left-=c:t.left=r(t.left-a,t.left)},top:function(t,e){var i,n=e.within,o=n.isWindow?n.scrollTop:n.offset.top,s=e.within.height,a=t.top-e.collisionPosition.marginTop,l=o-a,c=a+e.collisionHeight-s-o;e.collisionHeight>s?l>0&&c<=0?(i=t.top+l+e.collisionHeight-s-o,t.top+=l-i):c>0&&l<=0?t.top=o:l>c?t.top=o+s-e.collisionHeight:t.top=o:l>0?t.top+=l:c>0?t.top-=c:t.top=r(t.top-a,t.top)}},flip:{left:function(t,e){var i,n,o=e.within,s=o.offset.left+o.scrollLeft,r=o.width,l=o.isWindow?o.scrollLeft:o.offset.left,c=t.left-e.collisionPosition.marginLeft,u=c-l,d=c+e.collisionWidth-r-l,h="left"===e.my[0]?-e.elemWidth:"right"===e.my[0]?e.elemWidth:0,p="left"===e.at[0]?e.targetWidth:"right"===e.at[0]?-e.targetWidth:0,f=-2*e.offset[0];u<0?(i=t.left+h+p+f+e.collisionWidth-r-s,(i<0||i<a(u))&&(t.left+=h+p+f)):d>0&&(n=t.left-e.collisionPosition.marginLeft+h+p+f-l,(n>0||a(n)<d)&&(t.left+=h+p+f))},top:function(t,e){var i,n,o=e.within,s=o.offset.top+o.scrollTop,r=o.height,l=o.isWindow?o.scrollTop:o.offset.top,c=t.top-e.collisionPosition.marginTop,u=c-l,d=c+e.collisionHeight-r-l,h="top"===e.my[1],p=h?-e.elemHeight:"bottom"===e.my[1]?e.elemHeight:0,f="top"===e.at[1]?e.targetHeight:"bottom"===e.at[1]?-e.targetHeight:0,g=-2*e.offset[1];u<0?(n=t.top+p+f+g+e.collisionHeight-r-s,(n<0||n<a(u))&&(t.top+=p+f+g)):d>0&&(i=t.top-e.collisionPosition.marginTop+p+f+g-l,(i>0||a(i)<d)&&(t.top+=p+f+g))}},flipfit:{left:function(){t.ui.position.flip.left.apply(this,arguments),t.ui.position.fit.left.apply(this,arguments)},top:function(){t.ui.position.flip.top.apply(this,arguments),t.ui.position.fit.top.apply(this,arguments)}}},function(){var e,i,n,o,r,a=document.getElementsByTagName("body")[0],l=document.createElement("div");e=document.createElement(a?"div":"body"),n={visibility:"hidden",width:0,height:0,border:0,margin:0,background:"none"},a&&t.extend(n,{position:"absolute",left:"-1000px",top:"-1000px"});for(r in n)e.style[r]=n[r];e.appendChild(l),i=a||document.documentElement,i.insertBefore(e,i.firstChild),l.style.cssText="position: absolute; left: 10.7432222px;",o=t(l).offset().left,s=o>10&&o<11,e.innerHTML="",i.removeChild(e)}()}(),t.ui.position})},{}],63:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery","./core","./mouse","./widget"],t):t(jQuery)}(function(t){return t.widget("ui.resizable",t.ui.mouse,{version:"1.11.4",widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:90,resize:null,start:null,stop:null},_num:function(t){return parseInt(t,10)||0},_isNumber:function(t){return!isNaN(parseInt(t,10))},_hasScroll:function(e,i){if("hidden"===t(e).css("overflow"))return!1;var n=i&&"left"===i?"scrollLeft":"scrollTop",o=!1;return e[n]>0||(e[n]=1,o=e[n]>0,e[n]=0,o)},_create:function(){var e,i,n,o,s,r=this,a=this.options;if(this.element.addClass("ui-resizable"),t.extend(this,{_aspectRatio:!!a.aspectRatio,aspectRatio:a.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],_helper:a.helper||a.ghost||a.animate?a.helper||"ui-resizable-helper":null}),this.element[0].nodeName.match(/^(canvas|textarea|input|select|button|img)$/i)&&(this.element.wrap(t("<div class='ui-wrapper' style='overflow: hidden;'></div>").css({position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("ui-resizable",this.element.resizable("instance")),this.elementIsWrapper=!0,this.element.css({marginLeft:this.originalElement.css("marginLeft"),marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom")}),this.originalElement.css({marginLeft:0,marginTop:0,marginRight:0,marginBottom:0}),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,display:"block"})),this.originalElement.css({margin:this.originalElement.css("margin")}),this._proportionallyResize()),this.handles=a.handles||(t(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se"),this._handles=t(),this.handles.constructor===String)for("all"===this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw"),e=this.handles.split(","),this.handles={},i=0;i<e.length;i++)n=t.trim(e[i]),s="ui-resizable-"+n,o=t("<div class='ui-resizable-handle "+s+"'></div>"),o.css({zIndex:a.zIndex}),"se"===n&&o.addClass("ui-icon ui-icon-gripsmall-diagonal-se"),this.handles[n]=".ui-resizable-"+n,this.element.append(o);this._renderAxis=function(e){var i,n,o,s;e=e||this.element;for(i in this.handles)this.handles[i].constructor===String?this.handles[i]=this.element.children(this.handles[i]).first().show():(this.handles[i].jquery||this.handles[i].nodeType)&&(this.handles[i]=t(this.handles[i]),this._on(this.handles[i],{mousedown:r._mouseDown})),this.elementIsWrapper&&this.originalElement[0].nodeName.match(/^(textarea|input|select|button)$/i)&&(n=t(this.handles[i],this.element),s=/sw|ne|nw|se|n|s/.test(i)?n.outerHeight():n.outerWidth(),o=["padding",/ne|nw|n/.test(i)?"Top":/se|sw|s/.test(i)?"Bottom":/^e$/.test(i)?"Right":"Left"].join(""),e.css(o,s),this._proportionallyResize()),this._handles=this._handles.add(this.handles[i])},this._renderAxis(this.element),this._handles=this._handles.add(this.element.find(".ui-resizable-handle")),this._handles.disableSelection(),this._handles.mouseover(function(){r.resizing||(this.className&&(o=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)),r.axis=o&&o[1]?o[1]:"se")}),a.autoHide&&(this._handles.hide(),t(this.element).addClass("ui-resizable-autohide").mouseenter(function(){a.disabled||(t(this).removeClass("ui-resizable-autohide"),r._handles.show())}).mouseleave(function(){a.disabled||r.resizing||(t(this).addClass("ui-resizable-autohide"),r._handles.hide())})),this._mouseInit()},_destroy:function(){this._mouseDestroy();var e,i=function(e){t(e).removeClass("ui-resizable ui-resizable-disabled ui-resizable-resizing").removeData("resizable").removeData("ui-resizable").unbind(".resizable").find(".ui-resizable-handle").remove()};return this.elementIsWrapper&&(i(this.element),e=this.element,this.originalElement.css({position:e.css("position"),width:e.outerWidth(),height:e.outerHeight(),top:e.css("top"),left:e.css("left")}).insertAfter(e),e.remove()),this.originalElement.css("resize",this.originalResizeStyle),i(this.originalElement),this},_mouseCapture:function(e){var i,n,o=!1;for(i in this.handles)n=t(this.handles[i])[0],(n===e.target||t.contains(n,e.target))&&(o=!0);return!this.options.disabled&&o},_mouseStart:function(e){var i,n,o,s=this.options,r=this.element;return this.resizing=!0,this._renderProxy(),i=this._num(this.helper.css("left")),n=this._num(this.helper.css("top")),s.containment&&(i+=t(s.containment).scrollLeft()||0,n+=t(s.containment).scrollTop()||0),this.offset=this.helper.offset(),this.position={left:i,top:n},this.size=this._helper?{width:this.helper.width(),height:this.helper.height()}:{width:r.width(),height:r.height()},this.originalSize=this._helper?{width:r.outerWidth(),height:r.outerHeight()}:{width:r.width(),height:r.height()},this.sizeDiff={width:r.outerWidth()-r.width(),height:r.outerHeight()-r.height()},this.originalPosition={left:i,top:n},this.originalMousePosition={left:e.pageX,top:e.pageY},this.aspectRatio="number"==typeof s.aspectRatio?s.aspectRatio:this.originalSize.width/this.originalSize.height||1,o=t(".ui-resizable-"+this.axis).css("cursor"),t("body").css("cursor","auto"===o?this.axis+"-resize":o),r.addClass("ui-resizable-resizing"),this._propagate("start",e),!0},_mouseDrag:function(e){var i,n,o=this.originalMousePosition,s=this.axis,r=e.pageX-o.left||0,a=e.pageY-o.top||0,l=this._change[s];return this._updatePrevProperties(),!!l&&(i=l.apply(this,[e,r,a]),this._updateVirtualBoundaries(e.shiftKey),(this._aspectRatio||e.shiftKey)&&(i=this._updateRatio(i,e)),i=this._respectSize(i,e),this._updateCache(i),this._propagate("resize",e),n=this._applyChanges(),!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize(),t.isEmptyObject(n)||(this._updatePrevProperties(),this._trigger("resize",e,this.ui()),this._applyChanges()),!1)},_mouseStop:function(e){this.resizing=!1;var i,n,o,s,r,a,l,c=this.options,u=this;return this._helper&&(i=this._proportionallyResizeElements,n=i.length&&/textarea/i.test(i[0].nodeName),o=n&&this._hasScroll(i[0],"left")?0:u.sizeDiff.height,s=n?0:u.sizeDiff.width,r={width:u.helper.width()-s,height:u.helper.height()-o},a=parseInt(u.element.css("left"),10)+(u.position.left-u.originalPosition.left)||null,l=parseInt(u.element.css("top"),10)+(u.position.top-u.originalPosition.top)||null,c.animate||this.element.css(t.extend(r,{top:l,left:a})),u.helper.height(u.size.height),u.helper.width(u.size.width),this._helper&&!c.animate&&this._proportionallyResize()),t("body").css("cursor","auto"),this.element.removeClass("ui-resizable-resizing"),this._propagate("stop",e),this._helper&&this.helper.remove(),!1},_updatePrevProperties:function(){this.prevPosition={top:this.position.top,left:this.position.left},this.prevSize={width:this.size.width,height:this.size.height}},_applyChanges:function(){var t={};return this.position.top!==this.prevPosition.top&&(t.top=this.position.top+"px"),this.position.left!==this.prevPosition.left&&(t.left=this.position.left+"px"),this.size.width!==this.prevSize.width&&(t.width=this.size.width+"px"),this.size.height!==this.prevSize.height&&(t.height=this.size.height+"px"),
this.helper.css(t),t},_updateVirtualBoundaries:function(t){var e,i,n,o,s,r=this.options;s={minWidth:this._isNumber(r.minWidth)?r.minWidth:0,maxWidth:this._isNumber(r.maxWidth)?r.maxWidth:1/0,minHeight:this._isNumber(r.minHeight)?r.minHeight:0,maxHeight:this._isNumber(r.maxHeight)?r.maxHeight:1/0},(this._aspectRatio||t)&&(e=s.minHeight*this.aspectRatio,n=s.minWidth/this.aspectRatio,i=s.maxHeight*this.aspectRatio,o=s.maxWidth/this.aspectRatio,e>s.minWidth&&(s.minWidth=e),n>s.minHeight&&(s.minHeight=n),i<s.maxWidth&&(s.maxWidth=i),o<s.maxHeight&&(s.maxHeight=o)),this._vBoundaries=s},_updateCache:function(t){this.offset=this.helper.offset(),this._isNumber(t.left)&&(this.position.left=t.left),this._isNumber(t.top)&&(this.position.top=t.top),this._isNumber(t.height)&&(this.size.height=t.height),this._isNumber(t.width)&&(this.size.width=t.width)},_updateRatio:function(t){var e=this.position,i=this.size,n=this.axis;return this._isNumber(t.height)?t.width=t.height*this.aspectRatio:this._isNumber(t.width)&&(t.height=t.width/this.aspectRatio),"sw"===n&&(t.left=e.left+(i.width-t.width),t.top=null),"nw"===n&&(t.top=e.top+(i.height-t.height),t.left=e.left+(i.width-t.width)),t},_respectSize:function(t){var e=this._vBoundaries,i=this.axis,n=this._isNumber(t.width)&&e.maxWidth&&e.maxWidth<t.width,o=this._isNumber(t.height)&&e.maxHeight&&e.maxHeight<t.height,s=this._isNumber(t.width)&&e.minWidth&&e.minWidth>t.width,r=this._isNumber(t.height)&&e.minHeight&&e.minHeight>t.height,a=this.originalPosition.left+this.originalSize.width,l=this.position.top+this.size.height,c=/sw|nw|w/.test(i),u=/nw|ne|n/.test(i);return s&&(t.width=e.minWidth),r&&(t.height=e.minHeight),n&&(t.width=e.maxWidth),o&&(t.height=e.maxHeight),s&&c&&(t.left=a-e.minWidth),n&&c&&(t.left=a-e.maxWidth),r&&u&&(t.top=l-e.minHeight),o&&u&&(t.top=l-e.maxHeight),t.width||t.height||t.left||!t.top?t.width||t.height||t.top||!t.left||(t.left=null):t.top=null,t},_getPaddingPlusBorderDimensions:function(t){for(var e=0,i=[],n=[t.css("borderTopWidth"),t.css("borderRightWidth"),t.css("borderBottomWidth"),t.css("borderLeftWidth")],o=[t.css("paddingTop"),t.css("paddingRight"),t.css("paddingBottom"),t.css("paddingLeft")];e<4;e++)i[e]=parseInt(n[e],10)||0,i[e]+=parseInt(o[e],10)||0;return{height:i[0]+i[2],width:i[1]+i[3]}},_proportionallyResize:function(){if(this._proportionallyResizeElements.length)for(var t,e=0,i=this.helper||this.element;e<this._proportionallyResizeElements.length;e++)t=this._proportionallyResizeElements[e],this.outerDimensions||(this.outerDimensions=this._getPaddingPlusBorderDimensions(t)),t.css({height:i.height()-this.outerDimensions.height||0,width:i.width()-this.outerDimensions.width||0})},_renderProxy:function(){var e=this.element,i=this.options;this.elementOffset=e.offset(),this._helper?(this.helper=this.helper||t("<div style='overflow:hidden;'></div>"),this.helper.addClass(this._helper).css({width:this.element.outerWidth()-1,height:this.element.outerHeight()-1,position:"absolute",left:this.elementOffset.left+"px",top:this.elementOffset.top+"px",zIndex:++i.zIndex}),this.helper.appendTo("body").disableSelection()):this.helper=this.element},_change:{e:function(t,e){return{width:this.originalSize.width+e}},w:function(t,e){var i=this.originalSize,n=this.originalPosition;return{left:n.left+e,width:i.width-e}},n:function(t,e,i){var n=this.originalSize,o=this.originalPosition;return{top:o.top+i,height:n.height-i}},s:function(t,e,i){return{height:this.originalSize.height+i}},se:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},sw:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[e,i,n]))},ne:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},nw:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[e,i,n]))}},_propagate:function(e,i){t.ui.plugin.call(this,e,[i,this.ui()]),"resize"!==e&&this._trigger(e,i,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}}),t.ui.plugin.add("resizable","animate",{stop:function(e){var i=t(this).resizable("instance"),n=i.options,o=i._proportionallyResizeElements,s=o.length&&/textarea/i.test(o[0].nodeName),r=s&&i._hasScroll(o[0],"left")?0:i.sizeDiff.height,a=s?0:i.sizeDiff.width,l={width:i.size.width-a,height:i.size.height-r},c=parseInt(i.element.css("left"),10)+(i.position.left-i.originalPosition.left)||null,u=parseInt(i.element.css("top"),10)+(i.position.top-i.originalPosition.top)||null;i.element.animate(t.extend(l,u&&c?{top:u,left:c}:{}),{duration:n.animateDuration,easing:n.animateEasing,step:function(){var n={width:parseInt(i.element.css("width"),10),height:parseInt(i.element.css("height"),10),top:parseInt(i.element.css("top"),10),left:parseInt(i.element.css("left"),10)};o&&o.length&&t(o[0]).css({width:n.width,height:n.height}),i._updateCache(n),i._propagate("resize",e)}})}}),t.ui.plugin.add("resizable","containment",{start:function(){var e,i,n,o,s,r,a,l=t(this).resizable("instance"),c=l.options,u=l.element,d=c.containment,h=d instanceof t?d.get(0):/parent/.test(d)?u.parent().get(0):d;h&&(l.containerElement=t(h),/document/.test(d)||d===document?(l.containerOffset={left:0,top:0},l.containerPosition={left:0,top:0},l.parentData={element:t(document),left:0,top:0,width:t(document).width(),height:t(document).height()||document.body.parentNode.scrollHeight}):(e=t(h),i=[],t(["Top","Right","Left","Bottom"]).each(function(t,n){i[t]=l._num(e.css("padding"+n))}),l.containerOffset=e.offset(),l.containerPosition=e.position(),l.containerSize={height:e.innerHeight()-i[3],width:e.innerWidth()-i[1]},n=l.containerOffset,o=l.containerSize.height,s=l.containerSize.width,r=l._hasScroll(h,"left")?h.scrollWidth:s,a=l._hasScroll(h)?h.scrollHeight:o,l.parentData={element:h,left:n.left,top:n.top,width:r,height:a}))},resize:function(e){var i,n,o,s,r=t(this).resizable("instance"),a=r.options,l=r.containerOffset,c=r.position,u=r._aspectRatio||e.shiftKey,d={top:0,left:0},h=r.containerElement,p=!0;h[0]!==document&&/static/.test(h.css("position"))&&(d=l),c.left<(r._helper?l.left:0)&&(r.size.width=r.size.width+(r._helper?r.position.left-l.left:r.position.left-d.left),u&&(r.size.height=r.size.width/r.aspectRatio,p=!1),r.position.left=a.helper?l.left:0),c.top<(r._helper?l.top:0)&&(r.size.height=r.size.height+(r._helper?r.position.top-l.top:r.position.top),u&&(r.size.width=r.size.height*r.aspectRatio,p=!1),r.position.top=r._helper?l.top:0),o=r.containerElement.get(0)===r.element.parent().get(0),s=/relative|absolute/.test(r.containerElement.css("position")),o&&s?(r.offset.left=r.parentData.left+r.position.left,r.offset.top=r.parentData.top+r.position.top):(r.offset.left=r.element.offset().left,r.offset.top=r.element.offset().top),i=Math.abs(r.sizeDiff.width+(r._helper?r.offset.left-d.left:r.offset.left-l.left)),n=Math.abs(r.sizeDiff.height+(r._helper?r.offset.top-d.top:r.offset.top-l.top)),i+r.size.width>=r.parentData.width&&(r.size.width=r.parentData.width-i,u&&(r.size.height=r.size.width/r.aspectRatio,p=!1)),n+r.size.height>=r.parentData.height&&(r.size.height=r.parentData.height-n,u&&(r.size.width=r.size.height*r.aspectRatio,p=!1)),p||(r.position.left=r.prevPosition.left,r.position.top=r.prevPosition.top,r.size.width=r.prevSize.width,r.size.height=r.prevSize.height)},stop:function(){var e=t(this).resizable("instance"),i=e.options,n=e.containerOffset,o=e.containerPosition,s=e.containerElement,r=t(e.helper),a=r.offset(),l=r.outerWidth()-e.sizeDiff.width,c=r.outerHeight()-e.sizeDiff.height;e._helper&&!i.animate&&/relative/.test(s.css("position"))&&t(this).css({left:a.left-o.left-n.left,width:l,height:c}),e._helper&&!i.animate&&/static/.test(s.css("position"))&&t(this).css({left:a.left-o.left-n.left,width:l,height:c})}}),t.ui.plugin.add("resizable","alsoResize",{start:function(){var e=t(this).resizable("instance"),i=e.options;t(i.alsoResize).each(function(){var e=t(this);e.data("ui-resizable-alsoresize",{width:parseInt(e.width(),10),height:parseInt(e.height(),10),left:parseInt(e.css("left"),10),top:parseInt(e.css("top"),10)})})},resize:function(e,i){var n=t(this).resizable("instance"),o=n.options,s=n.originalSize,r=n.originalPosition,a={height:n.size.height-s.height||0,width:n.size.width-s.width||0,top:n.position.top-r.top||0,left:n.position.left-r.left||0};t(o.alsoResize).each(function(){var e=t(this),n=t(this).data("ui-resizable-alsoresize"),o={},s=e.parents(i.originalElement[0]).length?["width","height"]:["width","height","top","left"];t.each(s,function(t,e){var i=(n[e]||0)+(a[e]||0);i&&i>=0&&(o[e]=i||null)}),e.css(o)})},stop:function(){t(this).removeData("resizable-alsoresize")}}),t.ui.plugin.add("resizable","ghost",{start:function(){var e=t(this).resizable("instance"),i=e.options,n=e.size;e.ghost=e.originalElement.clone(),e.ghost.css({opacity:.25,display:"block",position:"relative",height:n.height,width:n.width,margin:0,left:0,top:0}).addClass("ui-resizable-ghost").addClass("string"==typeof i.ghost?i.ghost:""),e.ghost.appendTo(e.helper)},resize:function(){var e=t(this).resizable("instance");e.ghost&&e.ghost.css({position:"relative",height:e.size.height,width:e.size.width})},stop:function(){var e=t(this).resizable("instance");e.ghost&&e.helper&&e.helper.get(0).removeChild(e.ghost.get(0))}}),t.ui.plugin.add("resizable","grid",{resize:function(){var e,i=t(this).resizable("instance"),n=i.options,o=i.size,s=i.originalSize,r=i.originalPosition,a=i.axis,l="number"==typeof n.grid?[n.grid,n.grid]:n.grid,c=l[0]||1,u=l[1]||1,d=Math.round((o.width-s.width)/c)*c,h=Math.round((o.height-s.height)/u)*u,p=s.width+d,f=s.height+h,g=n.maxWidth&&n.maxWidth<p,m=n.maxHeight&&n.maxHeight<f,v=n.minWidth&&n.minWidth>p,y=n.minHeight&&n.minHeight>f;n.grid=l,v&&(p+=c),y&&(f+=u),g&&(p-=c),m&&(f-=u),/^(se|s|e)$/.test(a)?(i.size.width=p,i.size.height=f):/^(ne)$/.test(a)?(i.size.width=p,i.size.height=f,i.position.top=r.top-h):/^(sw)$/.test(a)?(i.size.width=p,i.size.height=f,i.position.left=r.left-d):((f-u<=0||p-c<=0)&&(e=i._getPaddingPlusBorderDimensions(this)),f-u>0?(i.size.height=f,i.position.top=r.top-h):(f=u-e.height,i.size.height=f,i.position.top=r.top+s.height-f),p-c>0?(i.size.width=p,i.position.left=r.left-d):(p=c-e.width,i.size.width=p,i.position.left=r.left+s.width-p))}}),t.ui.resizable})},{}],64:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery","./core","./mouse","./widget"],t):t(jQuery)}(function(t){return t.widget("ui.sortable",t.ui.mouse,{version:"1.11.4",widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1e3,activate:null,beforeStop:null,change:null,deactivate:null,out:null,over:null,receive:null,remove:null,sort:null,start:null,stop:null,update:null},_isOverAxis:function(t,e,i){return t>=e&&t<e+i},_isFloating:function(t){return/left|right/.test(t.css("float"))||/inline|table-cell/.test(t.css("display"))},_create:function(){this.containerCache={},this.element.addClass("ui-sortable"),this.refresh(),this.offset=this.element.offset(),this._mouseInit(),this._setHandleClassName(),this.ready=!0},_setOption:function(t,e){this._super(t,e),"handle"===t&&this._setHandleClassName()},_setHandleClassName:function(){this.element.find(".ui-sortable-handle").removeClass("ui-sortable-handle"),t.each(this.items,function(){(this.instance.options.handle?this.item.find(this.instance.options.handle):this.item).addClass("ui-sortable-handle")})},_destroy:function(){this.element.removeClass("ui-sortable ui-sortable-disabled").find(".ui-sortable-handle").removeClass("ui-sortable-handle"),this._mouseDestroy();for(var t=this.items.length-1;t>=0;t--)this.items[t].item.removeData(this.widgetName+"-item");return this},_mouseCapture:function(e,i){var n=null,o=!1,s=this;return!this.reverting&&(!this.options.disabled&&"static"!==this.options.type&&(this._refreshItems(e),t(e.target).parents().each(function(){if(t.data(this,s.widgetName+"-item")===s)return n=t(this),!1}),t.data(e.target,s.widgetName+"-item")===s&&(n=t(e.target)),!!n&&(!(this.options.handle&&!i&&(t(this.options.handle,n).find("*").addBack().each(function(){this===e.target&&(o=!0)}),!o))&&(this.currentItem=n,this._removeCurrentsFromItems(),!0))))},_mouseStart:function(e,i,n){var o,s,r=this.options;if(this.currentContainer=this,this.refreshPositions(),this.helper=this._createHelper(e),this._cacheHelperProportions(),this._cacheMargins(),this.scrollParent=this.helper.scrollParent(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},t.extend(this.offset,{click:{left:e.pageX-this.offset.left,top:e.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()}),this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),this.originalPosition=this._generatePosition(e),this.originalPageX=e.pageX,this.originalPageY=e.pageY,r.cursorAt&&this._adjustOffsetFromHelper(r.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!==this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),r.containment&&this._setContainment(),r.cursor&&"auto"!==r.cursor&&(s=this.document.find("body"),this.storedCursor=s.css("cursor"),s.css("cursor",r.cursor),this.storedStylesheet=t("<style>*{ cursor: "+r.cursor+" !important; }</style>").appendTo(s)),r.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",r.opacity)),r.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",r.zIndex)),this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",e,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions(),!n)for(o=this.containers.length-1;o>=0;o--)this.containers[o]._trigger("activate",e,this._uiHash(this));return t.ui.ddmanager&&(t.ui.ddmanager.current=this),t.ui.ddmanager&&!r.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this.dragging=!0,this.helper.addClass("ui-sortable-helper"),this._mouseDrag(e),!0},_mouseDrag:function(e){var i,n,o,s,r=this.options,a=!1;for(this.position=this._generatePosition(e),this.positionAbs=this._convertPositionTo("absolute"),this.lastPositionAbs||(this.lastPositionAbs=this.positionAbs),this.options.scroll&&(this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-e.pageY<r.scrollSensitivity?this.scrollParent[0].scrollTop=a=this.scrollParent[0].scrollTop+r.scrollSpeed:e.pageY-this.overflowOffset.top<r.scrollSensitivity&&(this.scrollParent[0].scrollTop=a=this.scrollParent[0].scrollTop-r.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-e.pageX<r.scrollSensitivity?this.scrollParent[0].scrollLeft=a=this.scrollParent[0].scrollLeft+r.scrollSpeed:e.pageX-this.overflowOffset.left<r.scrollSensitivity&&(this.scrollParent[0].scrollLeft=a=this.scrollParent[0].scrollLeft-r.scrollSpeed)):(e.pageY-this.document.scrollTop()<r.scrollSensitivity?a=this.document.scrollTop(this.document.scrollTop()-r.scrollSpeed):this.window.height()-(e.pageY-this.document.scrollTop())<r.scrollSensitivity&&(a=this.document.scrollTop(this.document.scrollTop()+r.scrollSpeed)),e.pageX-this.document.scrollLeft()<r.scrollSensitivity?a=this.document.scrollLeft(this.document.scrollLeft()-r.scrollSpeed):this.window.width()-(e.pageX-this.document.scrollLeft())<r.scrollSensitivity&&(a=this.document.scrollLeft(this.document.scrollLeft()+r.scrollSpeed))),a!==!1&&t.ui.ddmanager&&!r.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e)),this.positionAbs=this._convertPositionTo("absolute"),this.options.axis&&"y"===this.options.axis||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&"x"===this.options.axis||(this.helper[0].style.top=this.position.top+"px"),i=this.items.length-1;i>=0;i--)if(n=this.items[i],o=n.item[0],s=this._intersectsWithPointer(n),s&&n.instance===this.currentContainer&&!(o===this.currentItem[0]||this.placeholder[1===s?"next":"prev"]()[0]===o||t.contains(this.placeholder[0],o)||"semi-dynamic"===this.options.type&&t.contains(this.element[0],o))){if(this.direction=1===s?"down":"up","pointer"!==this.options.tolerance&&!this._intersectsWithSides(n))break;this._rearrange(e,n),this._trigger("change",e,this._uiHash());break}return this._contactContainers(e),t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),this._trigger("sort",e,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(e,i){if(e){if(t.ui.ddmanager&&!this.options.dropBehaviour&&t.ui.ddmanager.drop(this,e),this.options.revert){var n=this,o=this.placeholder.offset(),s=this.options.axis,r={};s&&"x"!==s||(r.left=o.left-this.offset.parent.left-this.margins.left+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollLeft)),s&&"y"!==s||(r.top=o.top-this.offset.parent.top-this.margins.top+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollTop)),this.reverting=!0,t(this.helper).animate(r,parseInt(this.options.revert,10)||500,function(){n._clear(e)})}else this._clear(e,i);return!1}},cancel:function(){if(this.dragging){this._mouseUp({target:null}),"original"===this.options.helper?this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper"):this.currentItem.show();for(var e=this.containers.length-1;e>=0;e--)this.containers[e]._trigger("deactivate",null,this._uiHash(this)),this.containers[e].containerCache.over&&(this.containers[e]._trigger("out",null,this._uiHash(this)),this.containers[e].containerCache.over=0)}return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),"original"!==this.options.helper&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),t.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?t(this.domPosition.prev).after(this.currentItem):t(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},t(i).each(function(){var i=(t(e.item||this).attr(e.attribute||"id")||"").match(e.expression||/(.+)[\-=_](.+)/);i&&n.push((e.key||i[1]+"[]")+"="+(e.key&&e.expression?i[1]:i[2]))}),!n.length&&e.key&&n.push(e.key+"="),n.join("&")},toArray:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},i.each(function(){n.push(t(e.item||this).attr(e.attribute||"id")||"")}),n},_intersectsWith:function(t){var e=this.positionAbs.left,i=e+this.helperProportions.width,n=this.positionAbs.top,o=n+this.helperProportions.height,s=t.left,r=s+t.width,a=t.top,l=a+t.height,c=this.offset.click.top,u=this.offset.click.left,d="x"===this.options.axis||n+c>a&&n+c<l,h="y"===this.options.axis||e+u>s&&e+u<r,p=d&&h;return"pointer"===this.options.tolerance||this.options.forcePointerForContainers||"pointer"!==this.options.tolerance&&this.helperProportions[this.floating?"width":"height"]>t[this.floating?"width":"height"]?p:s<e+this.helperProportions.width/2&&i-this.helperProportions.width/2<r&&a<n+this.helperProportions.height/2&&o-this.helperProportions.height/2<l},_intersectsWithPointer:function(t){var e="x"===this.options.axis||this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top,t.height),i="y"===this.options.axis||this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left,t.width),n=e&&i,o=this._getDragVerticalDirection(),s=this._getDragHorizontalDirection();return!!n&&(this.floating?s&&"right"===s||"down"===o?2:1:o&&("down"===o?2:1))},_intersectsWithSides:function(t){var e=this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top+t.height/2,t.height),i=this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left+t.width/2,t.width),n=this._getDragVerticalDirection(),o=this._getDragHorizontalDirection();return this.floating&&o?"right"===o&&i||"left"===o&&!i:n&&("down"===n&&e||"up"===n&&!e)},_getDragVerticalDirection:function(){var t=this.positionAbs.top-this.lastPositionAbs.top;return 0!==t&&(t>0?"down":"up")},_getDragHorizontalDirection:function(){var t=this.positionAbs.left-this.lastPositionAbs.left;return 0!==t&&(t>0?"right":"left")},refresh:function(t){return this._refreshItems(t),this._setHandleClassName(),this.refreshPositions(),this},_connectWith:function(){var t=this.options;return t.connectWith.constructor===String?[t.connectWith]:t.connectWith},_getItemsAsjQuery:function(e){function i(){a.push(this)}var n,o,s,r,a=[],l=[],c=this._connectWith();if(c&&e)for(n=c.length-1;n>=0;n--)for(s=t(c[n],this.document[0]),o=s.length-1;o>=0;o--)r=t.data(s[o],this.widgetFullName),r&&r!==this&&!r.options.disabled&&l.push([t.isFunction(r.options.items)?r.options.items.call(r.element):t(r.options.items,r.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),r]);for(l.push([t.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):t(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]),n=l.length-1;n>=0;n--)l[n][0].each(i);return t(a)},_removeCurrentsFromItems:function(){var e=this.currentItem.find(":data("+this.widgetName+"-item)");this.items=t.grep(this.items,function(t){for(var i=0;i<e.length;i++)if(e[i]===t.item[0])return!1;return!0})},_refreshItems:function(e){this.items=[],this.containers=[this];var i,n,o,s,r,a,l,c,u=this.items,d=[[t.isFunction(this.options.items)?this.options.items.call(this.element[0],e,{item:this.currentItem}):t(this.options.items,this.element),this]],h=this._connectWith();if(h&&this.ready)for(i=h.length-1;i>=0;i--)for(o=t(h[i],this.document[0]),n=o.length-1;n>=0;n--)s=t.data(o[n],this.widgetFullName),s&&s!==this&&!s.options.disabled&&(d.push([t.isFunction(s.options.items)?s.options.items.call(s.element[0],e,{item:this.currentItem}):t(s.options.items,s.element),s]),this.containers.push(s));for(i=d.length-1;i>=0;i--)for(r=d[i][1],a=d[i][0],n=0,c=a.length;n<c;n++)l=t(a[n]),l.data(this.widgetName+"-item",r),u.push({item:l,instance:r,width:0,height:0,left:0,top:0})},refreshPositions:function(e){this.floating=!!this.items.length&&("x"===this.options.axis||this._isFloating(this.items[0].item)),this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset());var i,n,o,s;for(i=this.items.length-1;i>=0;i--)n=this.items[i],n.instance!==this.currentContainer&&this.currentContainer&&n.item[0]!==this.currentItem[0]||(o=this.options.toleranceElement?t(this.options.toleranceElement,n.item):n.item,e||(n.width=o.outerWidth(),n.height=o.outerHeight()),s=o.offset(),n.left=s.left,n.top=s.top);if(this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);else for(i=this.containers.length-1;i>=0;i--)s=this.containers[i].element.offset(),this.containers[i].containerCache.left=s.left,this.containers[i].containerCache.top=s.top,this.containers[i].containerCache.width=this.containers[i].element.outerWidth(),this.containers[i].containerCache.height=this.containers[i].element.outerHeight();return this},_createPlaceholder:function(e){e=e||this;var i,n=e.options;n.placeholder&&n.placeholder.constructor!==String||(i=n.placeholder,n.placeholder={element:function(){var n=e.currentItem[0].nodeName.toLowerCase(),o=t("<"+n+">",e.document[0]).addClass(i||e.currentItem[0].className+" ui-sortable-placeholder").removeClass("ui-sortable-helper");return"tbody"===n?e._createTrPlaceholder(e.currentItem.find("tr").eq(0),t("<tr>",e.document[0]).appendTo(o)):"tr"===n?e._createTrPlaceholder(e.currentItem,o):"img"===n&&o.attr("src",e.currentItem.attr("src")),i||o.css("visibility","hidden"),o},update:function(t,o){i&&!n.forcePlaceholderSize||(o.height()||o.height(e.currentItem.innerHeight()-parseInt(e.currentItem.css("paddingTop")||0,10)-parseInt(e.currentItem.css("paddingBottom")||0,10)),o.width()||o.width(e.currentItem.innerWidth()-parseInt(e.currentItem.css("paddingLeft")||0,10)-parseInt(e.currentItem.css("paddingRight")||0,10)))}}),e.placeholder=t(n.placeholder.element.call(e.element,e.currentItem)),e.currentItem.after(e.placeholder),n.placeholder.update(e,e.placeholder)},_createTrPlaceholder:function(e,i){var n=this;e.children().each(function(){t("<td>&#160;</td>",n.document[0]).attr("colspan",t(this).attr("colspan")||1).appendTo(i)})},_contactContainers:function(e){var i,n,o,s,r,a,l,c,u,d,h=null,p=null;for(i=this.containers.length-1;i>=0;i--)if(!t.contains(this.currentItem[0],this.containers[i].element[0]))if(this._intersectsWith(this.containers[i].containerCache)){if(h&&t.contains(this.containers[i].element[0],h.element[0]))continue;h=this.containers[i],p=i}else this.containers[i].containerCache.over&&(this.containers[i]._trigger("out",e,this._uiHash(this)),this.containers[i].containerCache.over=0);if(h)if(1===this.containers.length)this.containers[p].containerCache.over||(this.containers[p]._trigger("over",e,this._uiHash(this)),this.containers[p].containerCache.over=1);else{for(o=1e4,s=null,u=h.floating||this._isFloating(this.currentItem),r=u?"left":"top",a=u?"width":"height",d=u?"clientX":"clientY",n=this.items.length-1;n>=0;n--)t.contains(this.containers[p].element[0],this.items[n].item[0])&&this.items[n].item[0]!==this.currentItem[0]&&(l=this.items[n].item.offset()[r],c=!1,e[d]-l>this.items[n][a]/2&&(c=!0),Math.abs(e[d]-l)<o&&(o=Math.abs(e[d]-l),s=this.items[n],this.direction=c?"up":"down"));if(!s&&!this.options.dropOnEmpty)return;if(this.currentContainer===this.containers[p])return void(this.currentContainer.containerCache.over||(this.containers[p]._trigger("over",e,this._uiHash()),this.currentContainer.containerCache.over=1));s?this._rearrange(e,s,null,!0):this._rearrange(e,null,this.containers[p].element,!0),this._trigger("change",e,this._uiHash()),this.containers[p]._trigger("change",e,this._uiHash(this)),this.currentContainer=this.containers[p],this.options.placeholder.update(this.currentContainer,this.placeholder),this.containers[p]._trigger("over",e,this._uiHash(this)),this.containers[p].containerCache.over=1}},_createHelper:function(e){var i=this.options,n=t.isFunction(i.helper)?t(i.helper.apply(this.element[0],[e,this.currentItem])):"clone"===i.helper?this.currentItem.clone():this.currentItem;return n.parents("body").length||t("parent"!==i.appendTo?i.appendTo:this.currentItem[0].parentNode)[0].appendChild(n[0]),n[0]===this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),n[0].style.width&&!i.forceHelperSize||n.width(this.currentItem.width()),n[0].style.height&&!i.forceHelperSize||n.height(this.currentItem.height()),n},_adjustOffsetFromHelper:function(e){"string"==typeof e&&(e=e.split(" ")),t.isArray(e)&&(e={left:+e[0],top:+e[1]||0}),"left"in e&&(this.offset.click.left=e.left+this.margins.left),"right"in e&&(this.offset.click.left=this.helperProportions.width-e.right+this.margins.left),"top"in e&&(this.offset.click.top=e.top+this.margins.top),"bottom"in e&&(this.offset.click.top=this.helperProportions.height-e.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var e=this.offsetParent.offset();return"absolute"===this.cssPosition&&this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),(this.offsetParent[0]===this.document[0].body||this.offsetParent[0].tagName&&"html"===this.offsetParent[0].tagName.toLowerCase()&&t.ui.ie)&&(e={top:0,left:0}),{top:e.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"===this.cssPosition){var t=this.currentItem.position();return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:t.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i,n,o=this.options;"parent"===o.containment&&(o.containment=this.helper[0].parentNode),"document"!==o.containment&&"window"!==o.containment||(this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,"document"===o.containment?this.document.width():this.window.width()-this.helperProportions.width-this.margins.left,("document"===o.containment?this.document.width():this.window.height()||this.document[0].body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]),/^(document|window|parent)$/.test(o.containment)||(e=t(o.containment)[0],i=t(o.containment).offset(),n="hidden"!==t(e).css("overflow"),this.containment=[i.left+(parseInt(t(e).css("borderLeftWidth"),10)||0)+(parseInt(t(e).css("paddingLeft"),10)||0)-this.margins.left,i.top+(parseInt(t(e).css("borderTopWidth"),10)||0)+(parseInt(t(e).css("paddingTop"),10)||0)-this.margins.top,i.left+(n?Math.max(e.scrollWidth,e.offsetWidth):e.offsetWidth)-(parseInt(t(e).css("borderLeftWidth"),10)||0)-(parseInt(t(e).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,i.top+(n?Math.max(e.scrollHeight,e.offsetHeight):e.offsetHeight)-(parseInt(t(e).css("borderTopWidth"),10)||0)-(parseInt(t(e).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top])},_convertPositionTo:function(e,i){i||(i=this.position);var n="absolute"===e?1:-1,o="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,s=/(html|body)/i.test(o[0].tagName);return{top:i.top+this.offset.relative.top*n+this.offset.parent.top*n-("fixed"===this.cssPosition?-this.scrollParent.scrollTop():s?0:o.scrollTop())*n,left:i.left+this.offset.relative.left*n+this.offset.parent.left*n-("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():s?0:o.scrollLeft())*n}},_generatePosition:function(e){var i,n,o=this.options,s=e.pageX,r=e.pageY,a="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,l=/(html|body)/i.test(a[0].tagName);return"relative"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&this.scrollParent[0]!==this.offsetParent[0]||(this.offset.relative=this._getRelativeOffset()),this.originalPosition&&(this.containment&&(e.pageX-this.offset.click.left<this.containment[0]&&(s=this.containment[0]+this.offset.click.left),e.pageY-this.offset.click.top<this.containment[1]&&(r=this.containment[1]+this.offset.click.top),e.pageX-this.offset.click.left>this.containment[2]&&(s=this.containment[2]+this.offset.click.left),e.pageY-this.offset.click.top>this.containment[3]&&(r=this.containment[3]+this.offset.click.top)),o.grid&&(i=this.originalPageY+Math.round((r-this.originalPageY)/o.grid[1])*o.grid[1],r=this.containment?i-this.offset.click.top>=this.containment[1]&&i-this.offset.click.top<=this.containment[3]?i:i-this.offset.click.top>=this.containment[1]?i-o.grid[1]:i+o.grid[1]:i,n=this.originalPageX+Math.round((s-this.originalPageX)/o.grid[0])*o.grid[0],
s=this.containment?n-this.offset.click.left>=this.containment[0]&&n-this.offset.click.left<=this.containment[2]?n:n-this.offset.click.left>=this.containment[0]?n-o.grid[0]:n+o.grid[0]:n)),{top:r-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.scrollParent.scrollTop():l?0:a.scrollTop()),left:s-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():l?0:a.scrollLeft())}},_rearrange:function(t,e,i,n){i?i[0].appendChild(this.placeholder[0]):e.item[0].parentNode.insertBefore(this.placeholder[0],"down"===this.direction?e.item[0]:e.item[0].nextSibling),this.counter=this.counter?++this.counter:1;var o=this.counter;this._delay(function(){o===this.counter&&this.refreshPositions(!n)})},_clear:function(t,e){function i(t,e,i){return function(n){i._trigger(t,n,e._uiHash(e))}}this.reverting=!1;var n,o=[];if(!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem),this._noFinalSort=null,this.helper[0]===this.currentItem[0]){for(n in this._storedCSS)"auto"!==this._storedCSS[n]&&"static"!==this._storedCSS[n]||(this._storedCSS[n]="");this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper")}else this.currentItem.show();for(this.fromOutside&&!e&&o.push(function(t){this._trigger("receive",t,this._uiHash(this.fromOutside))}),!this.fromOutside&&this.domPosition.prev===this.currentItem.prev().not(".ui-sortable-helper")[0]&&this.domPosition.parent===this.currentItem.parent()[0]||e||o.push(function(t){this._trigger("update",t,this._uiHash())}),this!==this.currentContainer&&(e||(o.push(function(t){this._trigger("remove",t,this._uiHash())}),o.push(function(t){return function(e){t._trigger("receive",e,this._uiHash(this))}}.call(this,this.currentContainer)),o.push(function(t){return function(e){t._trigger("update",e,this._uiHash(this))}}.call(this,this.currentContainer)))),n=this.containers.length-1;n>=0;n--)e||o.push(i("deactivate",this,this.containers[n])),this.containers[n].containerCache.over&&(o.push(i("out",this,this.containers[n])),this.containers[n].containerCache.over=0);if(this.storedCursor&&(this.document.find("body").css("cursor",this.storedCursor),this.storedStylesheet.remove()),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex","auto"===this._storedZIndex?"":this._storedZIndex),this.dragging=!1,e||this._trigger("beforeStop",t,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.cancelHelperRemoval||(this.helper[0]!==this.currentItem[0]&&this.helper.remove(),this.helper=null),!e){for(n=0;n<o.length;n++)o[n].call(this,t);this._trigger("stop",t,this._uiHash())}return this.fromOutside=!1,!this.cancelHelperRemoval},_trigger:function(){t.Widget.prototype._trigger.apply(this,arguments)===!1&&this.cancel()},_uiHash:function(e){var i=e||this;return{helper:i.helper,placeholder:i.placeholder||t([]),position:i.position,originalPosition:i.originalPosition,offset:i.positionAbs,item:i.currentItem,sender:e?e.element:null}}})})},{}],65:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery","./core","./widget","./position"],t):t(jQuery)}(function(t){return t.widget("ui.tooltip",{version:"1.11.4",options:{content:function(){var e=t(this).attr("title")||"";return t("<a>").text(e).html()},hide:!0,items:"[title]:not([disabled])",position:{my:"left top+15",at:"left bottom",collision:"flipfit flip"},show:!0,tooltipClass:null,track:!1,close:null,open:null},_addDescribedBy:function(e,i){var n=(e.attr("aria-describedby")||"").split(/\s+/);n.push(i),e.data("ui-tooltip-id",i).attr("aria-describedby",t.trim(n.join(" ")))},_removeDescribedBy:function(e){var i=e.data("ui-tooltip-id"),n=(e.attr("aria-describedby")||"").split(/\s+/),o=t.inArray(i,n);o!==-1&&n.splice(o,1),e.removeData("ui-tooltip-id"),n=t.trim(n.join(" ")),n?e.attr("aria-describedby",n):e.removeAttr("aria-describedby")},_create:function(){this._on({mouseover:"open",focusin:"open"}),this.tooltips={},this.parents={},this.options.disabled&&this._disable(),this.liveRegion=t("<div>").attr({role:"log","aria-live":"assertive","aria-relevant":"additions"}).addClass("ui-helper-hidden-accessible").appendTo(this.document[0].body)},_setOption:function(e,i){var n=this;return"disabled"===e?(this[i?"_disable":"_enable"](),void(this.options[e]=i)):(this._super(e,i),void("content"===e&&t.each(this.tooltips,function(t,e){n._updateContent(e.element)})))},_disable:function(){var e=this;t.each(this.tooltips,function(i,n){var o=t.Event("blur");o.target=o.currentTarget=n.element[0],e.close(o,!0)}),this.element.find(this.options.items).addBack().each(function(){var e=t(this);e.is("[title]")&&e.data("ui-tooltip-title",e.attr("title")).removeAttr("title")})},_enable:function(){this.element.find(this.options.items).addBack().each(function(){var e=t(this);e.data("ui-tooltip-title")&&e.attr("title",e.data("ui-tooltip-title"))})},open:function(e){var i=this,n=t(e?e.target:this.element).closest(this.options.items);n.length&&!n.data("ui-tooltip-id")&&(n.attr("title")&&n.data("ui-tooltip-title",n.attr("title")),n.data("ui-tooltip-open",!0),e&&"mouseover"===e.type&&n.parents().each(function(){var e,n=t(this);n.data("ui-tooltip-open")&&(e=t.Event("blur"),e.target=e.currentTarget=this,i.close(e,!0)),n.attr("title")&&(n.uniqueId(),i.parents[this.id]={element:this,title:n.attr("title")},n.attr("title",""))}),this._registerCloseHandlers(e,n),this._updateContent(n,e))},_updateContent:function(t,e){var i,n=this.options.content,o=this,s=e?e.type:null;return"string"==typeof n?this._open(e,t,n):(i=n.call(t[0],function(i){o._delay(function(){t.data("ui-tooltip-open")&&(e&&(e.type=s),this._open(e,t,i))})}),void(i&&this._open(e,t,i)))},_open:function(e,i,n){function o(t){c.of=t,r.is(":hidden")||r.position(c)}var s,r,a,l,c=t.extend({},this.options.position);if(n){if(s=this._find(i))return void s.tooltip.find(".ui-tooltip-content").html(n);i.is("[title]")&&(e&&"mouseover"===e.type?i.attr("title",""):i.removeAttr("title")),s=this._tooltip(i),r=s.tooltip,this._addDescribedBy(i,r.attr("id")),r.find(".ui-tooltip-content").html(n),this.liveRegion.children().hide(),n.clone?(l=n.clone(),l.removeAttr("id").find("[id]").removeAttr("id")):l=n,t("<div>").html(l).appendTo(this.liveRegion),this.options.track&&e&&/^mouse/.test(e.type)?(this._on(this.document,{mousemove:o}),o(e)):r.position(t.extend({of:i},this.options.position)),r.hide(),this._show(r,this.options.show),this.options.show&&this.options.show.delay&&(a=this.delayedShow=setInterval(function(){r.is(":visible")&&(o(c.of),clearInterval(a))},t.fx.interval)),this._trigger("open",e,{tooltip:r})}},_registerCloseHandlers:function(e,i){var n={keyup:function(e){if(e.keyCode===t.ui.keyCode.ESCAPE){var n=t.Event(e);n.currentTarget=i[0],this.close(n,!0)}}};i[0]!==this.element[0]&&(n.remove=function(){this._removeTooltip(this._find(i).tooltip)}),e&&"mouseover"!==e.type||(n.mouseleave="close"),e&&"focusin"!==e.type||(n.focusout="close"),this._on(!0,i,n)},close:function(e){var i,n=this,o=t(e?e.currentTarget:this.element),s=this._find(o);return s?(i=s.tooltip,void(s.closing||(clearInterval(this.delayedShow),o.data("ui-tooltip-title")&&!o.attr("title")&&o.attr("title",o.data("ui-tooltip-title")),this._removeDescribedBy(o),s.hiding=!0,i.stop(!0),this._hide(i,this.options.hide,function(){n._removeTooltip(t(this))}),o.removeData("ui-tooltip-open"),this._off(o,"mouseleave focusout keyup"),o[0]!==this.element[0]&&this._off(o,"remove"),this._off(this.document,"mousemove"),e&&"mouseleave"===e.type&&t.each(this.parents,function(e,i){t(i.element).attr("title",i.title),delete n.parents[e]}),s.closing=!0,this._trigger("close",e,{tooltip:i}),s.hiding||(s.closing=!1)))):void o.removeData("ui-tooltip-open")},_tooltip:function(e){var i=t("<div>").attr("role","tooltip").addClass("ui-tooltip ui-widget ui-corner-all ui-widget-content "+(this.options.tooltipClass||"")),n=i.uniqueId().attr("id");return t("<div>").addClass("ui-tooltip-content").appendTo(i),i.appendTo(this.document[0].body),this.tooltips[n]={element:e,tooltip:i}},_find:function(t){var e=t.data("ui-tooltip-id");return e?this.tooltips[e]:null},_removeTooltip:function(t){t.remove(),delete this.tooltips[t.attr("id")]},_destroy:function(){var e=this;t.each(this.tooltips,function(i,n){var o=t.Event("blur"),s=n.element;o.target=o.currentTarget=s[0],e.close(o,!0),t("#"+i).remove(),s.data("ui-tooltip-title")&&(s.attr("title")||s.attr("title",s.data("ui-tooltip-title")),s.removeData("ui-tooltip-title"))}),this.liveRegion.remove()}})})},{}],66:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(function(t){var e=0,i=Array.prototype.slice;return t.cleanData=function(e){return function(i){var n,o,s;for(s=0;null!=(o=i[s]);s++)try{n=t._data(o,"events"),n&&n.remove&&t(o).triggerHandler("remove")}catch(r){}e(i)}}(t.cleanData),t.widget=function(e,i,n){var o,s,r,a,l={},c=e.split(".")[0];return e=e.split(".")[1],o=c+"-"+e,n||(n=i,i=t.Widget),t.expr[":"][o.toLowerCase()]=function(e){return!!t.data(e,o)},t[c]=t[c]||{},s=t[c][e],r=t[c][e]=function(t,e){return this._createWidget?void(arguments.length&&this._createWidget(t,e)):new r(t,e)},t.extend(r,s,{version:n.version,_proto:t.extend({},n),_childConstructors:[]}),a=new i,a.options=t.widget.extend({},a.options),t.each(n,function(e,n){return t.isFunction(n)?void(l[e]=function(){var t=function(){return i.prototype[e].apply(this,arguments)},o=function(t){return i.prototype[e].apply(this,t)};return function(){var e,i=this._super,s=this._superApply;return this._super=t,this._superApply=o,e=n.apply(this,arguments),this._super=i,this._superApply=s,e}}()):void(l[e]=n)}),r.prototype=t.widget.extend(a,{widgetEventPrefix:s?a.widgetEventPrefix||e:e},l,{constructor:r,namespace:c,widgetName:e,widgetFullName:o}),s?(t.each(s._childConstructors,function(e,i){var n=i.prototype;t.widget(n.namespace+"."+n.widgetName,r,i._proto)}),delete s._childConstructors):i._childConstructors.push(r),t.widget.bridge(e,r),r},t.widget.extend=function(e){for(var n,o,s=i.call(arguments,1),r=0,a=s.length;r<a;r++)for(n in s[r])o=s[r][n],s[r].hasOwnProperty(n)&&void 0!==o&&(t.isPlainObject(o)?e[n]=t.isPlainObject(e[n])?t.widget.extend({},e[n],o):t.widget.extend({},o):e[n]=o);return e},t.widget.bridge=function(e,n){var o=n.prototype.widgetFullName||e;t.fn[e]=function(s){var r="string"==typeof s,a=i.call(arguments,1),l=this;return r?this.each(function(){var i,n=t.data(this,o);return"instance"===s?(l=n,!1):n?t.isFunction(n[s])&&"_"!==s.charAt(0)?(i=n[s].apply(n,a),i!==n&&void 0!==i?(l=i&&i.jquery?l.pushStack(i.get()):i,!1):void 0):t.error("no such method '"+s+"' for "+e+" widget instance"):t.error("cannot call methods on "+e+" prior to initialization; attempted to call method '"+s+"'")}):(a.length&&(s=t.widget.extend.apply(null,[s].concat(a))),this.each(function(){var e=t.data(this,o);e?(e.option(s||{}),e._init&&e._init()):t.data(this,o,new n(s,this))})),l}},t.Widget=function(){},t.Widget._childConstructors=[],t.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{disabled:!1,create:null},_createWidget:function(i,n){n=t(n||this.defaultElement||this)[0],this.element=t(n),this.uuid=e++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=t(),this.hoverable=t(),this.focusable=t(),n!==this&&(t.data(n,this.widgetFullName,this),this._on(!0,this.element,{remove:function(t){t.target===n&&this.destroy()}}),this.document=t(n.style?n.ownerDocument:n.document||n),this.window=t(this.document[0].defaultView||this.document[0].parentWindow)),this.options=t.widget.extend({},this.options,this._getCreateOptions(),i),this._create(),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:t.noop,_getCreateEventData:t.noop,_create:t.noop,_init:t.noop,destroy:function(){this._destroy(),this.element.unbind(this.eventNamespace).removeData(this.widgetFullName).removeData(t.camelCase(this.widgetFullName)),this.widget().unbind(this.eventNamespace).removeAttr("aria-disabled").removeClass(this.widgetFullName+"-disabled ui-state-disabled"),this.bindings.unbind(this.eventNamespace),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")},_destroy:t.noop,widget:function(){return this.element},option:function(e,i){var n,o,s,r=e;if(0===arguments.length)return t.widget.extend({},this.options);if("string"==typeof e)if(r={},n=e.split("."),e=n.shift(),n.length){for(o=r[e]=t.widget.extend({},this.options[e]),s=0;s<n.length-1;s++)o[n[s]]=o[n[s]]||{},o=o[n[s]];if(e=n.pop(),1===arguments.length)return void 0===o[e]?null:o[e];o[e]=i}else{if(1===arguments.length)return void 0===this.options[e]?null:this.options[e];r[e]=i}return this._setOptions(r),this},_setOptions:function(t){var e;for(e in t)this._setOption(e,t[e]);return this},_setOption:function(t,e){return this.options[t]=e,"disabled"===t&&(this.widget().toggleClass(this.widgetFullName+"-disabled",!!e),e&&(this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus"))),this},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_on:function(e,i,n){var o,s=this;"boolean"!=typeof e&&(n=i,i=e,e=!1),n?(i=o=t(i),this.bindings=this.bindings.add(i)):(n=i,i=this.element,o=this.widget()),t.each(n,function(n,r){function a(){if(e||s.options.disabled!==!0&&!t(this).hasClass("ui-state-disabled"))return("string"==typeof r?s[r]:r).apply(s,arguments)}"string"!=typeof r&&(a.guid=r.guid=r.guid||a.guid||t.guid++);var l=n.match(/^([\w:-]*)\s*(.*)$/),c=l[1]+s.eventNamespace,u=l[2];u?o.delegate(u,c,a):i.bind(c,a)})},_off:function(e,i){i=(i||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.unbind(i).undelegate(i),this.bindings=t(this.bindings.not(e).get()),this.focusable=t(this.focusable.not(e).get()),this.hoverable=t(this.hoverable.not(e).get())},_delay:function(t,e){function i(){return("string"==typeof t?n[t]:t).apply(n,arguments)}var n=this;return setTimeout(i,e||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){t(e.currentTarget).addClass("ui-state-hover")},mouseleave:function(e){t(e.currentTarget).removeClass("ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){t(e.currentTarget).addClass("ui-state-focus")},focusout:function(e){t(e.currentTarget).removeClass("ui-state-focus")}})},_trigger:function(e,i,n){var o,s,r=this.options[e];if(n=n||{},i=t.Event(i),i.type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),i.target=this.element[0],s=i.originalEvent)for(o in s)o in i||(i[o]=s[o]);return this.element.trigger(i,n),!(t.isFunction(r)&&r.apply(this.element[0],[i].concat(n))===!1||i.isDefaultPrevented())}},t.each({show:"fadeIn",hide:"fadeOut"},function(e,i){t.Widget.prototype["_"+e]=function(n,o,s){"string"==typeof o&&(o={effect:o});var r,a=o?o===!0||"number"==typeof o?i:o.effect||i:e;o=o||{},"number"==typeof o&&(o={duration:o}),r=!t.isEmptyObject(o),o.complete=s,o.delay&&n.delay(o.delay),r&&t.effects&&t.effects.effect[a]?n[e](o):a!==e&&n[a]?n[a](o.duration,o.easing,s):n.queue(function(i){t(this)[e](),s&&s.call(n[0]),i()})}}),t.widget})},{}],67:[function(t,e,i){!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):e("object"==typeof i?t("jquery"):jQuery)}(function(t){function e(t){return a.raw?t:encodeURIComponent(t)}function i(t){return a.raw?t:decodeURIComponent(t)}function n(t){return e(a.json?JSON.stringify(t):String(t))}function o(t){0===t.indexOf('"')&&(t=t.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\"));try{return t=decodeURIComponent(t.replace(r," ")),a.json?JSON.parse(t):t}catch(e){}}function s(e,i){var n=a.raw?e:o(e);return t.isFunction(i)?i(n):n}var r=/\+/g,a=t.cookie=function(o,r,l){if(void 0!==r&&!t.isFunction(r)){if(l=t.extend({},a.defaults,l),"number"==typeof l.expires){var c=l.expires,u=l.expires=new Date;u.setTime(+u+864e5*c)}return document.cookie=[e(o),"=",n(r),l.expires?"; expires="+l.expires.toUTCString():"",l.path?"; path="+l.path:"",l.domain?"; domain="+l.domain:"",l.secure?"; secure":""].join("")}for(var d=o?void 0:{},h=document.cookie?document.cookie.split("; "):[],p=0,f=h.length;p<f;p++){var g=h[p].split("="),m=i(g.shift()),v=g.join("=");if(o&&o===m){d=s(v,r);break}o||void 0===(v=s(v))||(d[m]=v)}return d};a.defaults={},t.removeCookie=function(e,i){return void 0!==t.cookie(e)&&(t.cookie(e,"",t.extend({},i,{expires:-1})),!t.cookie(e))}})},{jquery:69}],68:[function(t,e,i){!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof i?e.exports=t:t(jQuery)}(function(t){function e(e){var r=e||window.event,a=l.call(arguments,1),c=0,u=0,d=0,h=0;if(e=t.event.fix(r),e.type="mousewheel","detail"in r&&(d=r.detail*-1),"wheelDelta"in r&&(d=r.wheelDelta),"wheelDeltaY"in r&&(d=r.wheelDeltaY),"wheelDeltaX"in r&&(u=r.wheelDeltaX*-1),"axis"in r&&r.axis===r.HORIZONTAL_AXIS&&(u=d*-1,d=0),c=0===d?u:d,"deltaY"in r&&(d=r.deltaY*-1,c=d),"deltaX"in r&&(u=r.deltaX,0===d&&(c=u*-1)),0!==d||0!==u){if(1===r.deltaMode){var p=t.data(this,"mousewheel-line-height");c*=p,d*=p,u*=p}else if(2===r.deltaMode){var f=t.data(this,"mousewheel-page-height");c*=f,d*=f,u*=f}return h=Math.max(Math.abs(d),Math.abs(u)),(!s||h<s)&&(s=h,n(r,h)&&(s/=40)),n(r,h)&&(c/=40,u/=40,d/=40),c=Math[c>=1?"floor":"ceil"](c/s),u=Math[u>=1?"floor":"ceil"](u/s),d=Math[d>=1?"floor":"ceil"](d/s),e.deltaX=u,e.deltaY=d,e.deltaFactor=s,e.deltaMode=0,a.unshift(e,c,u,d),o&&clearTimeout(o),o=setTimeout(i,200),(t.event.dispatch||t.event.handle).apply(this,a)}}function i(){s=null}function n(t,e){return u.settings.adjustOldDeltas&&"mousewheel"===t.type&&e%120===0}var o,s,r=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],a="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],l=Array.prototype.slice;if(t.event.fixHooks)for(var c=r.length;c;)t.event.fixHooks[r[--c]]=t.event.mouseHooks;var u=t.event.special.mousewheel={version:"3.1.9",setup:function(){if(this.addEventListener)for(var i=a.length;i;)this.addEventListener(a[--i],e,!1);else this.onmousewheel=e;t.data(this,"mousewheel-line-height",u.getLineHeight(this)),t.data(this,"mousewheel-page-height",u.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var t=a.length;t;)this.removeEventListener(a[--t],e,!1);else this.onmousewheel=null},getLineHeight:function(e){return parseInt(t(e)["offsetParent"in t.fn?"offsetParent":"parent"]().css("fontSize"),10)},getPageHeight:function(e){return t(e).height()},settings:{adjustOldDeltas:!0}};t.fn.extend({mousewheel:function(t){return t?this.bind("mousewheel",t):this.trigger("mousewheel")},unmousewheel:function(t){return this.unbind("mousewheel",t)}})})},{}],69:[function(t,e,i){!function(t,i){"object"==typeof e&&"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return i(t)}:i(t)}("undefined"!=typeof window?window:this,function(t,e){function i(t){var e="length"in t&&t.length,i=ot.type(t);return"function"!==i&&!ot.isWindow(t)&&(!(1!==t.nodeType||!e)||("array"===i||0===e||"number"==typeof e&&e>0&&e-1 in t))}function n(t,e,i){if(ot.isFunction(e))return ot.grep(t,function(t,n){return!!e.call(t,n,t)!==i});if(e.nodeType)return ot.grep(t,function(t){return t===e!==i});if("string"==typeof e){if(ht.test(e))return ot.filter(e,t,i);e=ot.filter(e,t)}return ot.grep(t,function(t){return ot.inArray(t,e)>=0!==i})}function o(t,e){do t=t[e];while(t&&1!==t.nodeType);return t}function s(t){var e=_t[t]={};return ot.each(t.match(bt)||[],function(t,i){e[i]=!0}),e}function r(){ft.addEventListener?(ft.removeEventListener("DOMContentLoaded",a,!1),t.removeEventListener("load",a,!1)):(ft.detachEvent("onreadystatechange",a),t.detachEvent("onload",a))}function a(){(ft.addEventListener||"load"===event.type||"complete"===ft.readyState)&&(r(),ot.ready())}function l(t,e,i){if(void 0===i&&1===t.nodeType){var n="data-"+e.replace(Dt,"-$1").toLowerCase();if(i=t.getAttribute(n),"string"==typeof i){try{i="true"===i||"false"!==i&&("null"===i?null:+i+""===i?+i:Ct.test(i)?ot.parseJSON(i):i)}catch(o){}ot.data(t,e,i)}else i=void 0}return i}function c(t){var e;for(e in t)if(("data"!==e||!ot.isEmptyObject(t[e]))&&"toJSON"!==e)return!1;return!0}function u(t,e,i,n){if(ot.acceptData(t)){var o,s,r=ot.expando,a=t.nodeType,l=a?ot.cache:t,c=a?t[r]:t[r]&&r;if(c&&l[c]&&(n||l[c].data)||void 0!==i||"string"!=typeof e)return c||(c=a?t[r]=K.pop()||ot.guid++:r),l[c]||(l[c]=a?{}:{toJSON:ot.noop}),"object"!=typeof e&&"function"!=typeof e||(n?l[c]=ot.extend(l[c],e):l[c].data=ot.extend(l[c].data,e)),s=l[c],n||(s.data||(s.data={}),s=s.data),void 0!==i&&(s[ot.camelCase(e)]=i),"string"==typeof e?(o=s[e],null==o&&(o=s[ot.camelCase(e)])):o=s,o}}function d(t,e,i){if(ot.acceptData(t)){var n,o,s=t.nodeType,r=s?ot.cache:t,a=s?t[ot.expando]:ot.expando;if(r[a]){if(e&&(n=i?r[a]:r[a].data)){ot.isArray(e)?e=e.concat(ot.map(e,ot.camelCase)):e in n?e=[e]:(e=ot.camelCase(e),e=e in n?[e]:e.split(" ")),o=e.length;for(;o--;)delete n[e[o]];if(i?!c(n):!ot.isEmptyObject(n))return}(i||(delete r[a].data,c(r[a])))&&(s?ot.cleanData([t],!0):it.deleteExpando||r!=r.window?delete r[a]:r[a]=null)}}}function h(){return!0}function p(){return!1}function f(){try{return ft.activeElement}catch(t){}}function g(t){var e=Ht.split("|"),i=t.createDocumentFragment();if(i.createElement)for(;e.length;)i.createElement(e.pop());return i}function m(t,e){var i,n,o=0,s=typeof t.getElementsByTagName!==xt?t.getElementsByTagName(e||"*"):typeof t.querySelectorAll!==xt?t.querySelectorAll(e||"*"):void 0;if(!s)for(s=[],i=t.childNodes||t;null!=(n=i[o]);o++)!e||ot.nodeName(n,e)?s.push(n):ot.merge(s,m(n,e));return void 0===e||e&&ot.nodeName(t,e)?ot.merge([t],s):s}function v(t){jt.test(t.type)&&(t.defaultChecked=t.checked)}function y(t,e){return ot.nodeName(t,"table")&&ot.nodeName(11!==e.nodeType?e:e.firstChild,"tr")?t.getElementsByTagName("tbody")[0]||t.appendChild(t.ownerDocument.createElement("tbody")):t}function b(t){return t.type=(null!==ot.find.attr(t,"type"))+"/"+t.type,t}function _(t){var e=Ut.exec(t.type);return e?t.type=e[1]:t.removeAttribute("type"),t}function w(t,e){for(var i,n=0;null!=(i=t[n]);n++)ot._data(i,"globalEval",!e||ot._data(e[n],"globalEval"))}function k(t,e){if(1===e.nodeType&&ot.hasData(t)){var i,n,o,s=ot._data(t),r=ot._data(e,s),a=s.events;if(a){delete r.handle,r.events={};for(i in a)for(n=0,o=a[i].length;n<o;n++)ot.event.add(e,i,a[i][n])}r.data&&(r.data=ot.extend({},r.data))}}function x(t,e){var i,n,o;if(1===e.nodeType){if(i=e.nodeName.toLowerCase(),!it.noCloneEvent&&e[ot.expando]){o=ot._data(e);for(n in o.events)ot.removeEvent(e,n,o.handle);e.removeAttribute(ot.expando)}"script"===i&&e.text!==t.text?(b(e).text=t.text,_(e)):"object"===i?(e.parentNode&&(e.outerHTML=t.outerHTML),it.html5Clone&&t.innerHTML&&!ot.trim(e.innerHTML)&&(e.innerHTML=t.innerHTML)):"input"===i&&jt.test(t.type)?(e.defaultChecked=e.checked=t.checked,e.value!==t.value&&(e.value=t.value)):"option"===i?e.defaultSelected=e.selected=t.defaultSelected:"input"!==i&&"textarea"!==i||(e.defaultValue=t.defaultValue)}}function C(e,i){var n,o=ot(i.createElement(e)).appendTo(i.body),s=t.getDefaultComputedStyle&&(n=t.getDefaultComputedStyle(o[0]))?n.display:ot.css(o[0],"display");return o.detach(),s}function D(t){var e=ft,i=Jt[t];return i||(i=C(t,e),"none"!==i&&i||(Zt=(Zt||ot("<iframe frameborder='0' width='0' height='0'/>")).appendTo(e.documentElement),e=(Zt[0].contentWindow||Zt[0].contentDocument).document,e.write(),e.close(),i=C(t,e),Zt.detach()),Jt[t]=i),i}function T(t,e){return{get:function(){var i=t();if(null!=i)return i?void delete this.get:(this.get=e).apply(this,arguments)}}}function S(t,e){if(e in t)return e;for(var i=e.charAt(0).toUpperCase()+e.slice(1),n=e,o=he.length;o--;)if(e=he[o]+i,e in t)return e;return n}function I(t,e){for(var i,n,o,s=[],r=0,a=t.length;r<a;r++)n=t[r],n.style&&(s[r]=ot._data(n,"olddisplay"),i=n.style.display,e?(s[r]||"none"!==i||(n.style.display=""),""===n.style.display&&It(n)&&(s[r]=ot._data(n,"olddisplay",D(n.nodeName)))):(o=It(n),(i&&"none"!==i||!o)&&ot._data(n,"olddisplay",o?i:ot.css(n,"display"))));for(r=0;r<a;r++)n=t[r],n.style&&(e&&"none"!==n.style.display&&""!==n.style.display||(n.style.display=e?s[r]||"":"none"));return t}function P(t,e,i){var n=le.exec(e);return n?Math.max(0,n[1]-(i||0))+(n[2]||"px"):e}function j(t,e,i,n,o){for(var s=i===(n?"border":"content")?4:"width"===e?1:0,r=0;s<4;s+=2)"margin"===i&&(r+=ot.css(t,i+St[s],!0,o)),n?("content"===i&&(r-=ot.css(t,"padding"+St[s],!0,o)),"margin"!==i&&(r-=ot.css(t,"border"+St[s]+"Width",!0,o))):(r+=ot.css(t,"padding"+St[s],!0,o),"padding"!==i&&(r+=ot.css(t,"border"+St[s]+"Width",!0,o)));return r}function N(t,e,i){var n=!0,o="width"===e?t.offsetWidth:t.offsetHeight,s=te(t),r=it.boxSizing&&"border-box"===ot.css(t,"boxSizing",!1,s);if(o<=0||null==o){if(o=ee(t,e,s),(o<0||null==o)&&(o=t.style[e]),ne.test(o))return o;n=r&&(it.boxSizingReliable()||o===t.style[e]),o=parseFloat(o)||0}return o+j(t,e,i||(r?"border":"content"),n,s)+"px"}function A(t,e,i,n,o){return new A.prototype.init(t,e,i,n,o)}function $(){return setTimeout(function(){pe=void 0}),pe=ot.now()}function E(t,e){var i,n={height:t},o=0;for(e=e?1:0;o<4;o+=2-e)i=St[o],n["margin"+i]=n["padding"+i]=t;return e&&(n.opacity=n.width=t),n}function M(t,e,i){for(var n,o=(be[e]||[]).concat(be["*"]),s=0,r=o.length;s<r;s++)if(n=o[s].call(i,e,t))return n}function H(t,e,i){var n,o,s,r,a,l,c,u,d=this,h={},p=t.style,f=t.nodeType&&It(t),g=ot._data(t,"fxshow");i.queue||(a=ot._queueHooks(t,"fx"),null==a.unqueued&&(a.unqueued=0,l=a.empty.fire,a.empty.fire=function(){a.unqueued||l()}),a.unqueued++,d.always(function(){d.always(function(){a.unqueued--,ot.queue(t,"fx").length||a.empty.fire()})})),1===t.nodeType&&("height"in e||"width"in e)&&(i.overflow=[p.overflow,p.overflowX,p.overflowY],c=ot.css(t,"display"),u="none"===c?ot._data(t,"olddisplay")||D(t.nodeName):c,"inline"===u&&"none"===ot.css(t,"float")&&(it.inlineBlockNeedsLayout&&"inline"!==D(t.nodeName)?p.zoom=1:p.display="inline-block")),i.overflow&&(p.overflow="hidden",it.shrinkWrapBlocks()||d.always(function(){p.overflow=i.overflow[0],p.overflowX=i.overflow[1],p.overflowY=i.overflow[2]}));for(n in e)if(o=e[n],ge.exec(o)){if(delete e[n],s=s||"toggle"===o,o===(f?"hide":"show")){if("show"!==o||!g||void 0===g[n])continue;f=!0}h[n]=g&&g[n]||ot.style(t,n)}else c=void 0;if(ot.isEmptyObject(h))"inline"===("none"===c?D(t.nodeName):c)&&(p.display=c);else{g?"hidden"in g&&(f=g.hidden):g=ot._data(t,"fxshow",{}),s&&(g.hidden=!f),f?ot(t).show():d.done(function(){ot(t).hide()}),d.done(function(){var e;ot._removeData(t,"fxshow");for(e in h)ot.style(t,e,h[e])});for(n in h)r=M(f?g[n]:0,n,d),n in g||(g[n]=r.start,f&&(r.end=r.start,r.start="width"===n||"height"===n?1:0))}}function L(t,e){var i,n,o,s,r;for(i in t)if(n=ot.camelCase(i),o=e[n],s=t[i],ot.isArray(s)&&(o=s[1],s=t[i]=s[0]),i!==n&&(t[n]=s,delete t[i]),r=ot.cssHooks[n],r&&"expand"in r){s=r.expand(s),delete t[n];for(i in s)i in t||(t[i]=s[i],e[i]=o)}else e[n]=o}function z(t,e,i){var n,o,s=0,r=ye.length,a=ot.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var e=pe||$(),i=Math.max(0,c.startTime+c.duration-e),n=i/c.duration||0,s=1-n,r=0,l=c.tweens.length;r<l;r++)c.tweens[r].run(s);return a.notifyWith(t,[c,s,i]),s<1&&l?i:(a.resolveWith(t,[c]),!1)},c=a.promise({elem:t,props:ot.extend({},e),opts:ot.extend(!0,{specialEasing:{}},i),originalProperties:e,originalOptions:i,startTime:pe||$(),duration:i.duration,tweens:[],createTween:function(e,i){var n=ot.Tween(t,c.opts,e,i,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(n),n},stop:function(e){var i=0,n=e?c.tweens.length:0;if(o)return this;for(o=!0;i<n;i++)c.tweens[i].run(1);return e?a.resolveWith(t,[c,e]):a.rejectWith(t,[c,e]),this}}),u=c.props;for(L(u,c.opts.specialEasing);s<r;s++)if(n=ye[s].call(c,t,u,c.opts))return n;return ot.map(u,M,c),ot.isFunction(c.opts.start)&&c.opts.start.call(t,c),ot.fx.timer(ot.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always)}function O(t){return function(e,i){"string"!=typeof e&&(i=e,e="*");var n,o=0,s=e.toLowerCase().match(bt)||[];if(ot.isFunction(i))for(;n=s[o++];)"+"===n.charAt(0)?(n=n.slice(1)||"*",(t[n]=t[n]||[]).unshift(i)):(t[n]=t[n]||[]).push(i)}}function q(t,e,i,n){function o(a){var l;return s[a]=!0,ot.each(t[a]||[],function(t,a){var c=a(e,i,n);return"string"!=typeof c||r||s[c]?r?!(l=c):void 0:(e.dataTypes.unshift(c),o(c),!1)}),l}var s={},r=t===Fe;return o(e.dataTypes[0])||!s["*"]&&o("*")}function R(t,e){var i,n,o=ot.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((o[n]?t:i||(i={}))[n]=e[n]);return i&&ot.extend(!0,t,i),t}function W(t,e,i){for(var n,o,s,r,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===o&&(o=t.mimeType||e.getResponseHeader("Content-Type"));if(o)for(r in a)if(a[r]&&a[r].test(o)){l.unshift(r);break}if(l[0]in i)s=l[0];else{for(r in i){if(!l[0]||t.converters[r+" "+l[0]]){s=r;break}n||(n=r)}s=s||n}if(s)return s!==l[0]&&l.unshift(s),i[s]}function F(t,e,i,n){var o,s,r,a,l,c={},u=t.dataTypes.slice();if(u[1])for(r in t.converters)c[r.toLowerCase()]=t.converters[r];for(s=u.shift();s;)if(t.responseFields[s]&&(i[t.responseFields[s]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=s,s=u.shift())if("*"===s)s=l;else if("*"!==l&&l!==s){if(r=c[l+" "+s]||c["* "+s],!r)for(o in c)if(a=o.split(" "),a[1]===s&&(r=c[l+" "+a[0]]||c["* "+a[0]])){r===!0?r=c[o]:c[o]!==!0&&(s=a[0],u.unshift(a[1]));break}if(r!==!0)if(r&&t["throws"])e=r(e);else try{e=r(e)}catch(d){return{state:"parsererror",error:r?d:"No conversion from "+l+" to "+s}}}return{state:"success",data:e}}function Y(t,e,i,n){var o;if(ot.isArray(e))ot.each(e,function(e,o){i||Ue.test(t)?n(t,o):Y(t+"["+("object"==typeof o?e:"")+"]",o,i,n)});else if(i||"object"!==ot.type(e))n(t,e);else for(o in e)Y(t+"["+o+"]",e[o],i,n)}function B(){try{return new t.XMLHttpRequest}catch(e){}}function X(){try{return new t.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}function U(t){return ot.isWindow(t)?t:9===t.nodeType&&(t.defaultView||t.parentWindow)}var K=[],V=K.slice,Q=K.concat,G=K.push,Z=K.indexOf,J={},tt=J.toString,et=J.hasOwnProperty,it={},nt="1.11.3",ot=function(t,e){return new ot.fn.init(t,e)},st=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,rt=/^-ms-/,at=/-([\da-z])/gi,lt=function(t,e){return e.toUpperCase()};ot.fn=ot.prototype={jquery:nt,constructor:ot,selector:"",length:0,toArray:function(){return V.call(this)},get:function(t){return null!=t?t<0?this[t+this.length]:this[t]:V.call(this)},pushStack:function(t){var e=ot.merge(this.constructor(),t);return e.prevObject=this,e.context=this.context,e},each:function(t,e){return ot.each(this,t,e)},map:function(t){return this.pushStack(ot.map(this,function(e,i){return t.call(e,i,e)}))},slice:function(){return this.pushStack(V.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(t){var e=this.length,i=+t+(t<0?e:0);return this.pushStack(i>=0&&i<e?[this[i]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:G,sort:K.sort,splice:K.splice},ot.extend=ot.fn.extend=function(){var t,e,i,n,o,s,r=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[a]||{},a++),"object"==typeof r||ot.isFunction(r)||(r={}),a===l&&(r=this,a--);a<l;a++)if(null!=(o=arguments[a]))for(n in o)t=r[n],i=o[n],r!==i&&(c&&i&&(ot.isPlainObject(i)||(e=ot.isArray(i)))?(e?(e=!1,s=t&&ot.isArray(t)?t:[]):s=t&&ot.isPlainObject(t)?t:{},r[n]=ot.extend(c,s,i)):void 0!==i&&(r[n]=i));return r},ot.extend({expando:"jQuery"+(nt+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isFunction:function(t){return"function"===ot.type(t);
},isArray:Array.isArray||function(t){return"array"===ot.type(t)},isWindow:function(t){return null!=t&&t==t.window},isNumeric:function(t){return!ot.isArray(t)&&t-parseFloat(t)+1>=0},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},isPlainObject:function(t){var e;if(!t||"object"!==ot.type(t)||t.nodeType||ot.isWindow(t))return!1;try{if(t.constructor&&!et.call(t,"constructor")&&!et.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(i){return!1}if(it.ownLast)for(e in t)return et.call(t,e);for(e in t);return void 0===e||et.call(t,e)},type:function(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?J[tt.call(t)]||"object":typeof t},globalEval:function(e){e&&ot.trim(e)&&(t.execScript||function(e){t.eval.call(t,e)})(e)},camelCase:function(t){return t.replace(rt,"ms-").replace(at,lt)},nodeName:function(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()},each:function(t,e,n){var o,s=0,r=t.length,a=i(t);if(n){if(a)for(;s<r&&(o=e.apply(t[s],n),o!==!1);s++);else for(s in t)if(o=e.apply(t[s],n),o===!1)break}else if(a)for(;s<r&&(o=e.call(t[s],s,t[s]),o!==!1);s++);else for(s in t)if(o=e.call(t[s],s,t[s]),o===!1)break;return t},trim:function(t){return null==t?"":(t+"").replace(st,"")},makeArray:function(t,e){var n=e||[];return null!=t&&(i(Object(t))?ot.merge(n,"string"==typeof t?[t]:t):G.call(n,t)),n},inArray:function(t,e,i){var n;if(e){if(Z)return Z.call(e,t,i);for(n=e.length,i=i?i<0?Math.max(0,n+i):i:0;i<n;i++)if(i in e&&e[i]===t)return i}return-1},merge:function(t,e){for(var i=+e.length,n=0,o=t.length;n<i;)t[o++]=e[n++];if(i!==i)for(;void 0!==e[n];)t[o++]=e[n++];return t.length=o,t},grep:function(t,e,i){for(var n,o=[],s=0,r=t.length,a=!i;s<r;s++)n=!e(t[s],s),n!==a&&o.push(t[s]);return o},map:function(t,e,n){var o,s=0,r=t.length,a=i(t),l=[];if(a)for(;s<r;s++)o=e(t[s],s,n),null!=o&&l.push(o);else for(s in t)o=e(t[s],s,n),null!=o&&l.push(o);return Q.apply([],l)},guid:1,proxy:function(t,e){var i,n,o;if("string"==typeof e&&(o=t[e],e=t,t=o),ot.isFunction(t))return i=V.call(arguments,2),n=function(){return t.apply(e||this,i.concat(V.call(arguments)))},n.guid=t.guid=t.guid||ot.guid++,n},now:function(){return+new Date},support:it}),ot.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(t,e){J["[object "+e+"]"]=e.toLowerCase()});var ct=function(t){function e(t,e,i,n){var o,s,r,a,l,c,d,p,f,g;if((e?e.ownerDocument||e:q)!==A&&N(e),e=e||A,i=i||[],a=e.nodeType,"string"!=typeof t||!t||1!==a&&9!==a&&11!==a)return i;if(!n&&E){if(11!==a&&(o=yt.exec(t)))if(r=o[1]){if(9===a){if(s=e.getElementById(r),!s||!s.parentNode)return i;if(s.id===r)return i.push(s),i}else if(e.ownerDocument&&(s=e.ownerDocument.getElementById(r))&&z(e,s)&&s.id===r)return i.push(s),i}else{if(o[2])return Z.apply(i,e.getElementsByTagName(t)),i;if((r=o[3])&&w.getElementsByClassName)return Z.apply(i,e.getElementsByClassName(r)),i}if(w.qsa&&(!M||!M.test(t))){if(p=d=O,f=e,g=1!==a&&t,1===a&&"object"!==e.nodeName.toLowerCase()){for(c=D(t),(d=e.getAttribute("id"))?p=d.replace(_t,"\\$&"):e.setAttribute("id",p),p="[id='"+p+"'] ",l=c.length;l--;)c[l]=p+h(c[l]);f=bt.test(t)&&u(e.parentNode)||e,g=c.join(",")}if(g)try{return Z.apply(i,f.querySelectorAll(g)),i}catch(m){}finally{d||e.removeAttribute("id")}}}return S(t.replace(lt,"$1"),e,i,n)}function i(){function t(i,n){return e.push(i+" ")>k.cacheLength&&delete t[e.shift()],t[i+" "]=n}var e=[];return t}function n(t){return t[O]=!0,t}function o(t){var e=A.createElement("div");try{return!!t(e)}catch(i){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function s(t,e){for(var i=t.split("|"),n=t.length;n--;)k.attrHandle[i[n]]=e}function r(t,e){var i=e&&t,n=i&&1===t.nodeType&&1===e.nodeType&&(~e.sourceIndex||U)-(~t.sourceIndex||U);if(n)return n;if(i)for(;i=i.nextSibling;)if(i===e)return-1;return t?1:-1}function a(t){return function(e){var i=e.nodeName.toLowerCase();return"input"===i&&e.type===t}}function l(t){return function(e){var i=e.nodeName.toLowerCase();return("input"===i||"button"===i)&&e.type===t}}function c(t){return n(function(e){return e=+e,n(function(i,n){for(var o,s=t([],i.length,e),r=s.length;r--;)i[o=s[r]]&&(i[o]=!(n[o]=i[o]))})})}function u(t){return t&&"undefined"!=typeof t.getElementsByTagName&&t}function d(){}function h(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function p(t,e,i){var n=e.dir,o=i&&"parentNode"===n,s=W++;return e.first?function(e,i,s){for(;e=e[n];)if(1===e.nodeType||o)return t(e,i,s)}:function(e,i,r){var a,l,c=[R,s];if(r){for(;e=e[n];)if((1===e.nodeType||o)&&t(e,i,r))return!0}else for(;e=e[n];)if(1===e.nodeType||o){if(l=e[O]||(e[O]={}),(a=l[n])&&a[0]===R&&a[1]===s)return c[2]=a[2];if(l[n]=c,c[2]=t(e,i,r))return!0}}}function f(t){return t.length>1?function(e,i,n){for(var o=t.length;o--;)if(!t[o](e,i,n))return!1;return!0}:t[0]}function g(t,i,n){for(var o=0,s=i.length;o<s;o++)e(t,i[o],n);return n}function m(t,e,i,n,o){for(var s,r=[],a=0,l=t.length,c=null!=e;a<l;a++)(s=t[a])&&(i&&!i(s,n,o)||(r.push(s),c&&e.push(a)));return r}function v(t,e,i,o,s,r){return o&&!o[O]&&(o=v(o)),s&&!s[O]&&(s=v(s,r)),n(function(n,r,a,l){var c,u,d,h=[],p=[],f=r.length,v=n||g(e||"*",a.nodeType?[a]:a,[]),y=!t||!n&&e?v:m(v,h,t,a,l),b=i?s||(n?t:f||o)?[]:r:y;if(i&&i(y,b,a,l),o)for(c=m(b,p),o(c,[],a,l),u=c.length;u--;)(d=c[u])&&(b[p[u]]=!(y[p[u]]=d));if(n){if(s||t){if(s){for(c=[],u=b.length;u--;)(d=b[u])&&c.push(y[u]=d);s(null,b=[],c,l)}for(u=b.length;u--;)(d=b[u])&&(c=s?tt(n,d):h[u])>-1&&(n[c]=!(r[c]=d))}}else b=m(b===r?b.splice(f,b.length):b),s?s(null,r,b,l):Z.apply(r,b)})}function y(t){for(var e,i,n,o=t.length,s=k.relative[t[0].type],r=s||k.relative[" "],a=s?1:0,l=p(function(t){return t===e},r,!0),c=p(function(t){return tt(e,t)>-1},r,!0),u=[function(t,i,n){var o=!s&&(n||i!==I)||((e=i).nodeType?l(t,i,n):c(t,i,n));return e=null,o}];a<o;a++)if(i=k.relative[t[a].type])u=[p(f(u),i)];else{if(i=k.filter[t[a].type].apply(null,t[a].matches),i[O]){for(n=++a;n<o&&!k.relative[t[n].type];n++);return v(a>1&&f(u),a>1&&h(t.slice(0,a-1).concat({value:" "===t[a-2].type?"*":""})).replace(lt,"$1"),i,a<n&&y(t.slice(a,n)),n<o&&y(t=t.slice(n)),n<o&&h(t))}u.push(i)}return f(u)}function b(t,i){var o=i.length>0,s=t.length>0,r=function(n,r,a,l,c){var u,d,h,p=0,f="0",g=n&&[],v=[],y=I,b=n||s&&k.find.TAG("*",c),_=R+=null==y?1:Math.random()||.1,w=b.length;for(c&&(I=r!==A&&r);f!==w&&null!=(u=b[f]);f++){if(s&&u){for(d=0;h=t[d++];)if(h(u,r,a)){l.push(u);break}c&&(R=_)}o&&((u=!h&&u)&&p--,n&&g.push(u))}if(p+=f,o&&f!==p){for(d=0;h=i[d++];)h(g,v,r,a);if(n){if(p>0)for(;f--;)g[f]||v[f]||(v[f]=Q.call(l));v=m(v)}Z.apply(l,v),c&&!n&&v.length>0&&p+i.length>1&&e.uniqueSort(l)}return c&&(R=_,I=y),g};return o?n(r):r}var _,w,k,x,C,D,T,S,I,P,j,N,A,$,E,M,H,L,z,O="sizzle"+1*new Date,q=t.document,R=0,W=0,F=i(),Y=i(),B=i(),X=function(t,e){return t===e&&(j=!0),0},U=1<<31,K={}.hasOwnProperty,V=[],Q=V.pop,G=V.push,Z=V.push,J=V.slice,tt=function(t,e){for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i;return-1},et="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",it="[\\x20\\t\\r\\n\\f]",nt="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",ot=nt.replace("w","w#"),st="\\["+it+"*("+nt+")(?:"+it+"*([*^$|!~]?=)"+it+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+ot+"))|)"+it+"*\\]",rt=":("+nt+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+st+")*)|.*)\\)|)",at=new RegExp(it+"+","g"),lt=new RegExp("^"+it+"+|((?:^|[^\\\\])(?:\\\\.)*)"+it+"+$","g"),ct=new RegExp("^"+it+"*,"+it+"*"),ut=new RegExp("^"+it+"*([>+~]|"+it+")"+it+"*"),dt=new RegExp("="+it+"*([^\\]'\"]*?)"+it+"*\\]","g"),ht=new RegExp(rt),pt=new RegExp("^"+ot+"$"),ft={ID:new RegExp("^#("+nt+")"),CLASS:new RegExp("^\\.("+nt+")"),TAG:new RegExp("^("+nt.replace("w","w*")+")"),ATTR:new RegExp("^"+st),PSEUDO:new RegExp("^"+rt),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+it+"*(even|odd|(([+-]|)(\\d*)n|)"+it+"*(?:([+-]|)"+it+"*(\\d+)|))"+it+"*\\)|)","i"),bool:new RegExp("^(?:"+et+")$","i"),needsContext:new RegExp("^"+it+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+it+"*((?:-\\d)?\\d*)"+it+"*\\)|)(?=[^-]|$)","i")},gt=/^(?:input|select|textarea|button)$/i,mt=/^h\d$/i,vt=/^[^{]+\{\s*\[native \w/,yt=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,bt=/[+~]/,_t=/'|\\/g,wt=new RegExp("\\\\([\\da-f]{1,6}"+it+"?|("+it+")|.)","ig"),kt=function(t,e,i){var n="0x"+e-65536;return n!==n||i?e:n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320)},xt=function(){N()};try{Z.apply(V=J.call(q.childNodes),q.childNodes),V[q.childNodes.length].nodeType}catch(Ct){Z={apply:V.length?function(t,e){G.apply(t,J.call(e))}:function(t,e){for(var i=t.length,n=0;t[i++]=e[n++];);t.length=i-1}}}w=e.support={},C=e.isXML=function(t){var e=t&&(t.ownerDocument||t).documentElement;return!!e&&"HTML"!==e.nodeName},N=e.setDocument=function(t){var e,i,n=t?t.ownerDocument||t:q;return n!==A&&9===n.nodeType&&n.documentElement?(A=n,$=n.documentElement,i=n.defaultView,i&&i!==i.top&&(i.addEventListener?i.addEventListener("unload",xt,!1):i.attachEvent&&i.attachEvent("onunload",xt)),E=!C(n),w.attributes=o(function(t){return t.className="i",!t.getAttribute("className")}),w.getElementsByTagName=o(function(t){return t.appendChild(n.createComment("")),!t.getElementsByTagName("*").length}),w.getElementsByClassName=vt.test(n.getElementsByClassName),w.getById=o(function(t){return $.appendChild(t).id=O,!n.getElementsByName||!n.getElementsByName(O).length}),w.getById?(k.find.ID=function(t,e){if("undefined"!=typeof e.getElementById&&E){var i=e.getElementById(t);return i&&i.parentNode?[i]:[]}},k.filter.ID=function(t){var e=t.replace(wt,kt);return function(t){return t.getAttribute("id")===e}}):(delete k.find.ID,k.filter.ID=function(t){var e=t.replace(wt,kt);return function(t){var i="undefined"!=typeof t.getAttributeNode&&t.getAttributeNode("id");return i&&i.value===e}}),k.find.TAG=w.getElementsByTagName?function(t,e){return"undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t):w.qsa?e.querySelectorAll(t):void 0}:function(t,e){var i,n=[],o=0,s=e.getElementsByTagName(t);if("*"===t){for(;i=s[o++];)1===i.nodeType&&n.push(i);return n}return s},k.find.CLASS=w.getElementsByClassName&&function(t,e){if(E)return e.getElementsByClassName(t)},H=[],M=[],(w.qsa=vt.test(n.querySelectorAll))&&(o(function(t){$.appendChild(t).innerHTML="<a id='"+O+"'></a><select id='"+O+"-\f]' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&M.push("[*^$]="+it+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||M.push("\\["+it+"*(?:value|"+et+")"),t.querySelectorAll("[id~="+O+"-]").length||M.push("~="),t.querySelectorAll(":checked").length||M.push(":checked"),t.querySelectorAll("a#"+O+"+*").length||M.push(".#.+[+~]")}),o(function(t){var e=n.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&M.push("name"+it+"*[*^$|!~]?="),t.querySelectorAll(":enabled").length||M.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),M.push(",.*:")})),(w.matchesSelector=vt.test(L=$.matches||$.webkitMatchesSelector||$.mozMatchesSelector||$.oMatchesSelector||$.msMatchesSelector))&&o(function(t){w.disconnectedMatch=L.call(t,"div"),L.call(t,"[s!='']:x"),H.push("!=",rt)}),M=M.length&&new RegExp(M.join("|")),H=H.length&&new RegExp(H.join("|")),e=vt.test($.compareDocumentPosition),z=e||vt.test($.contains)?function(t,e){var i=9===t.nodeType?t.documentElement:t,n=e&&e.parentNode;return t===n||!(!n||1!==n.nodeType||!(i.contains?i.contains(n):t.compareDocumentPosition&&16&t.compareDocumentPosition(n)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},X=e?function(t,e){if(t===e)return j=!0,0;var i=!t.compareDocumentPosition-!e.compareDocumentPosition;return i?i:(i=(t.ownerDocument||t)===(e.ownerDocument||e)?t.compareDocumentPosition(e):1,1&i||!w.sortDetached&&e.compareDocumentPosition(t)===i?t===n||t.ownerDocument===q&&z(q,t)?-1:e===n||e.ownerDocument===q&&z(q,e)?1:P?tt(P,t)-tt(P,e):0:4&i?-1:1)}:function(t,e){if(t===e)return j=!0,0;var i,o=0,s=t.parentNode,a=e.parentNode,l=[t],c=[e];if(!s||!a)return t===n?-1:e===n?1:s?-1:a?1:P?tt(P,t)-tt(P,e):0;if(s===a)return r(t,e);for(i=t;i=i.parentNode;)l.unshift(i);for(i=e;i=i.parentNode;)c.unshift(i);for(;l[o]===c[o];)o++;return o?r(l[o],c[o]):l[o]===q?-1:c[o]===q?1:0},n):A},e.matches=function(t,i){return e(t,null,null,i)},e.matchesSelector=function(t,i){if((t.ownerDocument||t)!==A&&N(t),i=i.replace(dt,"='$1']"),w.matchesSelector&&E&&(!H||!H.test(i))&&(!M||!M.test(i)))try{var n=L.call(t,i);if(n||w.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(o){}return e(i,A,null,[t]).length>0},e.contains=function(t,e){return(t.ownerDocument||t)!==A&&N(t),z(t,e)},e.attr=function(t,e){(t.ownerDocument||t)!==A&&N(t);var i=k.attrHandle[e.toLowerCase()],n=i&&K.call(k.attrHandle,e.toLowerCase())?i(t,e,!E):void 0;return void 0!==n?n:w.attributes||!E?t.getAttribute(e):(n=t.getAttributeNode(e))&&n.specified?n.value:null},e.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},e.uniqueSort=function(t){var e,i=[],n=0,o=0;if(j=!w.detectDuplicates,P=!w.sortStable&&t.slice(0),t.sort(X),j){for(;e=t[o++];)e===t[o]&&(n=i.push(o));for(;n--;)t.splice(i[n],1)}return P=null,t},x=e.getText=function(t){var e,i="",n=0,o=t.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)i+=x(t)}else if(3===o||4===o)return t.nodeValue}else for(;e=t[n++];)i+=x(e);return i},k=e.selectors={cacheLength:50,createPseudo:n,match:ft,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(wt,kt),t[3]=(t[3]||t[4]||t[5]||"").replace(wt,kt),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||e.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&e.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return ft.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&ht.test(i)&&(e=D(i,!0))&&(e=i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(wt,kt).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=F[t+" "];return e||(e=new RegExp("(^|"+it+")"+t+"("+it+"|$)"))&&F(t,function(t){return e.test("string"==typeof t.className&&t.className||"undefined"!=typeof t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,i,n){return function(o){var s=e.attr(o,t);return null==s?"!="===i:!i||(s+="","="===i?s===n:"!="===i?s!==n:"^="===i?n&&0===s.indexOf(n):"*="===i?n&&s.indexOf(n)>-1:"$="===i?n&&s.slice(-n.length)===n:"~="===i?(" "+s.replace(at," ")+" ").indexOf(n)>-1:"|="===i&&(s===n||s.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,i,n,o){var s="nth"!==t.slice(0,3),r="last"!==t.slice(-4),a="of-type"===e;return 1===n&&0===o?function(t){return!!t.parentNode}:function(e,i,l){var c,u,d,h,p,f,g=s!==r?"nextSibling":"previousSibling",m=e.parentNode,v=a&&e.nodeName.toLowerCase(),y=!l&&!a;if(m){if(s){for(;g;){for(d=e;d=d[g];)if(a?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;f=g="only"===t&&!f&&"nextSibling"}return!0}if(f=[r?m.firstChild:m.lastChild],r&&y){for(u=m[O]||(m[O]={}),c=u[t]||[],p=c[0]===R&&c[1],h=c[0]===R&&c[2],d=p&&m.childNodes[p];d=++p&&d&&d[g]||(h=p=0)||f.pop();)if(1===d.nodeType&&++h&&d===e){u[t]=[R,p,h];break}}else if(y&&(c=(e[O]||(e[O]={}))[t])&&c[0]===R)h=c[1];else for(;(d=++p&&d&&d[g]||(h=p=0)||f.pop())&&((a?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++h||(y&&((d[O]||(d[O]={}))[t]=[R,h]),d!==e)););return h-=o,h===n||h%n===0&&h/n>=0}}},PSEUDO:function(t,i){var o,s=k.pseudos[t]||k.setFilters[t.toLowerCase()]||e.error("unsupported pseudo: "+t);return s[O]?s(i):s.length>1?(o=[t,t,"",i],k.setFilters.hasOwnProperty(t.toLowerCase())?n(function(t,e){for(var n,o=s(t,i),r=o.length;r--;)n=tt(t,o[r]),t[n]=!(e[n]=o[r])}):function(t){return s(t,0,o)}):s}},pseudos:{not:n(function(t){var e=[],i=[],o=T(t.replace(lt,"$1"));return o[O]?n(function(t,e,i,n){for(var s,r=o(t,null,n,[]),a=t.length;a--;)(s=r[a])&&(t[a]=!(e[a]=s))}):function(t,n,s){return e[0]=t,o(e,null,s,i),e[0]=null,!i.pop()}}),has:n(function(t){return function(i){return e(t,i).length>0}}),contains:n(function(t){return t=t.replace(wt,kt),function(e){return(e.textContent||e.innerText||x(e)).indexOf(t)>-1}}),lang:n(function(t){return pt.test(t||"")||e.error("unsupported lang: "+t),t=t.replace(wt,kt).toLowerCase(),function(e){var i;do if(i=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return i=i.toLowerCase(),i===t||0===i.indexOf(t+"-");while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var i=t.location&&t.location.hash;return i&&i.slice(1)===e.id},root:function(t){return t===$},focus:function(t){return t===A.activeElement&&(!A.hasFocus||A.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:function(t){return t.disabled===!1},disabled:function(t){return t.disabled===!0},checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,t.selected===!0},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!k.pseudos.empty(t)},header:function(t){return mt.test(t.nodeName)},input:function(t){return gt.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:c(function(){return[0]}),last:c(function(t,e){return[e-1]}),eq:c(function(t,e,i){return[i<0?i+e:i]}),even:c(function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t}),odd:c(function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t}),lt:c(function(t,e,i){for(var n=i<0?i+e:i;--n>=0;)t.push(n);return t}),gt:c(function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t})}},k.pseudos.nth=k.pseudos.eq;for(_ in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})k.pseudos[_]=a(_);for(_ in{submit:!0,reset:!0})k.pseudos[_]=l(_);return d.prototype=k.filters=k.pseudos,k.setFilters=new d,D=e.tokenize=function(t,i){var n,o,s,r,a,l,c,u=Y[t+" "];if(u)return i?0:u.slice(0);for(a=t,l=[],c=k.preFilter;a;){n&&!(o=ct.exec(a))||(o&&(a=a.slice(o[0].length)||a),l.push(s=[])),n=!1,(o=ut.exec(a))&&(n=o.shift(),s.push({value:n,type:o[0].replace(lt," ")}),a=a.slice(n.length));for(r in k.filter)!(o=ft[r].exec(a))||c[r]&&!(o=c[r](o))||(n=o.shift(),s.push({value:n,type:r,matches:o}),a=a.slice(n.length));if(!n)break}return i?a.length:a?e.error(t):Y(t,l).slice(0)},T=e.compile=function(t,e){var i,n=[],o=[],s=B[t+" "];if(!s){for(e||(e=D(t)),i=e.length;i--;)s=y(e[i]),s[O]?n.push(s):o.push(s);s=B(t,b(o,n)),s.selector=t}return s},S=e.select=function(t,e,i,n){var o,s,r,a,l,c="function"==typeof t&&t,d=!n&&D(t=c.selector||t);if(i=i||[],1===d.length){if(s=d[0]=d[0].slice(0),s.length>2&&"ID"===(r=s[0]).type&&w.getById&&9===e.nodeType&&E&&k.relative[s[1].type]){if(e=(k.find.ID(r.matches[0].replace(wt,kt),e)||[])[0],!e)return i;c&&(e=e.parentNode),t=t.slice(s.shift().value.length)}for(o=ft.needsContext.test(t)?0:s.length;o--&&(r=s[o],!k.relative[a=r.type]);)if((l=k.find[a])&&(n=l(r.matches[0].replace(wt,kt),bt.test(s[0].type)&&u(e.parentNode)||e))){if(s.splice(o,1),t=n.length&&h(s),!t)return Z.apply(i,n),i;break}}return(c||T(t,d))(n,e,!E,i,bt.test(t)&&u(e.parentNode)||e),i},w.sortStable=O.split("").sort(X).join("")===O,w.detectDuplicates=!!j,N(),w.sortDetached=o(function(t){return 1&t.compareDocumentPosition(A.createElement("div"))}),o(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||s("type|href|height|width",function(t,e,i){if(!i)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),w.attributes&&o(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||s("value",function(t,e,i){if(!i&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),o(function(t){return null==t.getAttribute("disabled")})||s(et,function(t,e,i){var n;if(!i)return t[e]===!0?e.toLowerCase():(n=t.getAttributeNode(e))&&n.specified?n.value:null}),e}(t);ot.find=ct,ot.expr=ct.selectors,ot.expr[":"]=ot.expr.pseudos,ot.unique=ct.uniqueSort,ot.text=ct.getText,ot.isXMLDoc=ct.isXML,ot.contains=ct.contains;var ut=ot.expr.match.needsContext,dt=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,ht=/^.[^:#\[\.,]*$/;ot.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?ot.find.matchesSelector(n,t)?[n]:[]:ot.find.matches(t,ot.grep(e,function(t){return 1===t.nodeType}))},ot.fn.extend({find:function(t){var e,i=[],n=this,o=n.length;if("string"!=typeof t)return this.pushStack(ot(t).filter(function(){for(e=0;e<o;e++)if(ot.contains(n[e],this))return!0}));for(e=0;e<o;e++)ot.find(t,n[e],i);return i=this.pushStack(o>1?ot.unique(i):i),i.selector=this.selector?this.selector+" "+t:t,i},filter:function(t){return this.pushStack(n(this,t||[],!1))},not:function(t){return this.pushStack(n(this,t||[],!0))},is:function(t){return!!n(this,"string"==typeof t&&ut.test(t)?ot(t):t||[],!1).length}});var pt,ft=t.document,gt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,mt=ot.fn.init=function(t,e){var i,n;if(!t)return this;if("string"==typeof t){if(i="<"===t.charAt(0)&&">"===t.charAt(t.length-1)&&t.length>=3?[null,t,null]:gt.exec(t),!i||!i[1]&&e)return!e||e.jquery?(e||pt).find(t):this.constructor(e).find(t);if(i[1]){if(e=e instanceof ot?e[0]:e,ot.merge(this,ot.parseHTML(i[1],e&&e.nodeType?e.ownerDocument||e:ft,!0)),dt.test(i[1])&&ot.isPlainObject(e))for(i in e)ot.isFunction(this[i])?this[i](e[i]):this.attr(i,e[i]);return this}if(n=ft.getElementById(i[2]),n&&n.parentNode){if(n.id!==i[2])return pt.find(t);this.length=1,this[0]=n}return this.context=ft,this.selector=t,this}return t.nodeType?(this.context=this[0]=t,this.length=1,this):ot.isFunction(t)?"undefined"!=typeof pt.ready?pt.ready(t):t(ot):(void 0!==t.selector&&(this.selector=t.selector,this.context=t.context),ot.makeArray(t,this))};mt.prototype=ot.fn,pt=ot(ft);var vt=/^(?:parents|prev(?:Until|All))/,yt={children:!0,contents:!0,next:!0,prev:!0};ot.extend({dir:function(t,e,i){for(var n=[],o=t[e];o&&9!==o.nodeType&&(void 0===i||1!==o.nodeType||!ot(o).is(i));)1===o.nodeType&&n.push(o),o=o[e];return n},sibling:function(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i}}),ot.fn.extend({has:function(t){var e,i=ot(t,this),n=i.length;return this.filter(function(){for(e=0;e<n;e++)if(ot.contains(this,i[e]))return!0})},closest:function(t,e){for(var i,n=0,o=this.length,s=[],r=ut.test(t)||"string"!=typeof t?ot(t,e||this.context):0;n<o;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(r?r.index(i)>-1:1===i.nodeType&&ot.find.matchesSelector(i,t))){s.push(i);break}return this.pushStack(s.length>1?ot.unique(s):s)},index:function(t){return t?"string"==typeof t?ot.inArray(this[0],ot(t)):ot.inArray(t.jquery?t[0]:t,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(ot.unique(ot.merge(this.get(),ot(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),ot.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return ot.dir(t,"parentNode")},parentsUntil:function(t,e,i){return ot.dir(t,"parentNode",i)},next:function(t){return o(t,"nextSibling")},prev:function(t){return o(t,"previousSibling")},nextAll:function(t){return ot.dir(t,"nextSibling")},prevAll:function(t){return ot.dir(t,"previousSibling")},nextUntil:function(t,e,i){return ot.dir(t,"nextSibling",i)},prevUntil:function(t,e,i){return ot.dir(t,"previousSibling",i)},siblings:function(t){return ot.sibling((t.parentNode||{}).firstChild,t)},children:function(t){return ot.sibling(t.firstChild)},contents:function(t){return ot.nodeName(t,"iframe")?t.contentDocument||t.contentWindow.document:ot.merge([],t.childNodes)}},function(t,e){ot.fn[t]=function(i,n){var o=ot.map(this,e,i);return"Until"!==t.slice(-5)&&(n=i),n&&"string"==typeof n&&(o=ot.filter(n,o)),this.length>1&&(yt[t]||(o=ot.unique(o)),vt.test(t)&&(o=o.reverse())),this.pushStack(o)}});var bt=/\S+/g,_t={};ot.Callbacks=function(t){t="string"==typeof t?_t[t]||s(t):ot.extend({},t);var e,i,n,o,r,a,l=[],c=!t.once&&[],u=function(s){for(i=t.memory&&s,n=!0,r=a||0,a=0,o=l.length,e=!0;l&&r<o;r++)if(l[r].apply(s[0],s[1])===!1&&t.stopOnFalse){i=!1;break}e=!1,l&&(c?c.length&&u(c.shift()):i?l=[]:d.disable())},d={add:function(){if(l){var n=l.length;!function s(e){ot.each(e,function(e,i){var n=ot.type(i);"function"===n?t.unique&&d.has(i)||l.push(i):i&&i.length&&"string"!==n&&s(i)})}(arguments),e?o=l.length:i&&(a=n,u(i))}return this},remove:function(){return l&&ot.each(arguments,function(t,i){for(var n;(n=ot.inArray(i,l,n))>-1;)l.splice(n,1),e&&(n<=o&&o--,n<=r&&r--)}),this},has:function(t){return t?ot.inArray(t,l)>-1:!(!l||!l.length)},empty:function(){return l=[],o=0,this},disable:function(){return l=c=i=void 0,this},disabled:function(){return!l},lock:function(){return c=void 0,i||d.disable(),this},locked:function(){return!c},fireWith:function(t,i){return!l||n&&!c||(i=i||[],i=[t,i.slice?i.slice():i],e?c.push(i):u(i)),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!n}};return d},ot.extend({Deferred:function(t){var e=[["resolve","done",ot.Callbacks("once memory"),"resolved"],["reject","fail",ot.Callbacks("once memory"),"rejected"],["notify","progress",ot.Callbacks("memory")]],i="pending",n={state:function(){return i},always:function(){return o.done(arguments).fail(arguments),this},then:function(){var t=arguments;return ot.Deferred(function(i){ot.each(e,function(e,s){var r=ot.isFunction(t[e])&&t[e];o[s[1]](function(){var t=r&&r.apply(this,arguments);t&&ot.isFunction(t.promise)?t.promise().done(i.resolve).fail(i.reject).progress(i.notify):i[s[0]+"With"](this===n?i.promise():this,r?[t]:arguments)})}),t=null}).promise()},promise:function(t){return null!=t?ot.extend(t,n):n}},o={};return n.pipe=n.then,ot.each(e,function(t,s){var r=s[2],a=s[3];n[s[1]]=r.add,a&&r.add(function(){i=a},e[1^t][2].disable,e[2][2].lock),o[s[0]]=function(){return o[s[0]+"With"](this===o?n:this,arguments),this},o[s[0]+"With"]=r.fireWith}),n.promise(o),t&&t.call(o,o),o},when:function(t){var e,i,n,o=0,s=V.call(arguments),r=s.length,a=1!==r||t&&ot.isFunction(t.promise)?r:0,l=1===a?t:ot.Deferred(),c=function(t,i,n){return function(o){i[t]=this,n[t]=arguments.length>1?V.call(arguments):o,n===e?l.notifyWith(i,n):--a||l.resolveWith(i,n)}};if(r>1)for(e=new Array(r),i=new Array(r),n=new Array(r);o<r;o++)s[o]&&ot.isFunction(s[o].promise)?s[o].promise().done(c(o,n,s)).fail(l.reject).progress(c(o,i,e)):--a;return a||l.resolveWith(n,s),l.promise()}});var wt;ot.fn.ready=function(t){return ot.ready.promise().done(t),this},ot.extend({isReady:!1,readyWait:1,holdReady:function(t){t?ot.readyWait++:ot.ready(!0)},ready:function(t){if(t===!0?!--ot.readyWait:!ot.isReady){if(!ft.body)return setTimeout(ot.ready);ot.isReady=!0,t!==!0&&--ot.readyWait>0||(wt.resolveWith(ft,[ot]),ot.fn.triggerHandler&&(ot(ft).triggerHandler("ready"),ot(ft).off("ready")))}}}),ot.ready.promise=function(e){if(!wt)if(wt=ot.Deferred(),"complete"===ft.readyState)setTimeout(ot.ready);else if(ft.addEventListener)ft.addEventListener("DOMContentLoaded",a,!1),t.addEventListener("load",a,!1);else{ft.attachEvent("onreadystatechange",a),t.attachEvent("onload",a);var i=!1;try{i=null==t.frameElement&&ft.documentElement}catch(n){}i&&i.doScroll&&!function o(){if(!ot.isReady){try{i.doScroll("left")}catch(t){return setTimeout(o,50)}r(),ot.ready()}}()}return wt.promise(e)};var kt,xt="undefined";for(kt in ot(it))break;it.ownLast="0"!==kt,it.inlineBlockNeedsLayout=!1,ot(function(){var t,e,i,n;i=ft.getElementsByTagName("body")[0],i&&i.style&&(e=ft.createElement("div"),n=ft.createElement("div"),n.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",i.appendChild(n).appendChild(e),typeof e.style.zoom!==xt&&(e.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",it.inlineBlockNeedsLayout=t=3===e.offsetWidth,t&&(i.style.zoom=1)),i.removeChild(n))}),function(){var t=ft.createElement("div");if(null==it.deleteExpando){it.deleteExpando=!0;try{delete t.test}catch(e){it.deleteExpando=!1}}t=null}(),ot.acceptData=function(t){var e=ot.noData[(t.nodeName+" ").toLowerCase()],i=+t.nodeType||1;return(1===i||9===i)&&(!e||e!==!0&&t.getAttribute("classid")===e)};var Ct=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Dt=/([A-Z])/g;ot.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(t){return t=t.nodeType?ot.cache[t[ot.expando]]:t[ot.expando],!!t&&!c(t)},data:function(t,e,i){return u(t,e,i)},removeData:function(t,e){return d(t,e)},_data:function(t,e,i){return u(t,e,i,!0)},_removeData:function(t,e){return d(t,e,!0)}}),ot.fn.extend({data:function(t,e){var i,n,o,s=this[0],r=s&&s.attributes;if(void 0===t){if(this.length&&(o=ot.data(s),1===s.nodeType&&!ot._data(s,"parsedAttrs"))){for(i=r.length;i--;)r[i]&&(n=r[i].name,0===n.indexOf("data-")&&(n=ot.camelCase(n.slice(5)),l(s,n,o[n])));ot._data(s,"parsedAttrs",!0)}return o}return"object"==typeof t?this.each(function(){ot.data(this,t)}):arguments.length>1?this.each(function(){ot.data(this,t,e)}):s?l(s,t,ot.data(s,t)):void 0},removeData:function(t){return this.each(function(){ot.removeData(this,t)})}}),ot.extend({queue:function(t,e,i){var n;if(t)return e=(e||"fx")+"queue",n=ot._data(t,e),i&&(!n||ot.isArray(i)?n=ot._data(t,e,ot.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=ot.queue(t,e),n=i.length,o=i.shift(),s=ot._queueHooks(t,e),r=function(){ot.dequeue(t,e)};"inprogress"===o&&(o=i.shift(),n--),o&&("fx"===e&&i.unshift("inprogress"),delete s.stop,o.call(t,r,s)),!n&&s&&s.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return ot._data(t,i)||ot._data(t,i,{empty:ot.Callbacks("once memory").add(function(){ot._removeData(t,e+"queue"),ot._removeData(t,i)})})}}),ot.fn.extend({queue:function(t,e){var i=2;return"string"!=typeof t&&(e=t,t="fx",i--),arguments.length<i?ot.queue(this[0],t):void 0===e?this:this.each(function(){var i=ot.queue(this,t,e);ot._queueHooks(this,t),"fx"===t&&"inprogress"!==i[0]&&ot.dequeue(this,t)})},dequeue:function(t){return this.each(function(){ot.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var i,n=1,o=ot.Deferred(),s=this,r=this.length,a=function(){--n||o.resolveWith(s,[s])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";r--;)i=ot._data(s[r],t+"queueHooks"),i&&i.empty&&(n++,i.empty.add(a));return a(),o.promise(e)}});var Tt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,St=["Top","Right","Bottom","Left"],It=function(t,e){return t=e||t,"none"===ot.css(t,"display")||!ot.contains(t.ownerDocument,t)},Pt=ot.access=function(t,e,i,n,o,s,r){var a=0,l=t.length,c=null==i;if("object"===ot.type(i)){o=!0;for(a in i)ot.access(t,e,a,i[a],!0,s,r)}else if(void 0!==n&&(o=!0,ot.isFunction(n)||(r=!0),c&&(r?(e.call(t,n),e=null):(c=e,e=function(t,e,i){return c.call(ot(t),i)})),e))for(;a<l;a++)e(t[a],i,r?n:n.call(t[a],a,e(t[a],i)));return o?t:c?e.call(t):l?e(t[0],i):s},jt=/^(?:checkbox|radio)$/i;!function(){var t=ft.createElement("input"),e=ft.createElement("div"),i=ft.createDocumentFragment();if(e.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",it.leadingWhitespace=3===e.firstChild.nodeType,it.tbody=!e.getElementsByTagName("tbody").length,it.htmlSerialize=!!e.getElementsByTagName("link").length,
it.html5Clone="<:nav></:nav>"!==ft.createElement("nav").cloneNode(!0).outerHTML,t.type="checkbox",t.checked=!0,i.appendChild(t),it.appendChecked=t.checked,e.innerHTML="<textarea>x</textarea>",it.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue,i.appendChild(e),e.innerHTML="<input type='radio' checked='checked' name='t'/>",it.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,it.noCloneEvent=!0,e.attachEvent&&(e.attachEvent("onclick",function(){it.noCloneEvent=!1}),e.cloneNode(!0).click()),null==it.deleteExpando){it.deleteExpando=!0;try{delete e.test}catch(n){it.deleteExpando=!1}}}(),function(){var e,i,n=ft.createElement("div");for(e in{submit:!0,change:!0,focusin:!0})i="on"+e,(it[e+"Bubbles"]=i in t)||(n.setAttribute(i,"t"),it[e+"Bubbles"]=n.attributes[i].expando===!1);n=null}();var Nt=/^(?:input|select|textarea)$/i,At=/^key/,$t=/^(?:mouse|pointer|contextmenu)|click/,Et=/^(?:focusinfocus|focusoutblur)$/,Mt=/^([^.]*)(?:\.(.+)|)$/;ot.event={global:{},add:function(t,e,i,n,o){var s,r,a,l,c,u,d,h,p,f,g,m=ot._data(t);if(m){for(i.handler&&(l=i,i=l.handler,o=l.selector),i.guid||(i.guid=ot.guid++),(r=m.events)||(r=m.events={}),(u=m.handle)||(u=m.handle=function(t){return typeof ot===xt||t&&ot.event.triggered===t.type?void 0:ot.event.dispatch.apply(u.elem,arguments)},u.elem=t),e=(e||"").match(bt)||[""],a=e.length;a--;)s=Mt.exec(e[a])||[],p=g=s[1],f=(s[2]||"").split(".").sort(),p&&(c=ot.event.special[p]||{},p=(o?c.delegateType:c.bindType)||p,c=ot.event.special[p]||{},d=ot.extend({type:p,origType:g,data:n,handler:i,guid:i.guid,selector:o,needsContext:o&&ot.expr.match.needsContext.test(o),namespace:f.join(".")},l),(h=r[p])||(h=r[p]=[],h.delegateCount=0,c.setup&&c.setup.call(t,n,f,u)!==!1||(t.addEventListener?t.addEventListener(p,u,!1):t.attachEvent&&t.attachEvent("on"+p,u))),c.add&&(c.add.call(t,d),d.handler.guid||(d.handler.guid=i.guid)),o?h.splice(h.delegateCount++,0,d):h.push(d),ot.event.global[p]=!0);t=null}},remove:function(t,e,i,n,o){var s,r,a,l,c,u,d,h,p,f,g,m=ot.hasData(t)&&ot._data(t);if(m&&(u=m.events)){for(e=(e||"").match(bt)||[""],c=e.length;c--;)if(a=Mt.exec(e[c])||[],p=g=a[1],f=(a[2]||"").split(".").sort(),p){for(d=ot.event.special[p]||{},p=(n?d.delegateType:d.bindType)||p,h=u[p]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),l=s=h.length;s--;)r=h[s],!o&&g!==r.origType||i&&i.guid!==r.guid||a&&!a.test(r.namespace)||n&&n!==r.selector&&("**"!==n||!r.selector)||(h.splice(s,1),r.selector&&h.delegateCount--,d.remove&&d.remove.call(t,r));l&&!h.length&&(d.teardown&&d.teardown.call(t,f,m.handle)!==!1||ot.removeEvent(t,p,m.handle),delete u[p])}else for(p in u)ot.event.remove(t,p+e[c],i,n,!0);ot.isEmptyObject(u)&&(delete m.handle,ot._removeData(t,"events"))}},trigger:function(e,i,n,o){var s,r,a,l,c,u,d,h=[n||ft],p=et.call(e,"type")?e.type:e,f=et.call(e,"namespace")?e.namespace.split("."):[];if(a=u=n=n||ft,3!==n.nodeType&&8!==n.nodeType&&!Et.test(p+ot.event.triggered)&&(p.indexOf(".")>=0&&(f=p.split("."),p=f.shift(),f.sort()),r=p.indexOf(":")<0&&"on"+p,e=e[ot.expando]?e:new ot.Event(p,"object"==typeof e&&e),e.isTrigger=o?2:3,e.namespace=f.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),i=null==i?[e]:ot.makeArray(i,[e]),c=ot.event.special[p]||{},o||!c.trigger||c.trigger.apply(n,i)!==!1)){if(!o&&!c.noBubble&&!ot.isWindow(n)){for(l=c.delegateType||p,Et.test(l+p)||(a=a.parentNode);a;a=a.parentNode)h.push(a),u=a;u===(n.ownerDocument||ft)&&h.push(u.defaultView||u.parentWindow||t)}for(d=0;(a=h[d++])&&!e.isPropagationStopped();)e.type=d>1?l:c.bindType||p,s=(ot._data(a,"events")||{})[e.type]&&ot._data(a,"handle"),s&&s.apply(a,i),s=r&&a[r],s&&s.apply&&ot.acceptData(a)&&(e.result=s.apply(a,i),e.result===!1&&e.preventDefault());if(e.type=p,!o&&!e.isDefaultPrevented()&&(!c._default||c._default.apply(h.pop(),i)===!1)&&ot.acceptData(n)&&r&&n[p]&&!ot.isWindow(n)){u=n[r],u&&(n[r]=null),ot.event.triggered=p;try{n[p]()}catch(g){}ot.event.triggered=void 0,u&&(n[r]=u)}return e.result}},dispatch:function(t){t=ot.event.fix(t);var e,i,n,o,s,r=[],a=V.call(arguments),l=(ot._data(this,"events")||{})[t.type]||[],c=ot.event.special[t.type]||{};if(a[0]=t,t.delegateTarget=this,!c.preDispatch||c.preDispatch.call(this,t)!==!1){for(r=ot.event.handlers.call(this,t,l),e=0;(o=r[e++])&&!t.isPropagationStopped();)for(t.currentTarget=o.elem,s=0;(n=o.handlers[s++])&&!t.isImmediatePropagationStopped();)t.namespace_re&&!t.namespace_re.test(n.namespace)||(t.handleObj=n,t.data=n.data,i=((ot.event.special[n.origType]||{}).handle||n.handler).apply(o.elem,a),void 0!==i&&(t.result=i)===!1&&(t.preventDefault(),t.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,t),t.result}},handlers:function(t,e){var i,n,o,s,r=[],a=e.delegateCount,l=t.target;if(a&&l.nodeType&&(!t.button||"click"!==t.type))for(;l!=this;l=l.parentNode||this)if(1===l.nodeType&&(l.disabled!==!0||"click"!==t.type)){for(o=[],s=0;s<a;s++)n=e[s],i=n.selector+" ",void 0===o[i]&&(o[i]=n.needsContext?ot(i,this).index(l)>=0:ot.find(i,this,null,[l]).length),o[i]&&o.push(n);o.length&&r.push({elem:l,handlers:o})}return a<e.length&&r.push({elem:this,handlers:e.slice(a)}),r},fix:function(t){if(t[ot.expando])return t;var e,i,n,o=t.type,s=t,r=this.fixHooks[o];for(r||(this.fixHooks[o]=r=$t.test(o)?this.mouseHooks:At.test(o)?this.keyHooks:{}),n=r.props?this.props.concat(r.props):this.props,t=new ot.Event(s),e=n.length;e--;)i=n[e],t[i]=s[i];return t.target||(t.target=s.srcElement||ft),3===t.target.nodeType&&(t.target=t.target.parentNode),t.metaKey=!!t.metaKey,r.filter?r.filter(t,s):t},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(t,e){return null==t.which&&(t.which=null!=e.charCode?e.charCode:e.keyCode),t}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(t,e){var i,n,o,s=e.button,r=e.fromElement;return null==t.pageX&&null!=e.clientX&&(n=t.target.ownerDocument||ft,o=n.documentElement,i=n.body,t.pageX=e.clientX+(o&&o.scrollLeft||i&&i.scrollLeft||0)-(o&&o.clientLeft||i&&i.clientLeft||0),t.pageY=e.clientY+(o&&o.scrollTop||i&&i.scrollTop||0)-(o&&o.clientTop||i&&i.clientTop||0)),!t.relatedTarget&&r&&(t.relatedTarget=r===t.target?e.toElement:r),t.which||void 0===s||(t.which=1&s?1:2&s?3:4&s?2:0),t}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==f()&&this.focus)try{return this.focus(),!1}catch(t){}},delegateType:"focusin"},blur:{trigger:function(){if(this===f()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(ot.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(t){return ot.nodeName(t.target,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}},simulate:function(t,e,i,n){var o=ot.extend(new ot.Event,i,{type:t,isSimulated:!0,originalEvent:{}});n?ot.event.trigger(o,null,e):ot.event.dispatch.call(e,o),o.isDefaultPrevented()&&i.preventDefault()}},ot.removeEvent=ft.removeEventListener?function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i,!1)}:function(t,e,i){var n="on"+e;t.detachEvent&&(typeof t[n]===xt&&(t[n]=null),t.detachEvent(n,i))},ot.Event=function(t,e){return this instanceof ot.Event?(t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&t.returnValue===!1?h:p):this.type=t,e&&ot.extend(this,e),this.timeStamp=t&&t.timeStamp||ot.now(),void(this[ot.expando]=!0)):new ot.Event(t,e)},ot.Event.prototype={isDefaultPrevented:p,isPropagationStopped:p,isImmediatePropagationStopped:p,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=h,t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=h,t&&(t.stopPropagation&&t.stopPropagation(),t.cancelBubble=!0)},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=h,t&&t.stopImmediatePropagation&&t.stopImmediatePropagation(),this.stopPropagation()}},ot.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){ot.event.special[t]={delegateType:e,bindType:e,handle:function(t){var i,n=this,o=t.relatedTarget,s=t.handleObj;return o&&(o===n||ot.contains(n,o))||(t.type=s.origType,i=s.handler.apply(this,arguments),t.type=e),i}}}),it.submitBubbles||(ot.event.special.submit={setup:function(){return!ot.nodeName(this,"form")&&void ot.event.add(this,"click._submit keypress._submit",function(t){var e=t.target,i=ot.nodeName(e,"input")||ot.nodeName(e,"button")?e.form:void 0;i&&!ot._data(i,"submitBubbles")&&(ot.event.add(i,"submit._submit",function(t){t._submit_bubble=!0}),ot._data(i,"submitBubbles",!0))})},postDispatch:function(t){t._submit_bubble&&(delete t._submit_bubble,this.parentNode&&!t.isTrigger&&ot.event.simulate("submit",this.parentNode,t,!0))},teardown:function(){return!ot.nodeName(this,"form")&&void ot.event.remove(this,"._submit")}}),it.changeBubbles||(ot.event.special.change={setup:function(){return Nt.test(this.nodeName)?("checkbox"!==this.type&&"radio"!==this.type||(ot.event.add(this,"propertychange._change",function(t){"checked"===t.originalEvent.propertyName&&(this._just_changed=!0)}),ot.event.add(this,"click._change",function(t){this._just_changed&&!t.isTrigger&&(this._just_changed=!1),ot.event.simulate("change",this,t,!0)})),!1):void ot.event.add(this,"beforeactivate._change",function(t){var e=t.target;Nt.test(e.nodeName)&&!ot._data(e,"changeBubbles")&&(ot.event.add(e,"change._change",function(t){!this.parentNode||t.isSimulated||t.isTrigger||ot.event.simulate("change",this.parentNode,t,!0)}),ot._data(e,"changeBubbles",!0))})},handle:function(t){var e=t.target;if(this!==e||t.isSimulated||t.isTrigger||"radio"!==e.type&&"checkbox"!==e.type)return t.handleObj.handler.apply(this,arguments)},teardown:function(){return ot.event.remove(this,"._change"),!Nt.test(this.nodeName)}}),it.focusinBubbles||ot.each({focus:"focusin",blur:"focusout"},function(t,e){var i=function(t){ot.event.simulate(e,t.target,ot.event.fix(t),!0)};ot.event.special[e]={setup:function(){var n=this.ownerDocument||this,o=ot._data(n,e);o||n.addEventListener(t,i,!0),ot._data(n,e,(o||0)+1)},teardown:function(){var n=this.ownerDocument||this,o=ot._data(n,e)-1;o?ot._data(n,e,o):(n.removeEventListener(t,i,!0),ot._removeData(n,e))}}}),ot.fn.extend({on:function(t,e,i,n,o){var s,r;if("object"==typeof t){"string"!=typeof e&&(i=i||e,e=void 0);for(s in t)this.on(s,e,i,t[s],o);return this}if(null==i&&null==n?(n=e,i=e=void 0):null==n&&("string"==typeof e?(n=i,i=void 0):(n=i,i=e,e=void 0)),n===!1)n=p;else if(!n)return this;return 1===o&&(r=n,n=function(t){return ot().off(t),r.apply(this,arguments)},n.guid=r.guid||(r.guid=ot.guid++)),this.each(function(){ot.event.add(this,t,n,i,e)})},one:function(t,e,i,n){return this.on(t,e,i,n,1)},off:function(t,e,i){var n,o;if(t&&t.preventDefault&&t.handleObj)return n=t.handleObj,ot(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"==typeof t){for(o in t)this.off(o,e,t[o]);return this}return e!==!1&&"function"!=typeof e||(i=e,e=void 0),i===!1&&(i=p),this.each(function(){ot.event.remove(this,t,i,e)})},trigger:function(t,e){return this.each(function(){ot.event.trigger(t,e,this)})},triggerHandler:function(t,e){var i=this[0];if(i)return ot.event.trigger(t,e,i,!0)}});var Ht="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",Lt=/ jQuery\d+="(?:null|\d+)"/g,zt=new RegExp("<(?:"+Ht+")[\\s/>]","i"),Ot=/^\s+/,qt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,Rt=/<([\w:]+)/,Wt=/<tbody/i,Ft=/<|&#?\w+;/,Yt=/<(?:script|style|link)/i,Bt=/checked\s*(?:[^=]|=\s*.checked.)/i,Xt=/^$|\/(?:java|ecma)script/i,Ut=/^true\/(.*)/,Kt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Vt={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:it.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},Qt=g(ft),Gt=Qt.appendChild(ft.createElement("div"));Vt.optgroup=Vt.option,Vt.tbody=Vt.tfoot=Vt.colgroup=Vt.caption=Vt.thead,Vt.th=Vt.td,ot.extend({clone:function(t,e,i){var n,o,s,r,a,l=ot.contains(t.ownerDocument,t);if(it.html5Clone||ot.isXMLDoc(t)||!zt.test("<"+t.nodeName+">")?s=t.cloneNode(!0):(Gt.innerHTML=t.outerHTML,Gt.removeChild(s=Gt.firstChild)),!(it.noCloneEvent&&it.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||ot.isXMLDoc(t)))for(n=m(s),a=m(t),r=0;null!=(o=a[r]);++r)n[r]&&x(o,n[r]);if(e)if(i)for(a=a||m(t),n=n||m(s),r=0;null!=(o=a[r]);r++)k(o,n[r]);else k(t,s);return n=m(s,"script"),n.length>0&&w(n,!l&&m(t,"script")),n=a=o=null,s},buildFragment:function(t,e,i,n){for(var o,s,r,a,l,c,u,d=t.length,h=g(e),p=[],f=0;f<d;f++)if(s=t[f],s||0===s)if("object"===ot.type(s))ot.merge(p,s.nodeType?[s]:s);else if(Ft.test(s)){for(a=a||h.appendChild(e.createElement("div")),l=(Rt.exec(s)||["",""])[1].toLowerCase(),u=Vt[l]||Vt._default,a.innerHTML=u[1]+s.replace(qt,"<$1></$2>")+u[2],o=u[0];o--;)a=a.lastChild;if(!it.leadingWhitespace&&Ot.test(s)&&p.push(e.createTextNode(Ot.exec(s)[0])),!it.tbody)for(s="table"!==l||Wt.test(s)?"<table>"!==u[1]||Wt.test(s)?0:a:a.firstChild,o=s&&s.childNodes.length;o--;)ot.nodeName(c=s.childNodes[o],"tbody")&&!c.childNodes.length&&s.removeChild(c);for(ot.merge(p,a.childNodes),a.textContent="";a.firstChild;)a.removeChild(a.firstChild);a=h.lastChild}else p.push(e.createTextNode(s));for(a&&h.removeChild(a),it.appendChecked||ot.grep(m(p,"input"),v),f=0;s=p[f++];)if((!n||ot.inArray(s,n)===-1)&&(r=ot.contains(s.ownerDocument,s),a=m(h.appendChild(s),"script"),r&&w(a),i))for(o=0;s=a[o++];)Xt.test(s.type||"")&&i.push(s);return a=null,h},cleanData:function(t,e){for(var i,n,o,s,r=0,a=ot.expando,l=ot.cache,c=it.deleteExpando,u=ot.event.special;null!=(i=t[r]);r++)if((e||ot.acceptData(i))&&(o=i[a],s=o&&l[o])){if(s.events)for(n in s.events)u[n]?ot.event.remove(i,n):ot.removeEvent(i,n,s.handle);l[o]&&(delete l[o],c?delete i[a]:typeof i.removeAttribute!==xt?i.removeAttribute(a):i[a]=null,K.push(o))}}}),ot.fn.extend({text:function(t){return Pt(this,function(t){return void 0===t?ot.text(this):this.empty().append((this[0]&&this[0].ownerDocument||ft).createTextNode(t))},null,t,arguments.length)},append:function(){return this.domManip(arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=y(this,t);e.appendChild(t)}})},prepend:function(){return this.domManip(arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=y(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return this.domManip(arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return this.domManip(arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},remove:function(t,e){for(var i,n=t?ot.filter(t,this):this,o=0;null!=(i=n[o]);o++)e||1!==i.nodeType||ot.cleanData(m(i)),i.parentNode&&(e&&ot.contains(i.ownerDocument,i)&&w(m(i,"script")),i.parentNode.removeChild(i));return this},empty:function(){for(var t,e=0;null!=(t=this[e]);e++){for(1===t.nodeType&&ot.cleanData(m(t,!1));t.firstChild;)t.removeChild(t.firstChild);t.options&&ot.nodeName(t,"select")&&(t.options.length=0)}return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return ot.clone(this,t,e)})},html:function(t){return Pt(this,function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t)return 1===e.nodeType?e.innerHTML.replace(Lt,""):void 0;if("string"==typeof t&&!Yt.test(t)&&(it.htmlSerialize||!zt.test(t))&&(it.leadingWhitespace||!Ot.test(t))&&!Vt[(Rt.exec(t)||["",""])[1].toLowerCase()]){t=t.replace(qt,"<$1></$2>");try{for(;i<n;i++)e=this[i]||{},1===e.nodeType&&(ot.cleanData(m(e,!1)),e.innerHTML=t);e=0}catch(o){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=arguments[0];return this.domManip(arguments,function(e){t=this.parentNode,ot.cleanData(m(this)),t&&t.replaceChild(e,this)}),t&&(t.length||t.nodeType)?this:this.remove()},detach:function(t){return this.remove(t,!0)},domManip:function(t,e){t=Q.apply([],t);var i,n,o,s,r,a,l=0,c=this.length,u=this,d=c-1,h=t[0],p=ot.isFunction(h);if(p||c>1&&"string"==typeof h&&!it.checkClone&&Bt.test(h))return this.each(function(i){var n=u.eq(i);p&&(t[0]=h.call(this,i,n.html())),n.domManip(t,e)});if(c&&(a=ot.buildFragment(t,this[0].ownerDocument,!1,this),i=a.firstChild,1===a.childNodes.length&&(a=i),i)){for(s=ot.map(m(a,"script"),b),o=s.length;l<c;l++)n=a,l!==d&&(n=ot.clone(n,!0,!0),o&&ot.merge(s,m(n,"script"))),e.call(this[l],n,l);if(o)for(r=s[s.length-1].ownerDocument,ot.map(s,_),l=0;l<o;l++)n=s[l],Xt.test(n.type||"")&&!ot._data(n,"globalEval")&&ot.contains(r,n)&&(n.src?ot._evalUrl&&ot._evalUrl(n.src):ot.globalEval((n.text||n.textContent||n.innerHTML||"").replace(Kt,"")));a=i=null}return this}}),ot.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){ot.fn[t]=function(t){for(var i,n=0,o=[],s=ot(t),r=s.length-1;n<=r;n++)i=n===r?this:this.clone(!0),ot(s[n])[e](i),G.apply(o,i.get());return this.pushStack(o)}});var Zt,Jt={};!function(){var t;it.shrinkWrapBlocks=function(){if(null!=t)return t;t=!1;var e,i,n;return i=ft.getElementsByTagName("body")[0],i&&i.style?(e=ft.createElement("div"),n=ft.createElement("div"),n.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",i.appendChild(n).appendChild(e),typeof e.style.zoom!==xt&&(e.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",e.appendChild(ft.createElement("div")).style.width="5px",t=3!==e.offsetWidth),i.removeChild(n),t):void 0}}();var te,ee,ie=/^margin/,ne=new RegExp("^("+Tt+")(?!px)[a-z%]+$","i"),oe=/^(top|right|bottom|left)$/;t.getComputedStyle?(te=function(e){return e.ownerDocument.defaultView.opener?e.ownerDocument.defaultView.getComputedStyle(e,null):t.getComputedStyle(e,null)},ee=function(t,e,i){var n,o,s,r,a=t.style;return i=i||te(t),r=i?i.getPropertyValue(e)||i[e]:void 0,i&&(""!==r||ot.contains(t.ownerDocument,t)||(r=ot.style(t,e)),ne.test(r)&&ie.test(e)&&(n=a.width,o=a.minWidth,s=a.maxWidth,a.minWidth=a.maxWidth=a.width=r,r=i.width,a.width=n,a.minWidth=o,a.maxWidth=s)),void 0===r?r:r+""}):ft.documentElement.currentStyle&&(te=function(t){return t.currentStyle},ee=function(t,e,i){var n,o,s,r,a=t.style;return i=i||te(t),r=i?i[e]:void 0,null==r&&a&&a[e]&&(r=a[e]),ne.test(r)&&!oe.test(e)&&(n=a.left,o=t.runtimeStyle,s=o&&o.left,s&&(o.left=t.currentStyle.left),a.left="fontSize"===e?"1em":r,r=a.pixelLeft+"px",a.left=n,s&&(o.left=s)),void 0===r?r:r+""||"auto"}),function(){function e(){var e,i,n,o;i=ft.getElementsByTagName("body")[0],i&&i.style&&(e=ft.createElement("div"),n=ft.createElement("div"),n.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",i.appendChild(n).appendChild(e),e.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",s=r=!1,l=!0,t.getComputedStyle&&(s="1%"!==(t.getComputedStyle(e,null)||{}).top,r="4px"===(t.getComputedStyle(e,null)||{width:"4px"}).width,o=e.appendChild(ft.createElement("div")),o.style.cssText=e.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",o.style.marginRight=o.style.width="0",e.style.width="1px",l=!parseFloat((t.getComputedStyle(o,null)||{}).marginRight),e.removeChild(o)),e.innerHTML="<table><tr><td></td><td>t</td></tr></table>",o=e.getElementsByTagName("td"),o[0].style.cssText="margin:0;border:0;padding:0;display:none",a=0===o[0].offsetHeight,a&&(o[0].style.display="",o[1].style.display="none",a=0===o[0].offsetHeight),i.removeChild(n))}var i,n,o,s,r,a,l;i=ft.createElement("div"),i.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",o=i.getElementsByTagName("a")[0],n=o&&o.style,n&&(n.cssText="float:left;opacity:.5",it.opacity="0.5"===n.opacity,it.cssFloat=!!n.cssFloat,i.style.backgroundClip="content-box",i.cloneNode(!0).style.backgroundClip="",it.clearCloneStyle="content-box"===i.style.backgroundClip,it.boxSizing=""===n.boxSizing||""===n.MozBoxSizing||""===n.WebkitBoxSizing,ot.extend(it,{reliableHiddenOffsets:function(){return null==a&&e(),a},boxSizingReliable:function(){return null==r&&e(),r},pixelPosition:function(){return null==s&&e(),s},reliableMarginRight:function(){return null==l&&e(),l}}))}(),ot.swap=function(t,e,i,n){var o,s,r={};for(s in e)r[s]=t.style[s],t.style[s]=e[s];o=i.apply(t,n||[]);for(s in e)t.style[s]=r[s];return o};var se=/alpha\([^)]*\)/i,re=/opacity\s*=\s*([^)]*)/,ae=/^(none|table(?!-c[ea]).+)/,le=new RegExp("^("+Tt+")(.*)$","i"),ce=new RegExp("^([+-])=("+Tt+")","i"),ue={position:"absolute",visibility:"hidden",display:"block"},de={letterSpacing:"0",fontWeight:"400"},he=["Webkit","O","Moz","ms"];ot.extend({cssHooks:{opacity:{get:function(t,e){if(e){var i=ee(t,"opacity");return""===i?"1":i}}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":it.cssFloat?"cssFloat":"styleFloat"},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,s,r,a=ot.camelCase(e),l=t.style;if(e=ot.cssProps[a]||(ot.cssProps[a]=S(l,a)),r=ot.cssHooks[e]||ot.cssHooks[a],void 0===i)return r&&"get"in r&&void 0!==(o=r.get(t,!1,n))?o:l[e];if(s=typeof i,"string"===s&&(o=ce.exec(i))&&(i=(o[1]+1)*o[2]+parseFloat(ot.css(t,e)),s="number"),null!=i&&i===i&&("number"!==s||ot.cssNumber[a]||(i+="px"),it.clearCloneStyle||""!==i||0!==e.indexOf("background")||(l[e]="inherit"),!(r&&"set"in r&&void 0===(i=r.set(t,i,n)))))try{l[e]=i}catch(c){}}},css:function(t,e,i,n){var o,s,r,a=ot.camelCase(e);return e=ot.cssProps[a]||(ot.cssProps[a]=S(t.style,a)),r=ot.cssHooks[e]||ot.cssHooks[a],r&&"get"in r&&(s=r.get(t,!0,i)),void 0===s&&(s=ee(t,e,n)),"normal"===s&&e in de&&(s=de[e]),""===i||i?(o=parseFloat(s),i===!0||ot.isNumeric(o)?o||0:s):s}}),ot.each(["height","width"],function(t,e){ot.cssHooks[e]={get:function(t,i,n){if(i)return ae.test(ot.css(t,"display"))&&0===t.offsetWidth?ot.swap(t,ue,function(){return N(t,e,n)}):N(t,e,n)},set:function(t,i,n){var o=n&&te(t);return P(t,i,n?j(t,e,n,it.boxSizing&&"border-box"===ot.css(t,"boxSizing",!1,o),o):0)}}}),it.opacity||(ot.cssHooks.opacity={get:function(t,e){return re.test((e&&t.currentStyle?t.currentStyle.filter:t.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":e?"1":""},set:function(t,e){var i=t.style,n=t.currentStyle,o=ot.isNumeric(e)?"alpha(opacity="+100*e+")":"",s=n&&n.filter||i.filter||"";i.zoom=1,(e>=1||""===e)&&""===ot.trim(s.replace(se,""))&&i.removeAttribute&&(i.removeAttribute("filter"),""===e||n&&!n.filter)||(i.filter=se.test(s)?s.replace(se,o):s+" "+o)}}),ot.cssHooks.marginRight=T(it.reliableMarginRight,function(t,e){if(e)return ot.swap(t,{display:"inline-block"},ee,[t,"marginRight"])}),ot.each({margin:"",padding:"",border:"Width"},function(t,e){ot.cssHooks[t+e]={expand:function(i){for(var n=0,o={},s="string"==typeof i?i.split(" "):[i];n<4;n++)o[t+St[n]+e]=s[n]||s[n-2]||s[0];return o}},ie.test(t)||(ot.cssHooks[t+e].set=P)}),ot.fn.extend({css:function(t,e){return Pt(this,function(t,e,i){var n,o,s={},r=0;if(ot.isArray(e)){for(n=te(t),o=e.length;r<o;r++)s[e[r]]=ot.css(t,e[r],!1,n);return s}return void 0!==i?ot.style(t,e,i):ot.css(t,e)},t,e,arguments.length>1)},show:function(){return I(this,!0)},hide:function(){return I(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){It(this)?ot(this).show():ot(this).hide()})}}),ot.Tween=A,A.prototype={constructor:A,init:function(t,e,i,n,o,s){this.elem=t,this.prop=i,this.easing=o||"swing",this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=s||(ot.cssNumber[i]?"":"px")},cur:function(){var t=A.propHooks[this.prop];return t&&t.get?t.get(this):A.propHooks._default.get(this)},run:function(t){var e,i=A.propHooks[this.prop];return this.options.duration?this.pos=e=ot.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):A.propHooks._default.set(this),this}},A.prototype.init.prototype=A.prototype,A.propHooks={_default:{get:function(t){var e;return null==t.elem[t.prop]||t.elem.style&&null!=t.elem.style[t.prop]?(e=ot.css(t.elem,t.prop,""),e&&"auto"!==e?e:0):t.elem[t.prop]},set:function(t){ot.fx.step[t.prop]?ot.fx.step[t.prop](t):t.elem.style&&(null!=t.elem.style[ot.cssProps[t.prop]]||ot.cssHooks[t.prop])?ot.style(t.elem,t.prop,t.now+t.unit):t.elem[t.prop]=t.now}}},A.propHooks.scrollTop=A.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},ot.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2}},ot.fx=A.prototype.init,ot.fx.step={};var pe,fe,ge=/^(?:toggle|show|hide)$/,me=new RegExp("^(?:([+-])=|)("+Tt+")([a-z%]*)$","i"),ve=/queueHooks$/,ye=[H],be={"*":[function(t,e){var i=this.createTween(t,e),n=i.cur(),o=me.exec(e),s=o&&o[3]||(ot.cssNumber[t]?"":"px"),r=(ot.cssNumber[t]||"px"!==s&&+n)&&me.exec(ot.css(i.elem,t)),a=1,l=20;if(r&&r[3]!==s){s=s||r[3],o=o||[],r=+n||1;do a=a||".5",r/=a,ot.style(i.elem,t,r+s);while(a!==(a=i.cur()/n)&&1!==a&&--l)}return o&&(r=i.start=+r||+n||0,i.unit=s,i.end=o[1]?r+(o[1]+1)*o[2]:+o[2]),i}]};ot.Animation=ot.extend(z,{tweener:function(t,e){ot.isFunction(t)?(e=t,t=["*"]):t=t.split(" ");for(var i,n=0,o=t.length;n<o;n++)i=t[n],be[i]=be[i]||[],be[i].unshift(e)},prefilter:function(t,e){e?ye.unshift(t):ye.push(t)}}),ot.speed=function(t,e,i){var n=t&&"object"==typeof t?ot.extend({},t):{complete:i||!i&&e||ot.isFunction(t)&&t,duration:t,easing:i&&e||e&&!ot.isFunction(e)&&e};return n.duration=ot.fx.off?0:"number"==typeof n.duration?n.duration:n.duration in ot.fx.speeds?ot.fx.speeds[n.duration]:ot.fx.speeds._default,null!=n.queue&&n.queue!==!0||(n.queue="fx"),n.old=n.complete,n.complete=function(){ot.isFunction(n.old)&&n.old.call(this),n.queue&&ot.dequeue(this,n.queue)},n},ot.fn.extend({fadeTo:function(t,e,i,n){return this.filter(It).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(t,e,i,n){var o=ot.isEmptyObject(t),s=ot.speed(e,i,n),r=function(){var e=z(this,ot.extend({},t),s);(o||ot._data(this,"finish"))&&e.stop(!0)};return r.finish=r,o||s.queue===!1?this.each(r):this.queue(s.queue,r)},stop:function(t,e,i){var n=function(t){var e=t.stop;delete t.stop,e(i)};return"string"!=typeof t&&(i=e,e=t,t=void 0),e&&t!==!1&&this.queue(t||"fx",[]),this.each(function(){var e=!0,o=null!=t&&t+"queueHooks",s=ot.timers,r=ot._data(this);if(o)r[o]&&r[o].stop&&n(r[o]);else for(o in r)r[o]&&r[o].stop&&ve.test(o)&&n(r[o]);for(o=s.length;o--;)s[o].elem!==this||null!=t&&s[o].queue!==t||(s[o].anim.stop(i),e=!1,s.splice(o,1));!e&&i||ot.dequeue(this,t)})},finish:function(t){return t!==!1&&(t=t||"fx"),this.each(function(){var e,i=ot._data(this),n=i[t+"queue"],o=i[t+"queueHooks"],s=ot.timers,r=n?n.length:0;for(i.finish=!0,ot.queue(this,t,[]),o&&o.stop&&o.stop.call(this,!0),e=s.length;e--;)s[e].elem===this&&s[e].queue===t&&(s[e].anim.stop(!0),s.splice(e,1));for(e=0;e<r;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete i.finish})}}),ot.each(["toggle","show","hide"],function(t,e){var i=ot.fn[e];ot.fn[e]=function(t,n,o){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(E(e,!0),t,n,o)}}),ot.each({slideDown:E("show"),slideUp:E("hide"),slideToggle:E("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){ot.fn[t]=function(t,i,n){return this.animate(e,t,i,n)}}),ot.timers=[],ot.fx.tick=function(){var t,e=ot.timers,i=0;for(pe=ot.now();i<e.length;i++)t=e[i],t()||e[i]!==t||e.splice(i--,1);e.length||ot.fx.stop(),pe=void 0},ot.fx.timer=function(t){ot.timers.push(t),t()?ot.fx.start():ot.timers.pop()},ot.fx.interval=13,ot.fx.start=function(){fe||(fe=setInterval(ot.fx.tick,ot.fx.interval))},ot.fx.stop=function(){clearInterval(fe),fe=null},ot.fx.speeds={slow:600,fast:200,_default:400},ot.fn.delay=function(t,e){return t=ot.fx?ot.fx.speeds[t]||t:t,e=e||"fx",this.queue(e,function(e,i){var n=setTimeout(e,t);i.stop=function(){clearTimeout(n)}})},function(){var t,e,i,n,o;e=ft.createElement("div"),e.setAttribute("className","t"),e.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",n=e.getElementsByTagName("a")[0],i=ft.createElement("select"),o=i.appendChild(ft.createElement("option")),t=e.getElementsByTagName("input")[0],n.style.cssText="top:1px",it.getSetAttribute="t"!==e.className,it.style=/top/.test(n.getAttribute("style")),it.hrefNormalized="/a"===n.getAttribute("href"),it.checkOn=!!t.value,it.optSelected=o.selected,it.enctype=!!ft.createElement("form").enctype,i.disabled=!0,it.optDisabled=!o.disabled,t=ft.createElement("input"),t.setAttribute("value",""),it.input=""===t.getAttribute("value"),t.value="t",t.setAttribute("type","radio"),it.radioValue="t"===t.value}();var _e=/\r/g;ot.fn.extend({val:function(t){var e,i,n,o=this[0];{if(arguments.length)return n=ot.isFunction(t),this.each(function(i){var o;1===this.nodeType&&(o=n?t.call(this,i,ot(this).val()):t,null==o?o="":"number"==typeof o?o+="":ot.isArray(o)&&(o=ot.map(o,function(t){return null==t?"":t+""})),e=ot.valHooks[this.type]||ot.valHooks[this.nodeName.toLowerCase()],e&&"set"in e&&void 0!==e.set(this,o,"value")||(this.value=o))});if(o)return e=ot.valHooks[o.type]||ot.valHooks[o.nodeName.toLowerCase()],e&&"get"in e&&void 0!==(i=e.get(o,"value"))?i:(i=o.value,"string"==typeof i?i.replace(_e,""):null==i?"":i)}}}),ot.extend({valHooks:{option:{get:function(t){var e=ot.find.attr(t,"value");return null!=e?e:ot.trim(ot.text(t))}},select:{get:function(t){for(var e,i,n=t.options,o=t.selectedIndex,s="select-one"===t.type||o<0,r=s?null:[],a=s?o+1:n.length,l=o<0?a:s?o:0;l<a;l++)if(i=n[l],(i.selected||l===o)&&(it.optDisabled?!i.disabled:null===i.getAttribute("disabled"))&&(!i.parentNode.disabled||!ot.nodeName(i.parentNode,"optgroup"))){if(e=ot(i).val(),s)return e;r.push(e)}return r},set:function(t,e){for(var i,n,o=t.options,s=ot.makeArray(e),r=o.length;r--;)if(n=o[r],ot.inArray(ot.valHooks.option.get(n),s)>=0)try{n.selected=i=!0}catch(a){n.scrollHeight}else n.selected=!1;return i||(t.selectedIndex=-1),o}}}}),ot.each(["radio","checkbox"],function(){ot.valHooks[this]={set:function(t,e){if(ot.isArray(e))return t.checked=ot.inArray(ot(t).val(),e)>=0}},it.checkOn||(ot.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var we,ke,xe=ot.expr.attrHandle,Ce=/^(?:checked|selected)$/i,De=it.getSetAttribute,Te=it.input;ot.fn.extend({attr:function(t,e){return Pt(this,ot.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each(function(){ot.removeAttr(this,t)})}}),ot.extend({attr:function(t,e,i){var n,o,s=t.nodeType;if(t&&3!==s&&8!==s&&2!==s)return typeof t.getAttribute===xt?ot.prop(t,e,i):(1===s&&ot.isXMLDoc(t)||(e=e.toLowerCase(),n=ot.attrHooks[e]||(ot.expr.match.bool.test(e)?ke:we)),void 0===i?n&&"get"in n&&null!==(o=n.get(t,e))?o:(o=ot.find.attr(t,e),null==o?void 0:o):null!==i?n&&"set"in n&&void 0!==(o=n.set(t,i,e))?o:(t.setAttribute(e,i+""),
i):void ot.removeAttr(t,e))},removeAttr:function(t,e){var i,n,o=0,s=e&&e.match(bt);if(s&&1===t.nodeType)for(;i=s[o++];)n=ot.propFix[i]||i,ot.expr.match.bool.test(i)?Te&&De||!Ce.test(i)?t[n]=!1:t[ot.camelCase("default-"+i)]=t[n]=!1:ot.attr(t,i,""),t.removeAttribute(De?i:n)},attrHooks:{type:{set:function(t,e){if(!it.radioValue&&"radio"===e&&ot.nodeName(t,"input")){var i=t.value;return t.setAttribute("type",e),i&&(t.value=i),e}}}}}),ke={set:function(t,e,i){return e===!1?ot.removeAttr(t,i):Te&&De||!Ce.test(i)?t.setAttribute(!De&&ot.propFix[i]||i,i):t[ot.camelCase("default-"+i)]=t[i]=!0,i}},ot.each(ot.expr.match.bool.source.match(/\w+/g),function(t,e){var i=xe[e]||ot.find.attr;xe[e]=Te&&De||!Ce.test(e)?function(t,e,n){var o,s;return n||(s=xe[e],xe[e]=o,o=null!=i(t,e,n)?e.toLowerCase():null,xe[e]=s),o}:function(t,e,i){if(!i)return t[ot.camelCase("default-"+e)]?e.toLowerCase():null}}),Te&&De||(ot.attrHooks.value={set:function(t,e,i){return ot.nodeName(t,"input")?void(t.defaultValue=e):we&&we.set(t,e,i)}}),De||(we={set:function(t,e,i){var n=t.getAttributeNode(i);if(n||t.setAttributeNode(n=t.ownerDocument.createAttribute(i)),n.value=e+="","value"===i||e===t.getAttribute(i))return e}},xe.id=xe.name=xe.coords=function(t,e,i){var n;if(!i)return(n=t.getAttributeNode(e))&&""!==n.value?n.value:null},ot.valHooks.button={get:function(t,e){var i=t.getAttributeNode(e);if(i&&i.specified)return i.value},set:we.set},ot.attrHooks.contenteditable={set:function(t,e,i){we.set(t,""!==e&&e,i)}},ot.each(["width","height"],function(t,e){ot.attrHooks[e]={set:function(t,i){if(""===i)return t.setAttribute(e,"auto"),i}}})),it.style||(ot.attrHooks.style={get:function(t){return t.style.cssText||void 0},set:function(t,e){return t.style.cssText=e+""}});var Se=/^(?:input|select|textarea|button|object)$/i,Ie=/^(?:a|area)$/i;ot.fn.extend({prop:function(t,e){return Pt(this,ot.prop,t,e,arguments.length>1)},removeProp:function(t){return t=ot.propFix[t]||t,this.each(function(){try{this[t]=void 0,delete this[t]}catch(e){}})}}),ot.extend({propFix:{"for":"htmlFor","class":"className"},prop:function(t,e,i){var n,o,s,r=t.nodeType;if(t&&3!==r&&8!==r&&2!==r)return s=1!==r||!ot.isXMLDoc(t),s&&(e=ot.propFix[e]||e,o=ot.propHooks[e]),void 0!==i?o&&"set"in o&&void 0!==(n=o.set(t,i,e))?n:t[e]=i:o&&"get"in o&&null!==(n=o.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=ot.find.attr(t,"tabindex");return e?parseInt(e,10):Se.test(t.nodeName)||Ie.test(t.nodeName)&&t.href?0:-1}}}}),it.hrefNormalized||ot.each(["href","src"],function(t,e){ot.propHooks[e]={get:function(t){return t.getAttribute(e,4)}}}),it.optSelected||(ot.propHooks.selected={get:function(t){var e=t.parentNode;return e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex),null}}),ot.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){ot.propFix[this.toLowerCase()]=this}),it.enctype||(ot.propFix.enctype="encoding");var Pe=/[\t\r\n\f]/g;ot.fn.extend({addClass:function(t){var e,i,n,o,s,r,a=0,l=this.length,c="string"==typeof t&&t;if(ot.isFunction(t))return this.each(function(e){ot(this).addClass(t.call(this,e,this.className))});if(c)for(e=(t||"").match(bt)||[];a<l;a++)if(i=this[a],n=1===i.nodeType&&(i.className?(" "+i.className+" ").replace(Pe," "):" ")){for(s=0;o=e[s++];)n.indexOf(" "+o+" ")<0&&(n+=o+" ");r=ot.trim(n),i.className!==r&&(i.className=r)}return this},removeClass:function(t){var e,i,n,o,s,r,a=0,l=this.length,c=0===arguments.length||"string"==typeof t&&t;if(ot.isFunction(t))return this.each(function(e){ot(this).removeClass(t.call(this,e,this.className))});if(c)for(e=(t||"").match(bt)||[];a<l;a++)if(i=this[a],n=1===i.nodeType&&(i.className?(" "+i.className+" ").replace(Pe," "):"")){for(s=0;o=e[s++];)for(;n.indexOf(" "+o+" ")>=0;)n=n.replace(" "+o+" "," ");r=t?ot.trim(n):"",i.className!==r&&(i.className=r)}return this},toggleClass:function(t,e){var i=typeof t;return"boolean"==typeof e&&"string"===i?e?this.addClass(t):this.removeClass(t):ot.isFunction(t)?this.each(function(i){ot(this).toggleClass(t.call(this,i,this.className,e),e)}):this.each(function(){if("string"===i)for(var e,n=0,o=ot(this),s=t.match(bt)||[];e=s[n++];)o.hasClass(e)?o.removeClass(e):o.addClass(e);else i!==xt&&"boolean"!==i||(this.className&&ot._data(this,"__className__",this.className),this.className=this.className||t===!1?"":ot._data(this,"__className__")||"")})},hasClass:function(t){for(var e=" "+t+" ",i=0,n=this.length;i<n;i++)if(1===this[i].nodeType&&(" "+this[i].className+" ").replace(Pe," ").indexOf(e)>=0)return!0;return!1}}),ot.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(t,e){ot.fn[e]=function(t,i){return arguments.length>0?this.on(e,null,t,i):this.trigger(e)}}),ot.fn.extend({hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)},bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)}});var je=ot.now(),Ne=/\?/,Ae=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;ot.parseJSON=function(e){if(t.JSON&&t.JSON.parse)return t.JSON.parse(e+"");var i,n=null,o=ot.trim(e+"");return o&&!ot.trim(o.replace(Ae,function(t,e,o,s){return i&&e&&(n=0),0===n?t:(i=o||e,n+=!s-!o,"")}))?Function("return "+o)():ot.error("Invalid JSON: "+e)},ot.parseXML=function(e){var i,n;if(!e||"string"!=typeof e)return null;try{t.DOMParser?(n=new DOMParser,i=n.parseFromString(e,"text/xml")):(i=new ActiveXObject("Microsoft.XMLDOM"),i.async="false",i.loadXML(e))}catch(o){i=void 0}return i&&i.documentElement&&!i.getElementsByTagName("parsererror").length||ot.error("Invalid XML: "+e),i};var $e,Ee,Me=/#.*$/,He=/([?&])_=[^&]*/,Le=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,ze=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Oe=/^(?:GET|HEAD)$/,qe=/^\/\//,Re=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,We={},Fe={},Ye="*/".concat("*");try{Ee=location.href}catch(Be){Ee=ft.createElement("a"),Ee.href="",Ee=Ee.href}$e=Re.exec(Ee.toLowerCase())||[],ot.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ee,type:"GET",isLocal:ze.test($e[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ye,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":ot.parseJSON,"text xml":ot.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?R(R(t,ot.ajaxSettings),e):R(ot.ajaxSettings,t)},ajaxPrefilter:O(We),ajaxTransport:O(Fe),ajax:function(t,e){function i(t,e,i,n){var o,u,v,y,_,k=e;2!==b&&(b=2,a&&clearTimeout(a),c=void 0,r=n||"",w.readyState=t>0?4:0,o=t>=200&&t<300||304===t,i&&(y=W(d,w,i)),y=F(d,y,w,o),o?(d.ifModified&&(_=w.getResponseHeader("Last-Modified"),_&&(ot.lastModified[s]=_),_=w.getResponseHeader("etag"),_&&(ot.etag[s]=_)),204===t||"HEAD"===d.type?k="nocontent":304===t?k="notmodified":(k=y.state,u=y.data,v=y.error,o=!v)):(v=k,!t&&k||(k="error",t<0&&(t=0))),w.status=t,w.statusText=(e||k)+"",o?f.resolveWith(h,[u,k,w]):f.rejectWith(h,[w,k,v]),w.statusCode(m),m=void 0,l&&p.trigger(o?"ajaxSuccess":"ajaxError",[w,d,o?u:v]),g.fireWith(h,[w,k]),l&&(p.trigger("ajaxComplete",[w,d]),--ot.active||ot.event.trigger("ajaxStop")))}"object"==typeof t&&(e=t,t=void 0),e=e||{};var n,o,s,r,a,l,c,u,d=ot.ajaxSetup({},e),h=d.context||d,p=d.context&&(h.nodeType||h.jquery)?ot(h):ot.event,f=ot.Deferred(),g=ot.Callbacks("once memory"),m=d.statusCode||{},v={},y={},b=0,_="canceled",w={readyState:0,getResponseHeader:function(t){var e;if(2===b){if(!u)for(u={};e=Le.exec(r);)u[e[1].toLowerCase()]=e[2];e=u[t.toLowerCase()]}return null==e?null:e},getAllResponseHeaders:function(){return 2===b?r:null},setRequestHeader:function(t,e){var i=t.toLowerCase();return b||(t=y[i]=y[i]||t,v[t]=e),this},overrideMimeType:function(t){return b||(d.mimeType=t),this},statusCode:function(t){var e;if(t)if(b<2)for(e in t)m[e]=[m[e],t[e]];else w.always(t[w.status]);return this},abort:function(t){var e=t||_;return c&&c.abort(e),i(0,e),this}};if(f.promise(w).complete=g.add,w.success=w.done,w.error=w.fail,d.url=((t||d.url||Ee)+"").replace(Me,"").replace(qe,$e[1]+"//"),d.type=e.method||e.type||d.method||d.type,d.dataTypes=ot.trim(d.dataType||"*").toLowerCase().match(bt)||[""],null==d.crossDomain&&(n=Re.exec(d.url.toLowerCase()),d.crossDomain=!(!n||n[1]===$e[1]&&n[2]===$e[2]&&(n[3]||("http:"===n[1]?"80":"443"))===($e[3]||("http:"===$e[1]?"80":"443")))),d.data&&d.processData&&"string"!=typeof d.data&&(d.data=ot.param(d.data,d.traditional)),q(We,d,e,w),2===b)return w;l=ot.event&&d.global,l&&0===ot.active++&&ot.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!Oe.test(d.type),s=d.url,d.hasContent||(d.data&&(s=d.url+=(Ne.test(s)?"&":"?")+d.data,delete d.data),d.cache===!1&&(d.url=He.test(s)?s.replace(He,"$1_="+je++):s+(Ne.test(s)?"&":"?")+"_="+je++)),d.ifModified&&(ot.lastModified[s]&&w.setRequestHeader("If-Modified-Since",ot.lastModified[s]),ot.etag[s]&&w.setRequestHeader("If-None-Match",ot.etag[s])),(d.data&&d.hasContent&&d.contentType!==!1||e.contentType)&&w.setRequestHeader("Content-Type",d.contentType),w.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+Ye+"; q=0.01":""):d.accepts["*"]);for(o in d.headers)w.setRequestHeader(o,d.headers[o]);if(d.beforeSend&&(d.beforeSend.call(h,w,d)===!1||2===b))return w.abort();_="abort";for(o in{success:1,error:1,complete:1})w[o](d[o]);if(c=q(Fe,d,e,w)){w.readyState=1,l&&p.trigger("ajaxSend",[w,d]),d.async&&d.timeout>0&&(a=setTimeout(function(){w.abort("timeout")},d.timeout));try{b=1,c.send(v,i)}catch(k){if(!(b<2))throw k;i(-1,k)}}else i(-1,"No Transport");return w},getJSON:function(t,e,i){return ot.get(t,e,i,"json")},getScript:function(t,e){return ot.get(t,void 0,e,"script")}}),ot.each(["get","post"],function(t,e){ot[e]=function(t,i,n,o){return ot.isFunction(i)&&(o=o||n,n=i,i=void 0),ot.ajax({url:t,type:e,dataType:o,data:i,success:n})}}),ot._evalUrl=function(t){return ot.ajax({url:t,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})},ot.fn.extend({wrapAll:function(t){if(ot.isFunction(t))return this.each(function(e){ot(this).wrapAll(t.call(this,e))});if(this[0]){var e=ot(t,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstChild&&1===t.firstChild.nodeType;)t=t.firstChild;return t}).append(this)}return this},wrapInner:function(t){return ot.isFunction(t)?this.each(function(e){ot(this).wrapInner(t.call(this,e))}):this.each(function(){var e=ot(this),i=e.contents();i.length?i.wrapAll(t):e.append(t)})},wrap:function(t){var e=ot.isFunction(t);return this.each(function(i){ot(this).wrapAll(e?t.call(this,i):t)})},unwrap:function(){return this.parent().each(function(){ot.nodeName(this,"body")||ot(this).replaceWith(this.childNodes)}).end()}}),ot.expr.filters.hidden=function(t){return t.offsetWidth<=0&&t.offsetHeight<=0||!it.reliableHiddenOffsets()&&"none"===(t.style&&t.style.display||ot.css(t,"display"))},ot.expr.filters.visible=function(t){return!ot.expr.filters.hidden(t)};var Xe=/%20/g,Ue=/\[\]$/,Ke=/\r?\n/g,Ve=/^(?:submit|button|image|reset|file)$/i,Qe=/^(?:input|select|textarea|keygen)/i;ot.param=function(t,e){var i,n=[],o=function(t,e){e=ot.isFunction(e)?e():null==e?"":e,n[n.length]=encodeURIComponent(t)+"="+encodeURIComponent(e)};if(void 0===e&&(e=ot.ajaxSettings&&ot.ajaxSettings.traditional),ot.isArray(t)||t.jquery&&!ot.isPlainObject(t))ot.each(t,function(){o(this.name,this.value)});else for(i in t)Y(i,t[i],e,o);return n.join("&").replace(Xe,"+")},ot.fn.extend({serialize:function(){return ot.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=ot.prop(this,"elements");return t?ot.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!ot(this).is(":disabled")&&Qe.test(this.nodeName)&&!Ve.test(t)&&(this.checked||!jt.test(t))}).map(function(t,e){var i=ot(this).val();return null==i?null:ot.isArray(i)?ot.map(i,function(t){return{name:e.name,value:t.replace(Ke,"\r\n")}}):{name:e.name,value:i.replace(Ke,"\r\n")}}).get()}}),ot.ajaxSettings.xhr=void 0!==t.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&B()||X()}:B;var Ge=0,Ze={},Je=ot.ajaxSettings.xhr();t.attachEvent&&t.attachEvent("onunload",function(){for(var t in Ze)Ze[t](void 0,!0)}),it.cors=!!Je&&"withCredentials"in Je,Je=it.ajax=!!Je,Je&&ot.ajaxTransport(function(t){if(!t.crossDomain||it.cors){var e;return{send:function(i,n){var o,s=t.xhr(),r=++Ge;if(s.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(o in t.xhrFields)s[o]=t.xhrFields[o];t.mimeType&&s.overrideMimeType&&s.overrideMimeType(t.mimeType),t.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest");for(o in i)void 0!==i[o]&&s.setRequestHeader(o,i[o]+"");s.send(t.hasContent&&t.data||null),e=function(i,o){var a,l,c;if(e&&(o||4===s.readyState))if(delete Ze[r],e=void 0,s.onreadystatechange=ot.noop,o)4!==s.readyState&&s.abort();else{c={},a=s.status,"string"==typeof s.responseText&&(c.text=s.responseText);try{l=s.statusText}catch(u){l=""}a||!t.isLocal||t.crossDomain?1223===a&&(a=204):a=c.text?200:404}c&&n(a,l,c,s.getAllResponseHeaders())},t.async?4===s.readyState?setTimeout(e):s.onreadystatechange=Ze[r]=e:e()},abort:function(){e&&e(void 0,!0)}}}}),ot.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(t){return ot.globalEval(t),t}}}),ot.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET",t.global=!1)}),ot.ajaxTransport("script",function(t){if(t.crossDomain){var e,i=ft.head||ot("head")[0]||ft.documentElement;return{send:function(n,o){e=ft.createElement("script"),e.async=!0,t.scriptCharset&&(e.charset=t.scriptCharset),e.src=t.url,e.onload=e.onreadystatechange=function(t,i){(i||!e.readyState||/loaded|complete/.test(e.readyState))&&(e.onload=e.onreadystatechange=null,e.parentNode&&e.parentNode.removeChild(e),e=null,i||o(200,"success"))},i.insertBefore(e,i.firstChild)},abort:function(){e&&e.onload(void 0,!0)}}}});var ti=[],ei=/(=)\?(?=&|$)|\?\?/;ot.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=ti.pop()||ot.expando+"_"+je++;return this[t]=!0,t}}),ot.ajaxPrefilter("json jsonp",function(e,i,n){var o,s,r,a=e.jsonp!==!1&&(ei.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&ei.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=ot.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(ei,"$1"+o):e.jsonp!==!1&&(e.url+=(Ne.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return r||ot.error(o+" was not called"),r[0]},e.dataTypes[0]="json",s=t[o],t[o]=function(){r=arguments},n.always(function(){t[o]=s,e[o]&&(e.jsonpCallback=i.jsonpCallback,ti.push(o)),r&&ot.isFunction(s)&&s(r[0]),r=s=void 0}),"script"}),ot.parseHTML=function(t,e,i){if(!t||"string"!=typeof t)return null;"boolean"==typeof e&&(i=e,e=!1),e=e||ft;var n=dt.exec(t),o=!i&&[];return n?[e.createElement(n[1])]:(n=ot.buildFragment([t],e,o),o&&o.length&&ot(o).remove(),ot.merge([],n.childNodes))};var ii=ot.fn.load;ot.fn.load=function(t,e,i){if("string"!=typeof t&&ii)return ii.apply(this,arguments);var n,o,s,r=this,a=t.indexOf(" ");return a>=0&&(n=ot.trim(t.slice(a,t.length)),t=t.slice(0,a)),ot.isFunction(e)?(i=e,e=void 0):e&&"object"==typeof e&&(s="POST"),r.length>0&&ot.ajax({url:t,type:s,dataType:"html",data:e}).done(function(t){o=arguments,r.html(n?ot("<div>").append(ot.parseHTML(t)).find(n):t)}).complete(i&&function(t,e){r.each(i,o||[t.responseText,e,t])}),this},ot.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){ot.fn[e]=function(t){return this.on(e,t)}}),ot.expr.filters.animated=function(t){return ot.grep(ot.timers,function(e){return t===e.elem}).length};var ni=t.document.documentElement;ot.offset={setOffset:function(t,e,i){var n,o,s,r,a,l,c,u=ot.css(t,"position"),d=ot(t),h={};"static"===u&&(t.style.position="relative"),a=d.offset(),s=ot.css(t,"top"),l=ot.css(t,"left"),c=("absolute"===u||"fixed"===u)&&ot.inArray("auto",[s,l])>-1,c?(n=d.position(),r=n.top,o=n.left):(r=parseFloat(s)||0,o=parseFloat(l)||0),ot.isFunction(e)&&(e=e.call(t,i,a)),null!=e.top&&(h.top=e.top-a.top+r),null!=e.left&&(h.left=e.left-a.left+o),"using"in e?e.using.call(t,h):d.css(h)}},ot.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){ot.offset.setOffset(this,t,e)});var e,i,n={top:0,left:0},o=this[0],s=o&&o.ownerDocument;if(s)return e=s.documentElement,ot.contains(e,o)?(typeof o.getBoundingClientRect!==xt&&(n=o.getBoundingClientRect()),i=U(s),{top:n.top+(i.pageYOffset||e.scrollTop)-(e.clientTop||0),left:n.left+(i.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}):n},position:function(){if(this[0]){var t,e,i={top:0,left:0},n=this[0];return"fixed"===ot.css(n,"position")?e=n.getBoundingClientRect():(t=this.offsetParent(),e=this.offset(),ot.nodeName(t[0],"html")||(i=t.offset()),i.top+=ot.css(t[0],"borderTopWidth",!0),i.left+=ot.css(t[0],"borderLeftWidth",!0)),{top:e.top-i.top-ot.css(n,"marginTop",!0),left:e.left-i.left-ot.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent||ni;t&&!ot.nodeName(t,"html")&&"static"===ot.css(t,"position");)t=t.offsetParent;return t||ni})}}),ot.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var i=/Y/.test(e);ot.fn[t]=function(n){return Pt(this,function(t,n,o){var s=U(t);return void 0===o?s?e in s?s[e]:s.document.documentElement[n]:t[n]:void(s?s.scrollTo(i?ot(s).scrollLeft():o,i?o:ot(s).scrollTop()):t[n]=o)},t,n,arguments.length,null)}}),ot.each(["top","left"],function(t,e){ot.cssHooks[e]=T(it.pixelPosition,function(t,i){if(i)return i=ee(t,e),ne.test(i)?ot(t).position()[e]+"px":i})}),ot.each({Height:"height",Width:"width"},function(t,e){ot.each({padding:"inner"+t,content:e,"":"outer"+t},function(i,n){ot.fn[n]=function(n,o){var s=arguments.length&&(i||"boolean"!=typeof n),r=i||(n===!0||o===!0?"margin":"border");return Pt(this,function(e,i,n){var o;return ot.isWindow(e)?e.document.documentElement["client"+t]:9===e.nodeType?(o=e.documentElement,Math.max(e.body["scroll"+t],o["scroll"+t],e.body["offset"+t],o["offset"+t],o["client"+t])):void 0===n?ot.css(e,i,r):ot.style(e,i,n,r)},e,s?n:void 0,s,null)}})}),ot.fn.size=function(){return this.length},ot.fn.andSelf=ot.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return ot});var oi=t.jQuery,si=t.$;return ot.noConflict=function(e){return t.$===ot&&(t.$=si),e&&t.jQuery===ot&&(t.jQuery=oi),ot},typeof e===xt&&(t.jQuery=t.$=ot),ot})},{}],70:[function(t,e,i){"use strict";e.exports=t("./src/js/adaptor/jquery")},{"./src/js/adaptor/jquery":71}],71:[function(t,e,i){"use strict";function n(t){t.fn.perfectScrollbar=function(e){return this.each(function(){if("object"==typeof e||"undefined"==typeof e){var i=e;s.get(this)||o.initialize(this,i)}else{var n=e;"update"===n?o.update(this):"destroy"===n&&o.destroy(this)}return t(this)})}}var o=t("../main"),s=t("../plugin/instances");if("function"==typeof define&&define.amd)define(["jquery"],n);else{var r=window.jQuery?window.jQuery:window.$;"undefined"!=typeof r&&n(r)}e.exports=n},{"../main":77,"../plugin/instances":88}],72:[function(t,e,i){"use strict";function n(t,e){var i=t.className.split(" ");i.indexOf(e)<0&&i.push(e),t.className=i.join(" ")}function o(t,e){var i=t.className.split(" "),n=i.indexOf(e);n>=0&&i.splice(n,1),t.className=i.join(" ")}i.add=function(t,e){t.classList?t.classList.add(e):n(t,e)},i.remove=function(t,e){t.classList?t.classList.remove(e):o(t,e)},i.list=function(t){return t.classList?t.classList:t.className.split(" ")}},{}],73:[function(t,e,i){"use strict";function n(t,e){return window.getComputedStyle(t)[e]}function o(t,e,i){return"number"==typeof i&&(i=i.toString()+"px"),t.style[e]=i,t}function s(t,e){for(var i in e){var n=e[i];"number"==typeof n&&(n=n.toString()+"px"),t.style[i]=n}return t}var r={};r.e=function(t,e){var i=document.createElement(t);return i.className=e,i},r.appendTo=function(t,e){return e.appendChild(t),t},r.css=function(t,e,i){return"object"==typeof e?s(t,e):"undefined"==typeof i?n(t,e):o(t,e,i)},r.matches=function(t,e){return"undefined"!=typeof t.matches?t.matches(e):"undefined"!=typeof t.matchesSelector?t.matchesSelector(e):"undefined"!=typeof t.webkitMatchesSelector?t.webkitMatchesSelector(e):"undefined"!=typeof t.mozMatchesSelector?t.mozMatchesSelector(e):"undefined"!=typeof t.msMatchesSelector?t.msMatchesSelector(e):void 0},r.remove=function(t){"undefined"!=typeof t.remove?t.remove():t.parentNode&&t.parentNode.removeChild(t)},r.queryChildren=function(t,e){return Array.prototype.filter.call(t.childNodes,function(t){return r.matches(t,e)})},e.exports=r},{}],74:[function(t,e,i){"use strict";var n=function(t){this.element=t,this.events={}};n.prototype.bind=function(t,e){"undefined"==typeof this.events[t]&&(this.events[t]=[]),this.events[t].push(e),this.element.addEventListener(t,e,!1)},n.prototype.unbind=function(t,e){var i="undefined"!=typeof e;this.events[t]=this.events[t].filter(function(n){return!(!i||n===e)||(this.element.removeEventListener(t,n,!1),!1)},this)},n.prototype.unbindAll=function(){for(var t in this.events)this.unbind(t)};var o=function(){this.eventElements=[]};o.prototype.eventElement=function(t){var e=this.eventElements.filter(function(e){return e.element===t})[0];return"undefined"==typeof e&&(e=new n(t),this.eventElements.push(e)),e},o.prototype.bind=function(t,e,i){this.eventElement(t).bind(e,i)},o.prototype.unbind=function(t,e,i){this.eventElement(t).unbind(e,i)},o.prototype.unbindAll=function(){for(var t=0;t<this.eventElements.length;t++)this.eventElements[t].unbindAll()},o.prototype.once=function(t,e,i){var n=this.eventElement(t),o=function(t){n.unbind(e,o),i(t)};n.bind(e,o)},e.exports=o},{}],75:[function(t,e,i){"use strict";e.exports=function(){function t(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return function(){return t()+t()+"-"+t()+"-"+t()+"-"+t()+"-"+t()+t()+t()}}()},{}],76:[function(t,e,i){"use strict";var n=t("./class"),o=t("./dom");i.toInt=function(t){return parseInt(t,10)||0},i.clone=function(t){if(null===t)return null;if("object"==typeof t){var e={};for(var i in t)e[i]=this.clone(t[i]);return e}return t},i.extend=function(t,e){var i=this.clone(t);for(var n in e)i[n]=this.clone(e[n]);return i},i.isEditable=function(t){return o.matches(t,"input,[contenteditable]")||o.matches(t,"select,[contenteditable]")||o.matches(t,"textarea,[contenteditable]")||o.matches(t,"button,[contenteditable]")},i.removePsClasses=function(t){for(var e=n.list(t),i=0;i<e.length;i++){var o=e[i];0===o.indexOf("ps-")&&n.remove(t,o)}},i.outerWidth=function(t){return this.toInt(o.css(t,"width"))+this.toInt(o.css(t,"paddingLeft"))+this.toInt(o.css(t,"paddingRight"))+this.toInt(o.css(t,"borderLeftWidth"))+this.toInt(o.css(t,"borderRightWidth"))},i.startScrolling=function(t,e){n.add(t,"ps-in-scrolling"),"undefined"!=typeof e?n.add(t,"ps-"+e):(n.add(t,"ps-x"),n.add(t,"ps-y"))},i.stopScrolling=function(t,e){n.remove(t,"ps-in-scrolling"),"undefined"!=typeof e?n.remove(t,"ps-"+e):(n.remove(t,"ps-x"),n.remove(t,"ps-y"))},i.env={isWebKit:"WebkitAppearance"in document.documentElement.style,supportsTouch:"ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch,supportsIePointer:null!==window.navigator.msMaxTouchPoints}},{"./class":72,"./dom":73}],77:[function(t,e,i){"use strict";var n=t("./plugin/destroy"),o=t("./plugin/initialize"),s=t("./plugin/update");e.exports={initialize:o,update:s,destroy:n}},{"./plugin/destroy":79,"./plugin/initialize":87,"./plugin/update":90}],78:[function(t,e,i){"use strict";e.exports={wheelSpeed:1,wheelPropagation:!1,swipePropagation:!0,minScrollbarLength:null,maxScrollbarLength:null,useBothWheelAxes:!1,useKeyboard:!0,suppressScrollX:!1,suppressScrollY:!1,scrollXMarginOffset:0,scrollYMarginOffset:0,stopPropagationOnClick:!0}},{}],79:[function(t,e,i){"use strict";var n=t("../lib/dom"),o=t("../lib/helper"),s=t("./instances");e.exports=function(t){var e=s.get(t);e&&(e.event.unbindAll(),n.remove(e.scrollbarX),n.remove(e.scrollbarY),n.remove(e.scrollbarXRail),n.remove(e.scrollbarYRail),o.removePsClasses(t),s.remove(t))}},{"../lib/dom":73,"../lib/helper":76,"./instances":88}],80:[function(t,e,i){"use strict";function n(t,e){function i(t){return t.getBoundingClientRect()}var n=window.Event.prototype.stopPropagation.bind;e.settings.stopPropagationOnClick&&e.event.bind(e.scrollbarY,"click",n),e.event.bind(e.scrollbarYRail,"click",function(n){var s=o.toInt(e.scrollbarYHeight/2),a=e.railYRatio*(n.pageY-window.scrollY-i(e.scrollbarYRail).top-s),l=e.railYRatio*(e.railYHeight-e.scrollbarYHeight),c=a/l;c<0?c=0:c>1&&(c=1),t.scrollTop=(e.contentHeight-e.containerHeight)*c,r(t),n.stopPropagation()}),e.settings.stopPropagationOnClick&&e.event.bind(e.scrollbarX,"click",n),e.event.bind(e.scrollbarXRail,"click",function(n){var s=o.toInt(e.scrollbarXWidth/2),a=e.railXRatio*(n.pageX-window.scrollX-i(e.scrollbarXRail).left-s),l=e.railXRatio*(e.railXWidth-e.scrollbarXWidth),c=a/l;c<0?c=0:c>1&&(c=1),t.scrollLeft=(e.contentWidth-e.containerWidth)*c-e.negativeScrollAdjustment,r(t),n.stopPropagation()})}var o=t("../../lib/helper"),s=t("../instances"),r=t("../update-geometry");e.exports=function(t){var e=s.get(t);n(t,e)}},{"../../lib/helper":76,"../instances":88,"../update-geometry":89}],81:[function(t,e,i){"use strict";function n(t,e){function i(i){var o=n+i*e.railXRatio,s=e.scrollbarXRail.getBoundingClientRect().left+e.railXRatio*(e.railXWidth-e.scrollbarXWidth);o<0?e.scrollbarXLeft=0:o>s?e.scrollbarXLeft=s:e.scrollbarXLeft=o;var a=r.toInt(e.scrollbarXLeft*(e.contentWidth-e.containerWidth)/(e.containerWidth-e.railXRatio*e.scrollbarXWidth))-e.negativeScrollAdjustment;t.scrollLeft=a}var n=null,o=null,a=function(e){i(e.pageX-o),l(t),e.stopPropagation(),e.preventDefault()},c=function(){r.stopScrolling(t,"x"),e.event.unbind(e.ownerDocument,"mousemove",a)};e.event.bind(e.scrollbarX,"mousedown",function(i){o=i.pageX,n=r.toInt(s.css(e.scrollbarX,"left"))*e.railXRatio,r.startScrolling(t,"x"),e.event.bind(e.ownerDocument,"mousemove",a),e.event.once(e.ownerDocument,"mouseup",c),i.stopPropagation(),i.preventDefault()})}function o(t,e){function i(i){var o=n+i*e.railYRatio,s=e.scrollbarYRail.getBoundingClientRect().top+e.railYRatio*(e.railYHeight-e.scrollbarYHeight);o<0?e.scrollbarYTop=0:o>s?e.scrollbarYTop=s:e.scrollbarYTop=o;var a=r.toInt(e.scrollbarYTop*(e.contentHeight-e.containerHeight)/(e.containerHeight-e.railYRatio*e.scrollbarYHeight));t.scrollTop=a}var n=null,o=null,a=function(e){i(e.pageY-o),l(t),e.stopPropagation(),e.preventDefault()},c=function(){r.stopScrolling(t,"y"),e.event.unbind(e.ownerDocument,"mousemove",a)};e.event.bind(e.scrollbarY,"mousedown",function(i){o=i.pageY,n=r.toInt(s.css(e.scrollbarY,"top"))*e.railYRatio,r.startScrolling(t,"y"),e.event.bind(e.ownerDocument,"mousemove",a),e.event.once(e.ownerDocument,"mouseup",c),i.stopPropagation(),i.preventDefault()})}var s=t("../../lib/dom"),r=t("../../lib/helper"),a=t("../instances"),l=t("../update-geometry");e.exports=function(t){var e=a.get(t);n(t,e),o(t,e)}},{"../../lib/dom":73,"../../lib/helper":76,"../instances":88,"../update-geometry":89}],82:[function(t,e,i){"use strict";function n(t,e){function i(i,n){var o=t.scrollTop;if(0===i){if(!e.scrollbarYActive)return!1;if(0===o&&n>0||o>=e.contentHeight-e.containerHeight&&n<0)return!e.settings.wheelPropagation}var s=t.scrollLeft;if(0===n){if(!e.scrollbarXActive)return!1;if(0===s&&i<0||s>=e.contentWidth-e.containerWidth&&i>0)return!e.settings.wheelPropagation}return!0}var n=!1;e.event.bind(t,"mouseenter",function(){n=!0}),e.event.bind(t,"mouseleave",function(){n=!1});var s=!1;e.event.bind(e.ownerDocument,"keydown",function(a){if((!a.isDefaultPrevented||!a.isDefaultPrevented())&&n){var l=document.activeElement?document.activeElement:e.ownerDocument.activeElement;if(l){for(;l.shadowRoot;)l=l.shadowRoot.activeElement;if(o.isEditable(l))return}var c=0,u=0;switch(a.which){case 37:c=-30;break;case 38:u=30;break;case 39:c=30;break;case 40:u=-30;break;case 33:u=90;break;case 32:u=a.shiftKey?90:-90;break;case 34:u=-90;break;case 35:u=a.ctrlKey?-e.contentHeight:-e.containerHeight;break;case 36:u=a.ctrlKey?t.scrollTop:e.containerHeight;break;default:return}t.scrollTop=t.scrollTop-u,t.scrollLeft=t.scrollLeft+c,r(t),s=i(c,u),s&&a.preventDefault()}})}var o=t("../../lib/helper"),s=t("../instances"),r=t("../update-geometry");e.exports=function(t){var e=s.get(t);n(t,e)}},{"../../lib/helper":76,"../instances":88,"../update-geometry":89}],83:[function(t,e,i){"use strict";function n(t,e){function i(i,n){var o=t.scrollTop;if(0===i){if(!e.scrollbarYActive)return!1;if(0===o&&n>0||o>=e.contentHeight-e.containerHeight&&n<0)return!e.settings.wheelPropagation}var s=t.scrollLeft;if(0===n){if(!e.scrollbarXActive)return!1;if(0===s&&i<0||s>=e.contentWidth-e.containerWidth&&i>0)return!e.settings.wheelPropagation}return!0}function n(t){var e=t.deltaX,i=-1*t.deltaY;return"undefined"!=typeof e&&"undefined"!=typeof i||(e=-1*t.wheelDeltaX/6,i=t.wheelDeltaY/6),t.deltaMode&&1===t.deltaMode&&(e*=10,i*=10),e!==e&&i!==i&&(e=0,i=t.wheelDelta),[e,i]}function s(e,i){var n=t.querySelector("textarea:hover");if(n){var o=n.scrollHeight-n.clientHeight;if(o>0&&!(0===n.scrollTop&&i>0||n.scrollTop===o&&i<0))return!0;var s=n.scrollLeft-n.clientWidth;if(s>0&&!(0===n.scrollLeft&&e<0||n.scrollLeft===s&&e>0))return!0}return!1}function a(a){if(o.env.isWebKit||!t.querySelector("select:focus")){var c=n(a),u=c[0],d=c[1];s(u,d)||(l=!1,e.settings.useBothWheelAxes?e.scrollbarYActive&&!e.scrollbarXActive?(d?t.scrollTop=t.scrollTop-d*e.settings.wheelSpeed:t.scrollTop=t.scrollTop+u*e.settings.wheelSpeed,l=!0):e.scrollbarXActive&&!e.scrollbarYActive&&(u?t.scrollLeft=t.scrollLeft+u*e.settings.wheelSpeed:t.scrollLeft=t.scrollLeft-d*e.settings.wheelSpeed,l=!0):(t.scrollTop=t.scrollTop-d*e.settings.wheelSpeed,t.scrollLeft=t.scrollLeft+u*e.settings.wheelSpeed),r(t),l=l||i(u,d),l&&(a.stopPropagation(),a.preventDefault()))}}var l=!1;"undefined"!=typeof window.onwheel?e.event.bind(t,"wheel",a):"undefined"!=typeof window.onmousewheel&&e.event.bind(t,"mousewheel",a)}var o=t("../../lib/helper"),s=t("../instances"),r=t("../update-geometry");e.exports=function(t){var e=s.get(t);n(t,e)}},{"../../lib/helper":76,"../instances":88,"../update-geometry":89}],84:[function(t,e,i){"use strict";function n(t,e){e.event.bind(t,"scroll",function(){s(t)})}var o=t("../instances"),s=t("../update-geometry");e.exports=function(t){var e=o.get(t);n(t,e)}},{"../instances":88,"../update-geometry":89}],85:[function(t,e,i){"use strict";function n(t,e){function i(){var t=window.getSelection?window.getSelection():document.getSelection?document.getSelection():"";return 0===t.toString().length?null:t.getRangeAt(0).commonAncestorContainer}function n(){l||(l=setInterval(function(){return s.get(t)?(t.scrollTop=t.scrollTop+c.top,t.scrollLeft=t.scrollLeft+c.left,void r(t)):void clearInterval(l)},50))}function a(){l&&(clearInterval(l),l=null),o.stopScrolling(t)}var l=null,c={top:0,left:0},u=!1;e.event.bind(e.ownerDocument,"selectionchange",function(){t.contains(i())?u=!0:(u=!1,a())}),e.event.bind(window,"mouseup",function(){
u&&(u=!1,a())}),e.event.bind(window,"mousemove",function(e){if(u){var i={x:e.pageX,y:e.pageY},s={left:t.offsetLeft,right:t.offsetLeft+t.offsetWidth,top:t.offsetTop,bottom:t.offsetTop+t.offsetHeight};i.x<s.left+3?(c.left=-5,o.startScrolling(t,"x")):i.x>s.right-3?(c.left=5,o.startScrolling(t,"x")):c.left=0,i.y<s.top+3?(s.top+3-i.y<5?c.top=-5:c.top=-20,o.startScrolling(t,"y")):i.y>s.bottom-3?(i.y-s.bottom+3<5?c.top=5:c.top=20,o.startScrolling(t,"y")):c.top=0,0===c.top&&0===c.left?a():n()}})}var o=t("../../lib/helper"),s=t("../instances"),r=t("../update-geometry");e.exports=function(t){var e=s.get(t);n(t,e)}},{"../../lib/helper":76,"../instances":88,"../update-geometry":89}],86:[function(t,e,i){"use strict";function n(t,e,i,n){function r(i,n){var o=t.scrollTop,s=t.scrollLeft,r=Math.abs(i),a=Math.abs(n);if(a>r){if(n<0&&o===e.contentHeight-e.containerHeight||n>0&&0===o)return!e.settings.swipePropagation}else if(r>a&&(i<0&&s===e.contentWidth-e.containerWidth||i>0&&0===s))return!e.settings.swipePropagation;return!0}function a(e,i){t.scrollTop=t.scrollTop-i,t.scrollLeft=t.scrollLeft-e,s(t)}function l(){b=!0}function c(){b=!1}function u(t){return t.targetTouches?t.targetTouches[0]:t}function d(t){return!(!t.targetTouches||1!==t.targetTouches.length)||!(!t.pointerType||"mouse"===t.pointerType||t.pointerType===t.MSPOINTER_TYPE_MOUSE)}function h(t){if(d(t)){_=!0;var e=u(t);g.pageX=e.pageX,g.pageY=e.pageY,m=(new Date).getTime(),null!==y&&clearInterval(y),t.stopPropagation()}}function p(t){if(!b&&_&&d(t)){var e=u(t),i={pageX:e.pageX,pageY:e.pageY},n=i.pageX-g.pageX,o=i.pageY-g.pageY;a(n,o),g=i;var s=(new Date).getTime(),l=s-m;l>0&&(v.x=n/l,v.y=o/l,m=s),r(n,o)&&(t.stopPropagation(),t.preventDefault())}}function f(){!b&&_&&(_=!1,clearInterval(y),y=setInterval(function(){return o.get(t)?Math.abs(v.x)<.01&&Math.abs(v.y)<.01?void clearInterval(y):(a(30*v.x,30*v.y),v.x*=.8,void(v.y*=.8)):void clearInterval(y)},10))}var g={},m=0,v={},y=null,b=!1,_=!1;i&&(e.event.bind(window,"touchstart",l),e.event.bind(window,"touchend",c),e.event.bind(t,"touchstart",h),e.event.bind(t,"touchmove",p),e.event.bind(t,"touchend",f)),n&&(window.PointerEvent?(e.event.bind(window,"pointerdown",l),e.event.bind(window,"pointerup",c),e.event.bind(t,"pointerdown",h),e.event.bind(t,"pointermove",p),e.event.bind(t,"pointerup",f)):window.MSPointerEvent&&(e.event.bind(window,"MSPointerDown",l),e.event.bind(window,"MSPointerUp",c),e.event.bind(t,"MSPointerDown",h),e.event.bind(t,"MSPointerMove",p),e.event.bind(t,"MSPointerUp",f)))}var o=t("../instances"),s=t("../update-geometry");e.exports=function(t,e,i){var s=o.get(t);n(t,s,e,i)}},{"../instances":88,"../update-geometry":89}],87:[function(t,e,i){"use strict";var n=t("../lib/class"),o=t("../lib/helper"),s=t("./instances"),r=t("./update-geometry"),a=t("./handler/click-rail"),l=t("./handler/drag-scrollbar"),c=t("./handler/keyboard"),u=t("./handler/mouse-wheel"),d=t("./handler/native-scroll"),h=t("./handler/selection"),p=t("./handler/touch");e.exports=function(t,e){e="object"==typeof e?e:{},n.add(t,"ps-container");var i=s.add(t);i.settings=o.extend(i.settings,e),a(t),l(t),u(t),d(t),h(t),(o.env.supportsTouch||o.env.supportsIePointer)&&p(t,o.env.supportsTouch,o.env.supportsIePointer),i.settings.useKeyboard&&c(t),r(t)}},{"../lib/class":72,"../lib/helper":76,"./handler/click-rail":80,"./handler/drag-scrollbar":81,"./handler/keyboard":82,"./handler/mouse-wheel":83,"./handler/native-scroll":84,"./handler/selection":85,"./handler/touch":86,"./instances":88,"./update-geometry":89}],88:[function(t,e,i){"use strict";function n(t){var e=this;e.settings=d.clone(l),e.containerWidth=null,e.containerHeight=null,e.contentWidth=null,e.contentHeight=null,e.isRtl="rtl"===a.css(t,"direction"),e.isNegativeScroll=function(){var e=t.scrollLeft,i=null;return t.scrollLeft=-1,i=t.scrollLeft<0,t.scrollLeft=e,i}(),e.negativeScrollAdjustment=e.isNegativeScroll?t.scrollWidth-t.clientWidth:0,e.event=new c,e.ownerDocument=t.ownerDocument||document,e.scrollbarXRail=a.appendTo(a.e("div","ps-scrollbar-x-rail"),t),e.scrollbarX=a.appendTo(a.e("div","ps-scrollbar-x"),e.scrollbarXRail),e.scrollbarXActive=null,e.scrollbarXWidth=null,e.scrollbarXLeft=null,e.scrollbarXBottom=d.toInt(a.css(e.scrollbarXRail,"bottom")),e.isScrollbarXUsingBottom=e.scrollbarXBottom===e.scrollbarXBottom,e.scrollbarXTop=e.isScrollbarXUsingBottom?null:d.toInt(a.css(e.scrollbarXRail,"top")),e.railBorderXWidth=d.toInt(a.css(e.scrollbarXRail,"borderLeftWidth"))+d.toInt(a.css(e.scrollbarXRail,"borderRightWidth")),a.css(e.scrollbarXRail,"display","block"),e.railXMarginWidth=d.toInt(a.css(e.scrollbarXRail,"marginLeft"))+d.toInt(a.css(e.scrollbarXRail,"marginRight")),a.css(e.scrollbarXRail,"display",""),e.railXWidth=null,e.railXRatio=null,e.scrollbarYRail=a.appendTo(a.e("div","ps-scrollbar-y-rail"),t),e.scrollbarY=a.appendTo(a.e("div","ps-scrollbar-y"),e.scrollbarYRail),e.scrollbarYActive=null,e.scrollbarYHeight=null,e.scrollbarYTop=null,e.scrollbarYRight=d.toInt(a.css(e.scrollbarYRail,"right")),e.isScrollbarYUsingRight=e.scrollbarYRight===e.scrollbarYRight,e.scrollbarYLeft=e.isScrollbarYUsingRight?null:d.toInt(a.css(e.scrollbarYRail,"left")),e.scrollbarYOuterWidth=e.isRtl?d.outerWidth(e.scrollbarY):null,e.railBorderYWidth=d.toInt(a.css(e.scrollbarYRail,"borderTopWidth"))+d.toInt(a.css(e.scrollbarYRail,"borderBottomWidth")),a.css(e.scrollbarYRail,"display","block"),e.railYMarginHeight=d.toInt(a.css(e.scrollbarYRail,"marginTop"))+d.toInt(a.css(e.scrollbarYRail,"marginBottom")),a.css(e.scrollbarYRail,"display",""),e.railYHeight=null,e.railYRatio=null}function o(t){return"undefined"==typeof t.dataset?t.getAttribute("data-ps-id"):t.dataset.psId}function s(t,e){"undefined"==typeof t.dataset?t.setAttribute("data-ps-id",e):t.dataset.psId=e}function r(t){"undefined"==typeof t.dataset?t.removeAttribute("data-ps-id"):delete t.dataset.psId}var a=t("../lib/dom"),l=t("./default-setting"),c=t("../lib/event-manager"),u=t("../lib/guid"),d=t("../lib/helper"),h={};i.add=function(t){var e=u();return s(t,e),h[e]=new n(t),h[e]},i.remove=function(t){delete h[o(t)],r(t)},i.get=function(t){return h[o(t)]}},{"../lib/dom":73,"../lib/event-manager":74,"../lib/guid":75,"../lib/helper":76,"./default-setting":78}],89:[function(t,e,i){"use strict";function n(t,e){return t.settings.minScrollbarLength&&(e=Math.max(e,t.settings.minScrollbarLength)),t.settings.maxScrollbarLength&&(e=Math.min(e,t.settings.maxScrollbarLength)),e}function o(t,e){var i={width:e.railXWidth};e.isRtl?i.left=e.negativeScrollAdjustment+t.scrollLeft+e.containerWidth-e.contentWidth:i.left=t.scrollLeft,e.isScrollbarXUsingBottom?i.bottom=e.scrollbarXBottom-t.scrollTop:i.top=e.scrollbarXTop+t.scrollTop,r.css(e.scrollbarXRail,i);var n={top:t.scrollTop,height:e.railYHeight};e.isScrollbarYUsingRight?e.isRtl?n.right=e.contentWidth-(e.negativeScrollAdjustment+t.scrollLeft)-e.scrollbarYRight-e.scrollbarYOuterWidth:n.right=e.scrollbarYRight-t.scrollLeft:e.isRtl?n.left=e.negativeScrollAdjustment+t.scrollLeft+2*e.containerWidth-e.contentWidth-e.scrollbarYLeft-e.scrollbarYOuterWidth:n.left=e.scrollbarYLeft+t.scrollLeft,r.css(e.scrollbarYRail,n),r.css(e.scrollbarX,{left:e.scrollbarXLeft,width:e.scrollbarXWidth-e.railBorderXWidth}),r.css(e.scrollbarY,{top:e.scrollbarYTop,height:e.scrollbarYHeight-e.railBorderYWidth})}var s=t("../lib/class"),r=t("../lib/dom"),a=t("../lib/helper"),l=t("./instances");e.exports=function(t){var e=l.get(t);e.containerWidth=t.clientWidth,e.containerHeight=t.clientHeight,e.contentWidth=t.scrollWidth,e.contentHeight=t.scrollHeight;var i;t.contains(e.scrollbarXRail)||(i=r.queryChildren(t,".ps-scrollbar-x-rail"),i.length>0&&i.forEach(function(t){r.remove(t)}),r.appendTo(e.scrollbarXRail,t)),t.contains(e.scrollbarYRail)||(i=r.queryChildren(t,".ps-scrollbar-y-rail"),i.length>0&&i.forEach(function(t){r.remove(t)}),r.appendTo(e.scrollbarYRail,t)),!e.settings.suppressScrollX&&e.containerWidth+e.settings.scrollXMarginOffset<e.contentWidth?(e.scrollbarXActive=!0,e.railXWidth=e.containerWidth-e.railXMarginWidth,e.railXRatio=e.containerWidth/e.railXWidth,e.scrollbarXWidth=n(e,a.toInt(e.railXWidth*e.containerWidth/e.contentWidth)),e.scrollbarXLeft=a.toInt((e.negativeScrollAdjustment+t.scrollLeft)*(e.railXWidth-e.scrollbarXWidth)/(e.contentWidth-e.containerWidth))):(e.scrollbarXActive=!1,e.scrollbarXWidth=0,e.scrollbarXLeft=0,t.scrollLeft=0),!e.settings.suppressScrollY&&e.containerHeight+e.settings.scrollYMarginOffset<e.contentHeight?(e.scrollbarYActive=!0,e.railYHeight=e.containerHeight-e.railYMarginHeight,e.railYRatio=e.containerHeight/e.railYHeight,e.scrollbarYHeight=n(e,a.toInt(e.railYHeight*e.containerHeight/e.contentHeight)),e.scrollbarYTop=a.toInt(t.scrollTop*(e.railYHeight-e.scrollbarYHeight)/(e.contentHeight-e.containerHeight))):(e.scrollbarYActive=!1,e.scrollbarYHeight=0,e.scrollbarYTop=0,t.scrollTop=0),e.scrollbarXLeft>=e.railXWidth-e.scrollbarXWidth&&(e.scrollbarXLeft=e.railXWidth-e.scrollbarXWidth),e.scrollbarYTop>=e.railYHeight-e.scrollbarYHeight&&(e.scrollbarYTop=e.railYHeight-e.scrollbarYHeight),o(t,e),s[e.scrollbarXActive?"add":"remove"](t,"ps-active-x"),s[e.scrollbarYActive?"add":"remove"](t,"ps-active-y")}},{"../lib/class":72,"../lib/dom":73,"../lib/helper":76,"./instances":88}],90:[function(t,e,i){"use strict";var n=t("../lib/dom"),o=t("../lib/helper"),s=t("./instances"),r=t("./update-geometry");e.exports=function(t){var e=s.get(t);e&&(e.negativeScrollAdjustment=e.isNegativeScroll?t.scrollWidth-t.clientWidth:0,n.css(e.scrollbarXRail,"display","block"),n.css(e.scrollbarYRail,"display","block"),e.railXMarginWidth=o.toInt(n.css(e.scrollbarXRail,"marginLeft"))+o.toInt(n.css(e.scrollbarXRail,"marginRight")),e.railYMarginHeight=o.toInt(n.css(e.scrollbarYRail,"marginTop"))+o.toInt(n.css(e.scrollbarYRail,"marginBottom")),n.css(e.scrollbarXRail,"display","none"),n.css(e.scrollbarYRail,"display","none"),r(t),n.css(e.scrollbarXRail,"display",""),n.css(e.scrollbarYRail,"display",""))}},{"../lib/dom":73,"../lib/helper":76,"./instances":88,"./update-geometry":89}],91:[function(t,e,i){!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):e("object"==typeof i?t("jquery"):jQuery)}(function(t){var e=function(){if(t&&t.fn&&t.fn.select2&&t.fn.select2.amd)var e=t.fn.select2.amd;var e;return function(){if(!e||!e.requirejs){e?i=e:e={};var t,i,n;!function(e){function o(t,e){return _.call(t,e)}function s(t,e){var i,n,o,s,r,a,l,c,u,d,h,p=e&&e.split("/"),f=y.map,g=f&&f["*"]||{};if(t&&"."===t.charAt(0))if(e){for(p=p.slice(0,p.length-1),t=t.split("/"),r=t.length-1,y.nodeIdCompat&&k.test(t[r])&&(t[r]=t[r].replace(k,"")),t=p.concat(t),u=0;u<t.length;u+=1)if(h=t[u],"."===h)t.splice(u,1),u-=1;else if(".."===h){if(1===u&&(".."===t[2]||".."===t[0]))break;u>0&&(t.splice(u-1,2),u-=2)}t=t.join("/")}else 0===t.indexOf("./")&&(t=t.substring(2));if((p||g)&&f){for(i=t.split("/"),u=i.length;u>0;u-=1){if(n=i.slice(0,u).join("/"),p)for(d=p.length;d>0;d-=1)if(o=f[p.slice(0,d).join("/")],o&&(o=o[n])){s=o,a=u;break}if(s)break;!l&&g&&g[n]&&(l=g[n],c=u)}!s&&l&&(s=l,a=c),s&&(i.splice(0,a,s),t=i.join("/"))}return t}function r(t,i){return function(){return p.apply(e,w.call(arguments,0).concat([t,i]))}}function a(t){return function(e){return s(e,t)}}function l(t){return function(e){m[t]=e}}function c(t){if(o(v,t)){var i=v[t];delete v[t],b[t]=!0,h.apply(e,i)}if(!o(m,t)&&!o(b,t))throw new Error("No "+t);return m[t]}function u(t){var e,i=t?t.indexOf("!"):-1;return i>-1&&(e=t.substring(0,i),t=t.substring(i+1,t.length)),[e,t]}function d(t){return function(){return y&&y.config&&y.config[t]||{}}}var h,p,f,g,m={},v={},y={},b={},_=Object.prototype.hasOwnProperty,w=[].slice,k=/\.js$/;f=function(t,e){var i,n=u(t),o=n[0];return t=n[1],o&&(o=s(o,e),i=c(o)),o?t=i&&i.normalize?i.normalize(t,a(e)):s(t,e):(t=s(t,e),n=u(t),o=n[0],t=n[1],o&&(i=c(o))),{f:o?o+"!"+t:t,n:t,pr:o,p:i}},g={require:function(t){return r(t)},exports:function(t){var e=m[t];return"undefined"!=typeof e?e:m[t]={}},module:function(t){return{id:t,uri:"",exports:m[t],config:d(t)}}},h=function(t,i,n,s){var a,u,d,h,p,y,_=[],w=typeof n;if(s=s||t,"undefined"===w||"function"===w){for(i=!i.length&&n.length?["require","exports","module"]:i,p=0;p<i.length;p+=1)if(h=f(i[p],s),u=h.f,"require"===u)_[p]=g.require(t);else if("exports"===u)_[p]=g.exports(t),y=!0;else if("module"===u)a=_[p]=g.module(t);else if(o(m,u)||o(v,u)||o(b,u))_[p]=c(u);else{if(!h.p)throw new Error(t+" missing "+u);h.p.load(h.n,r(s,!0),l(u),{}),_[p]=m[u]}d=n?n.apply(m[t],_):void 0,t&&(a&&a.exports!==e&&a.exports!==m[t]?m[t]=a.exports:d===e&&y||(m[t]=d))}else t&&(m[t]=n)},t=i=p=function(t,i,n,o,s){if("string"==typeof t)return g[t]?g[t](i):c(f(t,i).f);if(!t.splice){if(y=t,y.deps&&p(y.deps,y.callback),!i)return;i.splice?(t=i,i=n,n=null):t=e}return i=i||function(){},"function"==typeof n&&(n=o,o=s),o?h(e,t,i,n):setTimeout(function(){h(e,t,i,n)},4),p},p.config=function(t){return p(t)},t._defined=m,n=function(t,e,i){e.splice||(i=e,e=[]),o(m,t)||o(v,t)||(v[t]=[t,e,i])},n.amd={jQuery:!0}}(),e.requirejs=t,e.require=i,e.define=n}}(),e.define("almond",function(){}),e.define("jquery",[],function(){var e=t||$;return null==e&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),e}),e.define("select2/utils",["jquery"],function(t){function e(t){var e=t.prototype,i=[];for(var n in e){var o=e[n];"function"==typeof o&&"constructor"!==n&&i.push(n)}return i}var i={};i.Extend=function(t,e){function i(){this.constructor=t}var n={}.hasOwnProperty;for(var o in e)n.call(e,o)&&(t[o]=e[o]);return i.prototype=e.prototype,t.prototype=new i,t.__super__=e.prototype,t},i.Decorate=function(t,i){function n(){var e=Array.prototype.unshift,n=i.prototype.constructor.length,o=t.prototype.constructor;n>0&&(e.call(arguments,t.prototype.constructor),o=i.prototype.constructor),o.apply(this,arguments)}function o(){this.constructor=n}var s=e(i),r=e(t);i.displayName=t.displayName,n.prototype=new o;for(var a=0;a<r.length;a++){var l=r[a];n.prototype[l]=t.prototype[l]}for(var c=(function(t){var e=function(){};t in n.prototype&&(e=n.prototype[t]);var o=i.prototype[t];return function(){var t=Array.prototype.unshift;return t.call(arguments,e),o.apply(this,arguments)}}),u=0;u<s.length;u++){var d=s[u];n.prototype[d]=c(d)}return n};var n=function(){this.listeners={}};return n.prototype.on=function(t,e){this.listeners=this.listeners||{},t in this.listeners?this.listeners[t].push(e):this.listeners[t]=[e]},n.prototype.trigger=function(t){var e=Array.prototype.slice;this.listeners=this.listeners||{},t in this.listeners&&this.invoke(this.listeners[t],e.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},n.prototype.invoke=function(t,e){for(var i=0,n=t.length;i<n;i++)t[i].apply(this,e)},i.Observable=n,i.generateChars=function(t){for(var e="",i=0;i<t;i++){var n=Math.floor(36*Math.random());e+=n.toString(36)}return e},i.bind=function(t,e){return function(){t.apply(e,arguments)}},i._convertData=function(t){for(var e in t){var i=e.split("-"),n=t;if(1!==i.length){for(var o=0;o<i.length;o++){var s=i[o];s=s.substring(0,1).toLowerCase()+s.substring(1),s in n||(n[s]={}),o==i.length-1&&(n[s]=t[e]),n=n[s]}delete t[e]}}return t},i.hasScroll=function(e,i){var n=t(i),o=i.style.overflowX,s=i.style.overflowY;return(o!==s||"hidden"!==s&&"visible"!==s)&&("scroll"===o||"scroll"===s||(n.innerHeight()<i.scrollHeight||n.innerWidth()<i.scrollWidth))},i.escapeMarkup=function(t){var e={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof t?t:String(t).replace(/[&<>"'\/\\]/g,function(t){return e[t]})},i.appendMany=function(e,i){if("1.7"===t.fn.jquery.substr(0,3)){var n=t();t.map(i,function(t){n=n.add(t)}),i=n}e.append(i)},i}),e.define("select2/results",["jquery","./utils"],function(t,e){function i(t,e,n){this.$element=t,this.data=n,this.options=e,i.__super__.constructor.call(this)}return e.Extend(i,e.Observable),i.prototype.render=function(){var e=t('<ul class="select2-results__options" role="tree"></ul>');return this.options.get("multiple")&&e.attr("aria-multiselectable","true"),this.$results=e,e},i.prototype.clear=function(){this.$results.empty()},i.prototype.displayMessage=function(e){var i=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var n=t('<li role="treeitem" class="select2-results__option"></li>'),o=this.options.get("translations").get(e.message);n.append(i(o(e.args))),this.$results.append(n)},i.prototype.append=function(t){this.hideLoading();var e=[];if(null==t.results||0===t.results.length)return void(0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"}));t.results=this.sort(t.results);for(var i=0;i<t.results.length;i++){var n=t.results[i],o=this.option(n);e.push(o)}this.$results.append(e)},i.prototype.position=function(t,e){var i=e.find(".select2-results");i.append(t)},i.prototype.sort=function(t){var e=this.options.get("sorter");return e(t)},i.prototype.setClasses=function(){var e=this;this.data.current(function(i){var n=t.map(i,function(t){return t.id.toString()}),o=e.$results.find(".select2-results__option[aria-selected]");o.each(function(){var e=t(this),i=t.data(this,"data"),o=""+i.id;null!=i.element&&i.element.selected||null==i.element&&t.inArray(o,n)>-1?e.attr("aria-selected","true"):e.attr("aria-selected","false")});var s=o.filter("[aria-selected=true]");s.length>0?s.first().trigger("mouseenter"):o.first().trigger("mouseenter")})},i.prototype.showLoading=function(t){this.hideLoading();var e=this.options.get("translations").get("searching"),i={disabled:!0,loading:!0,text:e(t)},n=this.option(i);n.className+=" loading-results",this.$results.prepend(n)},i.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},i.prototype.option=function(e){var i=document.createElement("li");i.className="select2-results__option";var n={role:"treeitem","aria-selected":"false"};e.disabled&&(delete n["aria-selected"],n["aria-disabled"]="true"),null==e.id&&delete n["aria-selected"],null!=e._resultId&&(i.id=e._resultId),e.title&&(i.title=e.title),e.children&&(n.role="group",n["aria-label"]=e.text,delete n["aria-selected"]);for(var o in n){var s=n[o];i.setAttribute(o,s)}if(e.children){var r=t(i),a=document.createElement("strong");a.className="select2-results__group";t(a);this.template(e,a);for(var l=[],c=0;c<e.children.length;c++){var u=e.children[c],d=this.option(u);l.push(d)}var h=t("<ul></ul>",{"class":"select2-results__options select2-results__options--nested"});h.append(l),r.append(a),r.append(h)}else this.template(e,i);return t.data(i,"data",e),i},i.prototype.bind=function(e,i){var n=this,o=e.id+"-results";this.$results.attr("id",o),e.on("results:all",function(t){n.clear(),n.append(t.data),e.isOpen()&&n.setClasses()}),e.on("results:append",function(t){n.append(t.data),e.isOpen()&&n.setClasses()}),e.on("query",function(t){n.showLoading(t)}),e.on("select",function(){e.isOpen()&&n.setClasses()}),e.on("unselect",function(){e.isOpen()&&n.setClasses()}),e.on("open",function(){n.$results.attr("aria-expanded","true"),n.$results.attr("aria-hidden","false"),n.setClasses(),n.ensureHighlightVisible()}),e.on("close",function(){n.$results.attr("aria-expanded","false"),n.$results.attr("aria-hidden","true"),n.$results.removeAttr("aria-activedescendant")}),e.on("results:toggle",function(){var t=n.getHighlightedResults();0!==t.length&&t.trigger("mouseup")}),e.on("results:select",function(){var t=n.getHighlightedResults();if(0!==t.length){var e=t.data("data");"true"==t.attr("aria-selected")?n.trigger("close"):n.trigger("select",{data:e})}}),e.on("results:previous",function(){var t=n.getHighlightedResults(),e=n.$results.find("[aria-selected]"),i=e.index(t);if(0!==i){var o=i-1;0===t.length&&(o=0);var s=e.eq(o);s.trigger("mouseenter");var r=n.$results.offset().top,a=s.offset().top,l=n.$results.scrollTop()+(a-r);0===o?n.$results.scrollTop(0):a-r<0&&n.$results.scrollTop(l)}}),e.on("results:next",function(){var t=n.getHighlightedResults(),e=n.$results.find("[aria-selected]"),i=e.index(t),o=i+1;if(!(o>=e.length)){var s=e.eq(o);s.trigger("mouseenter");var r=n.$results.offset().top+n.$results.outerHeight(!1),a=s.offset().top+s.outerHeight(!1),l=n.$results.scrollTop()+a-r;0===o?n.$results.scrollTop(0):a>r&&n.$results.scrollTop(l)}}),e.on("results:focus",function(t){t.element.addClass("select2-results__option--highlighted")}),e.on("results:message",function(t){n.displayMessage(t)}),t.fn.mousewheel&&this.$results.on("mousewheel",function(t){var e=n.$results.scrollTop(),i=n.$results.get(0).scrollHeight-n.$results.scrollTop()+t.deltaY,o=t.deltaY>0&&e-t.deltaY<=0,s=t.deltaY<0&&i<=n.$results.height();o?(n.$results.scrollTop(0),t.preventDefault(),t.stopPropagation()):s&&(n.$results.scrollTop(n.$results.get(0).scrollHeight-n.$results.height()),t.preventDefault(),t.stopPropagation())}),this.$results.on("mouseup",".select2-results__option[aria-selected]",function(e){var i=t(this),o=i.data("data");return"true"===i.attr("aria-selected")?void(n.options.get("multiple")?n.trigger("unselect",{originalEvent:e,data:o}):n.trigger("close")):void n.trigger("select",{originalEvent:e,data:o})}),this.$results.on("mouseenter",".select2-results__option[aria-selected]",function(e){var i=t(this).data("data");n.getHighlightedResults().removeClass("select2-results__option--highlighted"),n.trigger("results:focus",{data:i,element:t(this)})})},i.prototype.getHighlightedResults=function(){var t=this.$results.find(".select2-results__option--highlighted");return t},i.prototype.destroy=function(){this.$results.remove()},i.prototype.ensureHighlightVisible=function(){var t=this.getHighlightedResults();if(0!==t.length){var e=this.$results.find("[aria-selected]"),i=e.index(t),n=this.$results.offset().top,o=t.offset().top,s=this.$results.scrollTop()+(o-n),r=o-n;s-=2*t.outerHeight(!1),i<=2?this.$results.scrollTop(0):(r>this.$results.outerHeight()||r<0)&&this.$results.scrollTop(s)}},i.prototype.template=function(e,i){var n=this.options.get("templateResult"),o=this.options.get("escapeMarkup"),s=n(e);null==s?i.style.display="none":"string"==typeof s?i.innerHTML=o(s):t(i).append(s)},i}),e.define("select2/keys",[],function(){var t={BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46};return t}),e.define("select2/selection/base",["jquery","../utils","../keys"],function(t,e,i){function n(t,e){this.$element=t,this.options=e,n.__super__.constructor.call(this)}return e.Extend(n,e.Observable),n.prototype.render=function(){var e=t('<span class="select2-selection" role="combobox" aria-autocomplete="list" aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=this.$element.data("old-tabindex")?this._tabindex=this.$element.data("old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),e.attr("title",this.$element.attr("title")),e.attr("tabindex",this._tabindex),this.$selection=e,e},n.prototype.bind=function(t,e){var n=this,o=(t.id+"-container",t.id+"-results");this.container=t,this.$selection.on("focus",function(t){n.trigger("focus",t)}),this.$selection.on("blur",function(t){n.trigger("blur",t)}),this.$selection.on("keydown",function(t){n.trigger("keypress",t),t.which===i.SPACE&&t.preventDefault()}),t.on("results:focus",function(t){n.$selection.attr("aria-activedescendant",t.data._resultId)}),t.on("selection:update",function(t){n.update(t.data)}),t.on("open",function(){n.$selection.attr("aria-expanded","true"),n.$selection.attr("aria-owns",o),n._attachCloseHandler(t)}),t.on("close",function(){n.$selection.attr("aria-expanded","false"),n.$selection.removeAttr("aria-activedescendant"),n.$selection.removeAttr("aria-owns"),n.$selection.focus(),n._detachCloseHandler(t)}),t.on("enable",function(){n.$selection.attr("tabindex",n._tabindex)}),t.on("disable",function(){n.$selection.attr("tabindex","-1")})},n.prototype._attachCloseHandler=function(e){t(document.body).on("mousedown.select2."+e.id,function(e){var i=t(e.target),n=i.closest(".select2"),o=t(".select2.select2-container--open");o.each(function(){var e=t(this);if(this!=n[0]){var i=e.data("element");i.select2("close")}})})},n.prototype._detachCloseHandler=function(e){t(document.body).off("mousedown.select2."+e.id)},n.prototype.position=function(t,e){var i=e.find(".selection");i.append(t)},n.prototype.destroy=function(){this._detachCloseHandler(this.container)},n.prototype.update=function(t){throw new Error("The `update` method must be defined in child classes.")},n}),e.define("select2/selection/single",["jquery","./base","../utils","../keys"],function(t,e,i,n){function o(){o.__super__.constructor.apply(this,arguments)}return i.Extend(o,e),o.prototype.render=function(){var t=o.__super__.render.call(this);return t.addClass("select2-selection--single"),t.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>'),t},o.prototype.bind=function(t,e){var i=this;o.__super__.bind.apply(this,arguments);var n=t.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",n),this.$selection.attr("aria-labelledby",n),this.$selection.on("mousedown",function(t){1===t.which&&i.trigger("toggle",{originalEvent:t})}),this.$selection.on("focus",function(t){}),this.$selection.on("blur",function(t){}),t.on("selection:update",function(t){i.update(t.data)})},o.prototype.clear=function(){this.$selection.find(".select2-selection__rendered").empty()},o.prototype.display=function(t){var e=this.options.get("templateSelection"),i=this.options.get("escapeMarkup");return i(e(t))},o.prototype.selectionContainer=function(){return t("<span></span>")},o.prototype.update=function(t){if(0===t.length)return void this.clear();var e=t[0],i=this.display(e),n=this.$selection.find(".select2-selection__rendered");n.empty().append(i),n.prop("title",e.title||e.text)},o}),e.define("select2/selection/multiple",["jquery","./base","../utils"],function(t,e,i){function n(t,e){n.__super__.constructor.apply(this,arguments)}return i.Extend(n,e),n.prototype.render=function(){var t=n.__super__.render.call(this);return t.addClass("select2-selection--multiple"),t.html('<ul class="select2-selection__rendered"></ul>'),t},n.prototype.bind=function(e,i){var o=this;n.__super__.bind.apply(this,arguments),this.$selection.on("click",function(t){o.trigger("toggle",{originalEvent:t})}),this.$selection.on("click",".select2-selection__choice__remove",function(e){var i=t(this),n=i.parent(),s=n.data("data");o.trigger("unselect",{originalEvent:e,data:s})})},n.prototype.clear=function(){this.$selection.find(".select2-selection__rendered").empty()},n.prototype.display=function(t){var e=this.options.get("templateSelection"),i=this.options.get("escapeMarkup");return i(e(t))},n.prototype.selectionContainer=function(){var e=t('<li class="select2-selection__choice"><span class="select2-selection__choice__remove" role="presentation">&times;</span></li>');return e},n.prototype.update=function(t){if(this.clear(),0!==t.length){for(var e=[],n=0;n<t.length;n++){var o=t[n],s=this.display(o),r=this.selectionContainer();r.append(s),r.prop("title",o.title||o.text),r.data("data",o),e.push(r)}var a=this.$selection.find(".select2-selection__rendered");i.appendMany(a,e)}},n}),e.define("select2/selection/placeholder",["../utils"],function(t){function e(t,e,i){this.placeholder=this.normalizePlaceholder(i.get("placeholder")),t.call(this,e,i)}return e.prototype.normalizePlaceholder=function(t,e){return"string"==typeof e&&(e={id:"",text:e}),e},e.prototype.createPlaceholder=function(t,e){var i=this.selectionContainer();return i.html(this.display(e)),i.addClass("select2-selection__placeholder").removeClass("select2-selection__choice"),i},e.prototype.update=function(t,e){var i=1==e.length&&e[0].id!=this.placeholder.id,n=e.length>1;if(n||i)return t.call(this,e);this.clear();var o=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(o)},e}),e.define("select2/selection/allowClear",["jquery","../keys"],function(t,e){function i(){}return i.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option."),this.$selection.on("mousedown",".select2-selection__clear",function(t){n._handleClear(t)}),e.on("keypress",function(t){n._handleKeyboardClear(t,e)})},i.prototype._handleClear=function(t,e){if(!this.options.get("disabled")){var i=this.$selection.find(".select2-selection__clear");if(0!==i.length){e.stopPropagation();for(var n=i.data("data"),o=0;o<n.length;o++){var s={data:n[o]};if(this.trigger("unselect",s),s.prevented)return}this.$element.val(this.placeholder.id).trigger("change"),this.trigger("toggle")}}},i.prototype._handleKeyboardClear=function(t,i,n){n.isOpen()||i.which!=e.DELETE&&i.which!=e.BACKSPACE||this._handleClear(i)},i.prototype.update=function(e,i){if(e.call(this,i),!(this.$selection.find(".select2-selection__placeholder").length>0||0===i.length)){var n=t('<span class="select2-selection__clear">&times;</span>');n.data("data",i),this.$selection.find(".select2-selection__rendered").prepend(n)}},i}),e.define("select2/selection/search",["jquery","../utils","../keys"],function(t,e,i){function n(t,e,i){t.call(this,e,i)}return n.prototype.render=function(e){var i=t('<li class="select2-search select2-search--inline"><input class="select2-search__field" type="search" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" role="textbox" /></li>');this.$searchContainer=i,this.$search=i.find("input");var n=e.call(this);return n},n.prototype.bind=function(t,e,n){var o=this;t.call(this,e,n),e.on("open",function(){o.$search.attr("tabindex",0),o.$search.focus()}),e.on("close",function(){o.$search.attr("tabindex",-1),o.$search.val(""),o.$search.focus()}),e.on("enable",function(){o.$search.prop("disabled",!1)}),e.on("disable",function(){o.$search.prop("disabled",!0)}),this.$selection.on("focusin",".select2-search--inline",function(t){o.trigger("focus",t)}),this.$selection.on("focusout",".select2-search--inline",function(t){o.trigger("blur",t)}),this.$selection.on("keydown",".select2-search--inline",function(t){t.stopPropagation(),o.trigger("keypress",t),o._keyUpPrevented=t.isDefaultPrevented();var e=t.which;if(e===i.BACKSPACE&&""===o.$search.val()){var n=o.$searchContainer.prev(".select2-selection__choice");if(n.length>0){var s=n.data("data");o.searchRemoveChoice(s),t.preventDefault()}}}),this.$selection.on("input",".select2-search--inline",function(t){o.$selection.off("keyup.search")}),this.$selection.on("keyup.search input",".select2-search--inline",function(t){o.handleSearch(t)})},n.prototype.createPlaceholder=function(t,e){this.$search.attr("placeholder",e.text)},n.prototype.update=function(t,e){this.$search.attr("placeholder",""),t.call(this,e),this.$selection.find(".select2-selection__rendered").append(this.$searchContainer),this.resizeSearch()},n.prototype.handleSearch=function(){if(this.resizeSearch(),!this._keyUpPrevented){var t=this.$search.val();this.trigger("query",{term:t})}this._keyUpPrevented=!1},n.prototype.searchRemoveChoice=function(t,e){this.trigger("unselect",{data:e}),this.trigger("open"),this.$search.val(e.text+" ")},n.prototype.resizeSearch=function(){this.$search.css("width","25px");var t="";if(""!==this.$search.attr("placeholder"))t=this.$selection.find(".select2-selection__rendered").innerWidth();else{var e=this.$search.val().length+1;t=.75*e+"em"}this.$search.css("width",t)},n}),e.define("select2/selection/eventRelay",["jquery"],function(t){function e(){}return e.prototype.bind=function(e,i,n){var o=this,s=["open","opening","close","closing","select","selecting","unselect","unselecting"],r=["opening","closing","selecting","unselecting"];
e.call(this,i,n),i.on("*",function(e,i){if(t.inArray(e,s)!==-1){i=i||{};var n=t.Event("select2:"+e,{params:i});o.$element.trigger(n),t.inArray(e,r)!==-1&&(i.prevented=n.isDefaultPrevented())}})},e}),e.define("select2/translation",["jquery","require"],function(t,e){function i(t){this.dict=t||{}}return i.prototype.all=function(){return this.dict},i.prototype.get=function(t){return this.dict[t]},i.prototype.extend=function(e){this.dict=t.extend({},e.all(),this.dict)},i._cache={},i.loadPath=function(t){if(!(t in i._cache)){var n=e(t);i._cache[t]=n}return new i(i._cache[t])},i}),e.define("select2/diacritics",[],function(){var t={"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ω":"ω","ς":"σ"};return t}),e.define("select2/data/base",["../utils"],function(t){function e(t,i){e.__super__.constructor.call(this)}return t.Extend(e,t.Observable),e.prototype.current=function(t){throw new Error("The `current` method must be defined in child classes.")},e.prototype.query=function(t,e){throw new Error("The `query` method must be defined in child classes.")},e.prototype.bind=function(t,e){},e.prototype.destroy=function(){},e.prototype.generateResultId=function(e,i){var n=e.id+"-result-";return n+=t.generateChars(4),n+=null!=i.id?"-"+i.id.toString():"-"+t.generateChars(4)},e}),e.define("select2/data/select",["./base","../utils","jquery"],function(t,e,i){function n(t,e){this.$element=t,this.options=e,n.__super__.constructor.call(this)}return e.Extend(n,t),n.prototype.current=function(t){var e=[],n=this;this.$element.find(":selected").each(function(){var t=i(this),o=n.item(t);e.push(o)}),t(e)},n.prototype.select=function(t){var e=this;if(t.selected=!0,i(t.element).is("option"))return t.element.selected=!0,void this.$element.trigger("change");if(this.$element.prop("multiple"))this.current(function(n){var o=[];t=[t],t.push.apply(t,n);for(var s=0;s<t.length;s++){var r=t[s].id;i.inArray(r,o)===-1&&o.push(r)}e.$element.val(o),e.$element.trigger("change")});else{var n=t.id;this.$element.val(n),this.$element.trigger("change")}},n.prototype.unselect=function(t){var e=this;if(this.$element.prop("multiple"))return t.selected=!1,i(t.element).is("option")?(t.element.selected=!1,void this.$element.trigger("change")):void this.current(function(n){for(var o=[],s=0;s<n.length;s++){var r=n[s].id;r!==t.id&&i.inArray(r,o)===-1&&o.push(r)}e.$element.val(o),e.$element.trigger("change")})},n.prototype.bind=function(t,e){var i=this;this.container=t,t.on("select",function(t){i.select(t.data)}),t.on("unselect",function(t){i.unselect(t.data)})},n.prototype.destroy=function(){this.$element.find("*").each(function(){i.removeData(this,"data")})},n.prototype.query=function(t,e){var n=[],o=this,s=this.$element.children();s.each(function(){var e=i(this);if(e.is("option")||e.is("optgroup")){var s=o.item(e),r=o.matches(t,s);null!==r&&n.push(r)}}),e({results:n})},n.prototype.addOptions=function(t){e.appendMany(this.$element,t)},n.prototype.option=function(t){var e;t.children?(e=document.createElement("optgroup"),e.label=t.text):(e=document.createElement("option"),void 0!==e.textContent?e.textContent=t.text:e.innerText=t.text),t.id&&(e.value=t.id),t.disabled&&(e.disabled=!0),t.selected&&(e.selected=!0),t.title&&(e.title=t.title);var n=i(e),o=this._normalizeItem(t);return o.element=e,i.data(e,"data",o),n},n.prototype.item=function(t){var e={};if(e=i.data(t[0],"data"),null!=e)return e;if(t.is("option"))e={id:t.val(),text:t.text(),disabled:t.prop("disabled"),selected:t.prop("selected"),title:t.prop("title")};else if(t.is("optgroup")){e={text:t.prop("label"),children:[],title:t.prop("title")};for(var n=t.children("option"),o=[],s=0;s<n.length;s++){var r=i(n[s]),a=this.item(r);o.push(a)}e.children=o}return e=this._normalizeItem(e),e.element=t[0],i.data(t[0],"data",e),e},n.prototype._normalizeItem=function(t){i.isPlainObject(t)||(t={id:t,text:t}),t=i.extend({},{text:""},t);var e={selected:!1,disabled:!1};return null!=t.id&&(t.id=t.id.toString()),null!=t.text&&(t.text=t.text.toString()),null==t._resultId&&t.id&&null!=this.container&&(t._resultId=this.generateResultId(this.container,t)),i.extend({},e,t)},n.prototype.matches=function(t,e){var i=this.options.get("matcher");return i(t,e)},n}),e.define("select2/data/array",["./select","../utils","jquery"],function(t,e,i){function n(t,e){var i=e.get("data")||[];n.__super__.constructor.call(this,t,e),this.addOptions(this.convertToOptions(i))}return e.Extend(n,t),n.prototype.select=function(t){var e=this.$element.find("option").filter(function(e,i){return i.value==t.id.toString()});0===e.length&&(e=this.option(t),this.addOptions(e)),n.__super__.select.call(this,t)},n.prototype.convertToOptions=function(t){function n(t){return function(){return i(this).val()==t.id}}for(var o=this,s=this.$element.find("option"),r=s.map(function(){return o.item(i(this)).id}).get(),a=[],l=0;l<t.length;l++){var c=this._normalizeItem(t[l]);if(i.inArray(c.id,r)>=0){var u=s.filter(n(c)),d=this.item(u),h=(i.extend(!0,{},d,c),this.option(d));u.replaceWith(h)}else{var p=this.option(c);if(c.children){var f=this.convertToOptions(c.children);e.appendMany(p,f)}a.push(p)}}return a},n}),e.define("select2/data/ajax",["./array","../utils","jquery"],function(t,e,i){function n(e,i){this.ajaxOptions=this._applyDefaults(i.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),t.__super__.constructor.call(this,e,i)}return e.Extend(n,t),n.prototype._applyDefaults=function(t){var e={data:function(t){return{q:t.term}},transport:function(t,e,n){var o=i.ajax(t);return o.then(e),o.fail(n),o}};return i.extend({},e,t,!0)},n.prototype.processResults=function(t){return t},n.prototype.query=function(t,e){function n(){var n=s.transport(s,function(n){var s=o.processResults(n,t);o.options.get("debug")&&window.console&&console.error&&(s&&s.results&&i.isArray(s.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response.")),e(s)},function(){});o._request=n}var o=this;null!=this._request&&(i.isFunction(this._request.abort)&&this._request.abort(),this._request=null);var s=i.extend({type:"GET"},this.ajaxOptions);"function"==typeof s.url&&(s.url=s.url(t)),"function"==typeof s.data&&(s.data=s.data(t)),this.ajaxOptions.delay&&""!==t.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(n,this.ajaxOptions.delay)):n()},n}),e.define("select2/data/tags",["jquery"],function(t){function e(e,i,n){var o=n.get("tags"),s=n.get("createTag");if(void 0!==s&&(this.createTag=s),e.call(this,i,n),t.isArray(o))for(var r=0;r<o.length;r++){var a=o[r],l=this._normalizeItem(a),c=this.option(l);this.$element.append(c)}}return e.prototype.query=function(t,e,i){function n(t,s){for(var r=t.results,a=0;a<r.length;a++){var l=r[a],c=null!=l.children&&!n({results:l.children},!0),u=l.text===e.term;if(u||c)return!s&&(t.data=r,void i(t))}if(s)return!0;var d=o.createTag(e);if(null!=d){var h=o.option(d);h.attr("data-select2-tag",!0),o.addOptions([h]),o.insertTag(r,d)}t.results=r,i(t)}var o=this;return this._removeOldTags(),null==e.term||null!=e.page?void t.call(this,e,i):void t.call(this,e,n)},e.prototype.createTag=function(e,i){var n=t.trim(i.term);return""===n?null:{id:n,text:n}},e.prototype.insertTag=function(t,e,i){e.unshift(i)},e.prototype._removeOldTags=function(e){var i=(this._lastTag,this.$element.find("option[data-select2-tag]"));i.each(function(){this.selected||t(this).remove()})},e}),e.define("select2/data/tokenizer",["jquery"],function(t){function e(t,e,i){var n=i.get("tokenizer");void 0!==n&&(this.tokenizer=n),t.call(this,e,i)}return e.prototype.bind=function(t,e,i){t.call(this,e,i),this.$search=e.dropdown.$search||e.selection.$search||i.find(".select2-search__field")},e.prototype.query=function(t,e,i){function n(t){o.select(t)}var o=this;e.term=e.term||"";var s=this.tokenizer(e,this.options,n);s.term!==e.term&&(this.$search.length&&(this.$search.val(s.term),this.$search.focus()),e.term=s.term),t.call(this,e,i)},e.prototype.tokenizer=function(e,i,n,o){for(var s=n.get("tokenSeparators")||[],r=i.term,a=0,l=this.createTag||function(t){return{id:t.term,text:t.term}};a<r.length;){var c=r[a];if(t.inArray(c,s)!==-1){var u=r.substr(0,a),d=t.extend({},i,{term:u}),h=l(d);o(h),r=r.substr(a+1)||"",a=0}else a++}return{term:r}},e}),e.define("select2/data/minimumInputLength",[],function(){function t(t,e,i){this.minimumInputLength=i.get("minimumInputLength"),t.call(this,e,i)}return t.prototype.query=function(t,e,i){return e.term=e.term||"",e.term.length<this.minimumInputLength?void this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:e.term,params:e}}):void t.call(this,e,i)},t}),e.define("select2/data/maximumInputLength",[],function(){function t(t,e,i){this.maximumInputLength=i.get("maximumInputLength"),t.call(this,e,i)}return t.prototype.query=function(t,e,i){return e.term=e.term||"",this.maximumInputLength>0&&e.term.length>this.maximumInputLength?void this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:e.term,params:e}}):void t.call(this,e,i)},t}),e.define("select2/data/maximumSelectionLength",[],function(){function t(t,e,i){this.maximumSelectionLength=i.get("maximumSelectionLength"),t.call(this,e,i)}return t.prototype.query=function(t,e,i){var n=this;this.current(function(o){var s=null!=o?o.length:0;return n.maximumSelectionLength>0&&s>=n.maximumSelectionLength?void n.trigger("results:message",{message:"maximumSelected",args:{maximum:n.maximumSelectionLength}}):void t.call(n,e,i)})},t}),e.define("select2/dropdown",["jquery","./utils"],function(t,e){function i(t,e){this.$element=t,this.options=e,i.__super__.constructor.call(this)}return e.Extend(i,e.Observable),i.prototype.render=function(){var e=t('<span class="select2-dropdown"><span class="select2-results"></span></span>');return e.attr("dir",this.options.get("dir")),this.$dropdown=e,e},i.prototype.position=function(t,e){},i.prototype.destroy=function(){this.$dropdown.remove()},i}),e.define("select2/dropdown/search",["jquery","../utils"],function(t,e){function i(){}return i.prototype.render=function(e){var i=e.call(this),n=t('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" role="textbox" /></span>');return this.$searchContainer=n,this.$search=n.find("input"),i.prepend(n),i},i.prototype.bind=function(e,i,n){var o=this;e.call(this,i,n),this.$search.on("keydown",function(t){o.trigger("keypress",t),o._keyUpPrevented=t.isDefaultPrevented()}),this.$search.on("input",function(e){t(this).off("keyup")}),this.$search.on("keyup input",function(t){o.handleSearch(t)}),i.on("open",function(){o.$search.attr("tabindex",0),o.$search.focus(),window.setTimeout(function(){o.$search.focus()},0)}),i.on("close",function(){o.$search.attr("tabindex",-1),o.$search.val("")}),i.on("results:all",function(t){if(null==t.query.term||""===t.query.term){var e=o.showSearch(t);e?o.$searchContainer.removeClass("select2-search--hide"):o.$searchContainer.addClass("select2-search--hide")}})},i.prototype.handleSearch=function(t){if(!this._keyUpPrevented){var e=this.$search.val();this.trigger("query",{term:e})}this._keyUpPrevented=!1},i.prototype.showSearch=function(t,e){return!0},i}),e.define("select2/dropdown/hidePlaceholder",[],function(){function t(t,e,i,n){this.placeholder=this.normalizePlaceholder(i.get("placeholder")),t.call(this,e,i,n)}return t.prototype.append=function(t,e){e.results=this.removePlaceholder(e.results),t.call(this,e)},t.prototype.normalizePlaceholder=function(t,e){return"string"==typeof e&&(e={id:"",text:e}),e},t.prototype.removePlaceholder=function(t,e){for(var i=e.slice(0),n=e.length-1;n>=0;n--){var o=e[n];this.placeholder.id===o.id&&i.splice(n,1)}return i},t}),e.define("select2/dropdown/infiniteScroll",["jquery"],function(t){function e(t,e,i,n){this.lastParams={},t.call(this,e,i,n),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return e.prototype.append=function(t,e){this.$loadingMore.remove(),this.loading=!1,t.call(this,e),this.showLoadingMore(e)&&this.$results.append(this.$loadingMore)},e.prototype.bind=function(e,i,n){var o=this;e.call(this,i,n),i.on("query",function(t){o.lastParams=t,o.loading=!0}),i.on("query:append",function(t){o.lastParams=t,o.loading=!0}),this.$results.on("scroll",function(){var e=t.contains(document.documentElement,o.$loadingMore[0]);if(!o.loading&&e){var i=o.$results.offset().top+o.$results.outerHeight(!1),n=o.$loadingMore.offset().top+o.$loadingMore.outerHeight(!1);i+50>=n&&o.loadMore()}})},e.prototype.loadMore=function(){this.loading=!0;var e=t.extend({},{page:1},this.lastParams);e.page++,this.trigger("query:append",e)},e.prototype.showLoadingMore=function(t,e){return e.pagination&&e.pagination.more},e.prototype.createLoadingMore=function(){var e=t('<li class="option load-more" role="treeitem"></li>'),i=this.options.get("translations").get("loadingMore");return e.html(i(this.lastParams)),e},e}),e.define("select2/dropdown/attachBody",["jquery","../utils"],function(t,e){function i(t,e,i){this.$dropdownParent=i.get("dropdownParent")||document.body,t.call(this,e,i)}return i.prototype.bind=function(t,e,i){var n=this,o=!1;t.call(this,e,i),e.on("open",function(){n._showDropdown(),n._attachPositioningHandler(e),o||(o=!0,e.on("results:all",function(){n._positionDropdown(),n._resizeDropdown()}),e.on("results:append",function(){n._positionDropdown(),n._resizeDropdown()}))}),e.on("close",function(){n._hideDropdown(),n._detachPositioningHandler(e)}),this.$dropdownContainer.on("mousedown",function(t){t.stopPropagation()})},i.prototype.position=function(t,e,i){e.attr("class",i.attr("class")),e.removeClass("select2"),e.addClass("select2-container--open"),e.css({position:"absolute",top:-999999}),this.$container=i},i.prototype.render=function(e){var i=t("<span></span>"),n=e.call(this);return i.append(n),this.$dropdownContainer=i,i},i.prototype._hideDropdown=function(t){this.$dropdownContainer.detach()},i.prototype._attachPositioningHandler=function(i){var n=this,o="scroll.select2."+i.id,s="resize.select2."+i.id,r="orientationchange.select2."+i.id,a=this.$container.parents().filter(e.hasScroll);a.each(function(){t(this).data("select2-scroll-position",{x:t(this).scrollLeft(),y:t(this).scrollTop()})}),a.on(o,function(e){var i=t(this).data("select2-scroll-position");t(this).scrollTop(i.y)}),t(window).on(o+" "+s+" "+r,function(t){n._positionDropdown(),n._resizeDropdown()})},i.prototype._detachPositioningHandler=function(i){var n="scroll.select2."+i.id,o="resize.select2."+i.id,s="orientationchange.select2."+i.id,r=this.$container.parents().filter(e.hasScroll);r.off(n),t(window).off(n+" "+o+" "+s)},i.prototype._positionDropdown=function(){var e=t(window),i=this.$dropdown.hasClass("select2-dropdown--above"),n=this.$dropdown.hasClass("select2-dropdown--below"),o=null,s=(this.$container.position(),this.$container.offset());s.bottom=s.top+this.$container.outerHeight(!1);var r={height:this.$container.outerHeight(!1)};r.top=s.top,r.bottom=s.top+r.height;var a={height:this.$dropdown.outerHeight(!1)},l={top:e.scrollTop(),bottom:e.scrollTop()+e.height()},c=l.top<s.top-a.height,u=l.bottom>s.bottom+a.height,d={left:s.left,top:r.bottom};i||n||(o="below"),u||!c||i?!c&&u&&i&&(o="below"):o="above",("above"==o||i&&"below"!==o)&&(d.top=r.top-a.height),null!=o&&(this.$dropdown.removeClass("select2-dropdown--below select2-dropdown--above").addClass("select2-dropdown--"+o),this.$container.removeClass("select2-container--below select2-container--above").addClass("select2-container--"+o)),this.$dropdownContainer.css(d)},i.prototype._resizeDropdown=function(){this.$dropdownContainer.width();var t={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(t.minWidth=t.width,t.width="auto"),this.$dropdown.css(t)},i.prototype._showDropdown=function(t){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},i}),e.define("select2/dropdown/minimumResultsForSearch",[],function(){function t(e){for(var i=0,n=0;n<e.length;n++){var o=e[n];o.children?i+=t(o.children):i++}return i}function e(t,e,i,n){this.minimumResultsForSearch=i.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),t.call(this,e,i,n)}return e.prototype.showSearch=function(e,i){return!(t(i.data.results)<this.minimumResultsForSearch)&&e.call(this,i)},e}),e.define("select2/dropdown/selectOnClose",[],function(){function t(){}return t.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),e.on("close",function(){n._handleSelectOnClose()})},t.prototype._handleSelectOnClose=function(){var t=this.getHighlightedResults();t.length<1||this.trigger("select",{data:t.data("data")})},t}),e.define("select2/dropdown/closeOnSelect",[],function(){function t(){}return t.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),e.on("select",function(t){n._selectTriggered(t)}),e.on("unselect",function(t){n._selectTriggered(t)})},t.prototype._selectTriggered=function(t,e){var i=e.originalEvent;i&&i.ctrlKey||this.trigger("close")},t}),e.define("select2/i18n/en",[],function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(t){var e=t.input.length-t.maximum,i="Please delete "+e+" character";return 1!=e&&(i+="s"),i},inputTooShort:function(t){var e=t.minimum-t.input.length,i="Please enter "+e+" or more characters";return i},loadingMore:function(){return"Loading more results…"},maximumSelected:function(t){var e="You can only select "+t.maximum+" item";return 1!=t.maximum&&(e+="s"),e},noResults:function(){return"No results found"},searching:function(){return"Searching…"}}}),e.define("select2/defaults",["jquery","require","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./i18n/en"],function(t,e,i,n,o,s,r,a,l,c,u,d,h,p,f,g,m,v,y,b,_,w,k,x,C,D,T,S,I){function P(){this.reset()}P.prototype.apply=function(d){if(d=t.extend({},this.defaults,d),null==d.dataAdapter){if(null!=d.ajax?d.dataAdapter=f:null!=d.data?d.dataAdapter=p:d.dataAdapter=h,d.minimumInputLength>0&&(d.dataAdapter=c.Decorate(d.dataAdapter,v)),d.maximumInputLength>0&&(d.dataAdapter=c.Decorate(d.dataAdapter,y)),d.maximumSelectionLength>0&&(d.dataAdapter=c.Decorate(d.dataAdapter,b)),d.tags&&(d.dataAdapter=c.Decorate(d.dataAdapter,g)),null==d.tokenSeparators&&null==d.tokenizer||(d.dataAdapter=c.Decorate(d.dataAdapter,m)),null!=d.query){var I=e(d.amdBase+"compat/query");d.dataAdapter=c.Decorate(d.dataAdapter,I)}if(null!=d.initSelection){var P=e(d.amdBase+"compat/initSelection");d.dataAdapter=c.Decorate(d.dataAdapter,P)}}if(null==d.resultsAdapter&&(d.resultsAdapter=i,null!=d.ajax&&(d.resultsAdapter=c.Decorate(d.resultsAdapter,x)),null!=d.placeholder&&(d.resultsAdapter=c.Decorate(d.resultsAdapter,k)),d.selectOnClose&&(d.resultsAdapter=c.Decorate(d.resultsAdapter,T))),null==d.dropdownAdapter){if(d.multiple)d.dropdownAdapter=_;else{var j=c.Decorate(_,w);d.dropdownAdapter=j}if(0!==d.minimumResultsForSearch&&(d.dropdownAdapter=c.Decorate(d.dropdownAdapter,D)),d.closeOnSelect&&(d.dropdownAdapter=c.Decorate(d.dropdownAdapter,S)),null!=d.dropdownCssClass||null!=d.dropdownCss||null!=d.adaptDropdownCssClass){var N=e(d.amdBase+"compat/dropdownCss");d.dropdownAdapter=c.Decorate(d.dropdownAdapter,N)}d.dropdownAdapter=c.Decorate(d.dropdownAdapter,C)}if(null==d.selectionAdapter){if(d.multiple?d.selectionAdapter=o:d.selectionAdapter=n,null!=d.placeholder&&(d.selectionAdapter=c.Decorate(d.selectionAdapter,s)),d.allowClear&&(d.selectionAdapter=c.Decorate(d.selectionAdapter,r)),d.multiple&&(d.selectionAdapter=c.Decorate(d.selectionAdapter,a)),null!=d.containerCssClass||null!=d.containerCss||null!=d.adaptContainerCssClass){var A=e(d.amdBase+"compat/containerCss");d.selectionAdapter=c.Decorate(d.selectionAdapter,A)}d.selectionAdapter=c.Decorate(d.selectionAdapter,l)}if("string"==typeof d.language)if(d.language.indexOf("-")>0){var $=d.language.split("-"),E=$[0];d.language=[d.language,E]}else d.language=[d.language];if(t.isArray(d.language)){var M=new u;d.language.push("en");for(var H=d.language,L=0;L<H.length;L++){var z=H[L],O={};try{O=u.loadPath(z)}catch(q){try{z=this.defaults.amdLanguageBase+z,O=u.loadPath(z)}catch(R){d.debug&&window.console&&console.warn&&console.warn('Select2: The language file for "'+z+'" could not be automatically loaded. A fallback will be used instead.');continue}}M.extend(O)}d.translations=M}else{var W=u.loadPath(this.defaults.amdLanguageBase+"en"),F=new u(d.language);F.extend(W),d.translations=F}return d},P.prototype.reset=function(){function e(t){function e(t){return d[t]||t}return t.replace(/[^\u0000-\u007E]/g,e)}function i(n,o){if(""===t.trim(n.term))return o;if(o.children&&o.children.length>0){for(var s=t.extend(!0,{},o),r=o.children.length-1;r>=0;r--){var a=o.children[r],l=i(n,a);null==l&&s.children.splice(r,1)}return s.children.length>0?s:i(n,s)}var c=e(o.text).toUpperCase(),u=e(n.term).toUpperCase();return c.indexOf(u)>-1?o:null}this.defaults={amdBase:"./",amdLanguageBase:"./i18n/",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:c.escapeMarkup,language:I,matcher:i,minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,sorter:function(t){return t},templateResult:function(t){return t.text},templateSelection:function(t){return t.text},theme:"default",width:"resolve"}},P.prototype.set=function(e,i){var n=t.camelCase(e),o={};o[n]=i;var s=c._convertData(o);t.extend(this.defaults,s)};var j=new P;return j}),e.define("select2/options",["require","jquery","./defaults","./utils"],function(t,e,i,n){function o(e,o){if(this.options=e,null!=o&&this.fromElement(o),this.options=i.apply(this.options),o&&o.is("input")){var s=t(this.get("amdBase")+"compat/inputData");this.options.dataAdapter=n.Decorate(this.options.dataAdapter,s)}}return o.prototype.fromElement=function(t){var i=["select2"];null==this.options.multiple&&(this.options.multiple=t.prop("multiple")),null==this.options.disabled&&(this.options.disabled=t.prop("disabled")),null==this.options.language&&(t.prop("lang")?this.options.language=t.prop("lang").toLowerCase():t.closest("[lang]").prop("lang")&&(this.options.language=t.closest("[lang]").prop("lang"))),null==this.options.dir&&(t.prop("dir")?this.options.dir=t.prop("dir"):t.closest("[dir]").prop("dir")?this.options.dir=t.closest("[dir]").prop("dir"):this.options.dir="ltr"),t.prop("disabled",this.options.disabled),t.prop("multiple",this.options.multiple),t.data("select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),t.data("data",t.data("select2Tags")),t.data("tags",!0)),t.data("ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),t.attr("ajax--url",t.data("ajaxUrl")),t.data("ajax--url",t.data("ajaxUrl")));var o={};o=e.fn.jquery&&"1."==e.fn.jquery.substr(0,2)&&t[0].dataset?e.extend(!0,{},t[0].dataset,t.data()):t.data();var s=e.extend(!0,{},o);s=n._convertData(s);for(var r in s)e.inArray(r,i)>-1||(e.isPlainObject(this.options[r])?e.extend(this.options[r],s[r]):this.options[r]=s[r]);return this},o.prototype.get=function(t){return this.options[t]},o.prototype.set=function(t,e){this.options[t]=e},o}),e.define("select2/core",["jquery","./options","./utils","./keys"],function(t,e,i,n){var o=function(t,i){null!=t.data("select2")&&t.data("select2").destroy(),this.$element=t,this.id=this._generateId(t),i=i||{},this.options=new e(i,t),o.__super__.constructor.call(this);var n=t.attr("tabindex")||0;t.data("old-tabindex",n),t.attr("tabindex","-1");var s=this.options.get("dataAdapter");this.dataAdapter=new s(t,this.options);var r=this.render();this._placeContainer(r);var a=this.options.get("selectionAdapter");this.selection=new a(t,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,r);var l=this.options.get("dropdownAdapter");this.dropdown=new l(t,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,r);var c=this.options.get("resultsAdapter");this.results=new c(t,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown);var u=this;this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),
this.dataAdapter.current(function(t){u.trigger("selection:update",{data:t})}),t.addClass("select2-hidden-accessible"),t.attr("aria-hidden","true"),this._syncAttributes(),t.data("select2",this)};return i.Extend(o,i.Observable),o.prototype._generateId=function(t){var e="";return e=null!=t.attr("id")?t.attr("id"):null!=t.attr("name")?t.attr("name")+"-"+i.generateChars(2):i.generateChars(4),e="select2-"+e},o.prototype._placeContainer=function(t){t.insertAfter(this.$element);var e=this._resolveWidth(this.$element,this.options.get("width"));null!=e&&t.css("width",e)},o.prototype._resolveWidth=function(t,e){var i=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==e){var n=this._resolveWidth(t,"style");return null!=n?n:this._resolveWidth(t,"element")}if("element"==e){var o=t.outerWidth(!1);return o<=0?"auto":o+"px"}if("style"==e){var s=t.attr("style");if("string"!=typeof s)return null;for(var r=s.split(";"),a=0,l=r.length;a<l;a+=1){var c=r[a].replace(/\s/g,""),u=c.match(i);if(null!==u&&u.length>=1)return u[1]}return null}return e},o.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},o.prototype._registerDomEvents=function(){var e=this;this.$element.on("change.select2",function(){e.dataAdapter.current(function(t){e.trigger("selection:update",{data:t})})}),this._sync=i.bind(this._syncAttributes,this),this.$element[0].attachEvent&&this.$element[0].attachEvent("onpropertychange",this._sync);var n=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;null!=n?(this._observer=new n(function(i){t.each(i,e._sync)}),this._observer.observe(this.$element[0],{attributes:!0,subtree:!1})):this.$element[0].addEventListener&&this.$element[0].addEventListener("DOMAttrModified",e._sync,!1)},o.prototype._registerDataEvents=function(){var t=this;this.dataAdapter.on("*",function(e,i){t.trigger(e,i)})},o.prototype._registerSelectionEvents=function(){var e=this,i=["toggle"];this.selection.on("toggle",function(){e.toggleDropdown()}),this.selection.on("*",function(n,o){t.inArray(n,i)===-1&&e.trigger(n,o)})},o.prototype._registerDropdownEvents=function(){var t=this;this.dropdown.on("*",function(e,i){t.trigger(e,i)})},o.prototype._registerResultsEvents=function(){var t=this;this.results.on("*",function(e,i){t.trigger(e,i)})},o.prototype._registerEvents=function(){var t=this;this.on("open",function(){t.$container.addClass("select2-container--open")}),this.on("close",function(){t.$container.removeClass("select2-container--open")}),this.on("enable",function(){t.$container.removeClass("select2-container--disabled")}),this.on("disable",function(){t.$container.addClass("select2-container--disabled")}),this.on("focus",function(){t.$container.addClass("select2-container--focus")}),this.on("blur",function(){t.$container.removeClass("select2-container--focus")}),this.on("query",function(e){t.isOpen()||t.trigger("open"),this.dataAdapter.query(e,function(i){t.trigger("results:all",{data:i,query:e})})}),this.on("query:append",function(e){this.dataAdapter.query(e,function(i){t.trigger("results:append",{data:i,query:e})})}),this.on("keypress",function(e){var i=e.which;t.isOpen()?i===n.ENTER?(t.trigger("results:select"),e.preventDefault()):i===n.SPACE&&e.ctrlKey?(t.trigger("results:toggle"),e.preventDefault()):i===n.UP?(t.trigger("results:previous"),e.preventDefault()):i===n.DOWN?(t.trigger("results:next"),e.preventDefault()):i!==n.ESC&&i!==n.TAB||(t.close(),e.preventDefault()):(i===n.ENTER||i===n.SPACE||(i===n.DOWN||i===n.UP)&&e.altKey)&&(t.open(),e.preventDefault())})},o.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.options.get("disabled")?(this.isOpen()&&this.close(),this.trigger("disable")):this.trigger("enable")},o.prototype.trigger=function(t,e){var i=o.__super__.trigger,n={open:"opening",close:"closing",select:"selecting",unselect:"unselecting"};if(t in n){var s=n[t],r={prevented:!1,name:t,args:e};if(i.call(this,s,r),r.prevented)return void(e.prevented=!0)}i.call(this,t,e)},o.prototype.toggleDropdown=function(){this.options.get("disabled")||(this.isOpen()?this.close():this.open())},o.prototype.open=function(){this.isOpen()||(this.trigger("query",{}),this.trigger("open"))},o.prototype.close=function(){this.isOpen()&&this.trigger("close")},o.prototype.isOpen=function(){return this.$container.hasClass("select2-container--open")},o.prototype.enable=function(t){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.'),null!=t&&0!==t.length||(t=[!0]);var e=!t[0];this.$element.prop("disabled",e)},o.prototype.data=function(){this.options.get("debug")&&arguments.length>0&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var t=[];return this.dataAdapter.current(function(e){t=e}),t},o.prototype.val=function(e){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),null==e||0===e.length)return this.$element.val();var i=e[0];t.isArray(i)&&(i=t.map(i,function(t){return t.toString()})),this.$element.val(i).trigger("change")},o.prototype.destroy=function(){this.$container.remove(),this.$element[0].detachEvent&&this.$element[0].detachEvent("onpropertychange",this._sync),null!=this._observer?(this._observer.disconnect(),this._observer=null):this.$element[0].removeEventListener&&this.$element[0].removeEventListener("DOMAttrModified",this._sync,!1),this._sync=null,this.$element.off(".select2"),this.$element.attr("tabindex",this.$element.data("old-tabindex")),this.$element.removeClass("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),this.$element.removeData("select2"),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},o.prototype.render=function(){var e=t('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return e.attr("dir",this.options.get("dir")),this.$container=e,this.$container.addClass("select2-container--"+this.options.get("theme")),e.data("element",this.$element),e},o}),e.define("jquery.select2",["jquery","require","./select2/core","./select2/defaults"],function(t,e,i,n){if(e("jquery.mousewheel"),null==t.fn.select2){var o=["open","close","destroy"];t.fn.select2=function(e){if(e=e||{},"object"==typeof e)return this.each(function(){var n=t.extend({},e,!0);new i(t(this),n)}),this;if("string"==typeof e){var n=this.data("select2");null==n&&window.console&&console.error&&console.error("The select2('"+e+"') method was called on an element that is not using Select2.");var s=Array.prototype.slice.call(arguments,1),r=n[e](s);return t.inArray(e,o)>-1?this:r}throw new Error("Invalid arguments for Select2: "+e)}}return null==t.fn.select2.defaults&&(t.fn.select2.defaults=n),i}),e.define("jquery.mousewheel",["jquery"],function(t){return t}),{define:e.define,require:e.require}}(),i=e.require("jquery.select2");return t.fn.select2.amd=e,i})},{jquery:69,"jquery.mousewheel":68}],92:[function(require,module,exports){!function($){function Timepicker(){this.debug=!0,this._curInst=null,this._disabledInputs=[],this._timepickerShowing=!1,this._inDialog=!1,this._dialogClass="ui-timepicker-dialog",this._mainDivId="ui-timepicker-div",this._inlineClass="ui-timepicker-inline",this._currentClass="ui-timepicker-current",this._dayOverClass="ui-timepicker-days-cell-over",this.regional=[],this.regional[""]={hourText:"Hour",minuteText:"Minute",amPmText:["AM","PM"],closeButtonText:"Done",nowButtonText:"Now",deselectButtonText:"Deselect"},this._defaults={showOn:"focus",button:null,showAnim:"fadeIn",showOptions:{},appendText:"",beforeShow:null,onSelect:null,onClose:null,timeSeparator:":",periodSeparator:" ",showPeriod:!1,showPeriodLabels:!0,showLeadingZero:!0,showMinutesLeadingZero:!0,altField:"",defaultTime:"now",myPosition:"left top",atPosition:"left bottom",onHourShow:null,onMinuteShow:null,hours:{starts:0,ends:23},minutes:{starts:0,ends:55,interval:5,manual:[]},rows:4,showHours:!0,showMinutes:!0,optionalMinutes:!1,showCloseButton:!1,showNowButton:!1,showDeselectButton:!1,maxTime:{hour:null,minute:null},minTime:{hour:null,minute:null}},$.extend(this._defaults,this.regional[""]),this.tpDiv=$('<div id="'+this._mainDivId+'" class="ui-timepicker ui-widget ui-helper-clearfix ui-corner-all " style="display: none"></div>')}function extendRemove(t,e){$.extend(t,e);for(var i in e)null!=e[i]&&void 0!=e[i]||(t[i]=e[i]);return t}$.extend($.ui,{timepicker:{version:"0.3.3"}});var PROP_NAME="timepicker",tpuuid=(new Date).getTime();$.extend(Timepicker.prototype,{markerClassName:"hasTimepicker",log:function(){this.debug&&console.log.apply("",arguments)},_widgetTimepicker:function(){return this.tpDiv},setDefaults:function(t){return extendRemove(this._defaults,t||{}),this},_attachTimepicker:function(target,settings){var inlineSettings=null;for(var attrName in this._defaults){var attrValue=target.getAttribute("time:"+attrName);if(attrValue){inlineSettings=inlineSettings||{};try{inlineSettings[attrName]=eval(attrValue)}catch(err){inlineSettings[attrName]=attrValue}}}var nodeName=target.nodeName.toLowerCase(),inline="div"==nodeName||"span"==nodeName;target.id||(this.uuid+=1,target.id="tp"+this.uuid);var inst=this._newInst($(target),inline);inst.settings=$.extend({},settings||{},inlineSettings||{}),"input"==nodeName?(this._connectTimepicker(target,inst),this._setTimeFromField(inst)):inline&&this._inlineTimepicker(target,inst)},_newInst:function(t,e){var i=t[0].id.replace(/([^A-Za-z0-9_-])/g,"\\\\$1");return{id:i,input:t,inline:e,tpDiv:e?$('<div class="'+this._inlineClass+' ui-timepicker ui-widget  ui-helper-clearfix"></div>'):this.tpDiv}},_connectTimepicker:function(t,e){var i=$(t);e.append=$([]),e.trigger=$([]),i.hasClass(this.markerClassName)||(this._attachments(i,e),i.addClass(this.markerClassName).keydown(this._doKeyDown).keyup(this._doKeyUp).bind("setData.timepicker",function(t,i,n){e.settings[i]=n}).bind("getData.timepicker",function(t,i){return this._get(e,i)}),$.data(t,PROP_NAME,e))},_doKeyDown:function(t){var e=$.timepicker._getInst(t.target),i=!0;if(e._keyEvent=!0,$.timepicker._timepickerShowing)switch(t.keyCode){case 9:$.timepicker._hideTimepicker(),i=!1;break;case 13:return $.timepicker._updateSelectedValue(e),$.timepicker._hideTimepicker(),!1;case 27:$.timepicker._hideTimepicker();break;default:i=!1}else 36==t.keyCode&&t.ctrlKey?$.timepicker._showTimepicker(this):i=!1;i&&(t.preventDefault(),t.stopPropagation())},_doKeyUp:function(t){var e=$.timepicker._getInst(t.target);$.timepicker._setTimeFromField(e),$.timepicker._updateTimepicker(e)},_attachments:function(t,e){var i=this._get(e,"appendText"),n=this._get(e,"isRTL");e.append&&e.append.remove(),i&&(e.append=$('<span class="'+this._appendClass+'">'+i+"</span>"),t[n?"before":"after"](e.append)),t.unbind("focus.timepicker",this._showTimepicker),t.unbind("click.timepicker",this._adjustZIndex),e.trigger&&e.trigger.remove();var o=this._get(e,"showOn");if("focus"!=o&&"both"!=o||(t.bind("focus.timepicker",this._showTimepicker),t.bind("click.timepicker",this._adjustZIndex)),"button"==o||"both"==o){var s=this._get(e,"button");null==s&&(s=$('<button class="ui-timepicker-trigger" type="button">...</button>'),t.after(s)),$(s).bind("click.timepicker",function(){return $.timepicker._timepickerShowing&&$.timepicker._lastInput==t[0]?$.timepicker._hideTimepicker():e.input.is(":disabled")||$.timepicker._showTimepicker(t[0]),!1})}},_inlineTimepicker:function(t,e){var i=$(t);i.hasClass(this.markerClassName)||(i.addClass(this.markerClassName).append(e.tpDiv).bind("setData.timepicker",function(t,i,n){e.settings[i]=n}).bind("getData.timepicker",function(t,i){return this._get(e,i)}),$.data(t,PROP_NAME,e),this._setTimeFromField(e),this._updateTimepicker(e),e.tpDiv.show())},_adjustZIndex:function(t){t=t.target||t;var e=$.timepicker._getInst(t);e.tpDiv.css("zIndex",$.timepicker._getZIndex(t)+1)},_showTimepicker:function(t){if(t=t.target||t,"input"!=t.nodeName.toLowerCase()&&(t=$("input",t.parentNode)[0]),!$.timepicker._isDisabledTimepicker(t)&&$.timepicker._lastInput!=t){$.timepicker._hideTimepicker();var e=$.timepicker._getInst(t);$.timepicker._curInst&&$.timepicker._curInst!=e&&$.timepicker._curInst.tpDiv.stop(!0,!0);var i=$.timepicker._get(e,"beforeShow");extendRemove(e.settings,i?i.apply(t,[t,e]):{}),e.lastVal=null,$.timepicker._lastInput=t,$.timepicker._setTimeFromField(e),$.timepicker._inDialog&&(t.value=""),$.timepicker._pos||($.timepicker._pos=$.timepicker._findPos(t),$.timepicker._pos[1]+=t.offsetHeight);var n=!1;$(t).parents().each(function(){return n|="fixed"==$(this).css("position"),!n});var o={left:$.timepicker._pos[0],top:$.timepicker._pos[1]};if($.timepicker._pos=null,e.tpDiv.css({position:"absolute",display:"block",top:"-1000px"}),$.timepicker._updateTimepicker(e),!e.inline&&"object"==typeof $.ui.position){e.tpDiv.position({of:e.input,my:$.timepicker._get(e,"myPosition"),at:$.timepicker._get(e,"atPosition"),collision:"flip"});var o=e.tpDiv.offset();$.timepicker._pos=[o.top,o.left]}if(e._hoursClicked=!1,e._minutesClicked=!1,o=$.timepicker._checkOffset(e,o,n),e.tpDiv.css({position:$.timepicker._inDialog&&$.blockUI?"static":n?"fixed":"absolute",display:"none",left:o.left+"px",top:o.top+"px"}),!e.inline){var s=$.timepicker._get(e,"showAnim"),r=$.timepicker._get(e,"duration"),a=function(){$.timepicker._timepickerShowing=!0;var t=$.timepicker._getBorders(e.tpDiv);e.tpDiv.find("iframe.ui-timepicker-cover").css({left:-t[0],top:-t[1],width:e.tpDiv.outerWidth(),height:e.tpDiv.outerHeight()})};$.timepicker._adjustZIndex(t),$.effects&&$.effects[s]?e.tpDiv.show(s,$.timepicker._get(e,"showOptions"),r,a):e.tpDiv.show(s?r:null,a),s&&r||a(),e.input.is(":visible")&&!e.input.is(":disabled")&&e.input.focus(),$.timepicker._curInst=e}}},_getZIndex:function(t){for(var e,i,n=$(t),o=0;n.length&&n[0]!==document;)e=n.css("position"),"absolute"!==e&&"relative"!==e&&"fixed"!==e||(i=parseInt(n.css("zIndex"),10),isNaN(i)||0===i||i>o&&(o=i)),n=n.parent();return o},_refreshTimepicker:function(t){var e=this._getInst(t);e&&this._updateTimepicker(e)},_updateTimepicker:function(t){t.tpDiv.empty().append(this._generateHTML(t)),this._rebindDialogEvents(t)},_rebindDialogEvents:function(t){var e=$.timepicker._getBorders(t.tpDiv),i=this;t.tpDiv.find("iframe.ui-timepicker-cover").css({left:-e[0],top:-e[1],width:t.tpDiv.outerWidth(),height:t.tpDiv.outerHeight()}).end().find(".ui-timepicker-minute-cell").unbind().bind("click",{fromDoubleClick:!1},$.proxy($.timepicker.selectMinutes,this)).bind("dblclick",{fromDoubleClick:!0},$.proxy($.timepicker.selectMinutes,this)).end().find(".ui-timepicker-hour-cell").unbind().bind("click",{fromDoubleClick:!1},$.proxy($.timepicker.selectHours,this)).bind("dblclick",{fromDoubleClick:!0},$.proxy($.timepicker.selectHours,this)).end().find(".ui-timepicker td a").unbind().bind("mouseout",function(){$(this).removeClass("ui-state-hover"),this.className.indexOf("ui-timepicker-prev")!=-1&&$(this).removeClass("ui-timepicker-prev-hover"),this.className.indexOf("ui-timepicker-next")!=-1&&$(this).removeClass("ui-timepicker-next-hover")}).bind("mouseover",function(){i._isDisabledTimepicker(t.inline?t.tpDiv.parent()[0]:t.input[0])||($(this).parents(".ui-timepicker-calendar").find("a").removeClass("ui-state-hover"),$(this).addClass("ui-state-hover"),this.className.indexOf("ui-timepicker-prev")!=-1&&$(this).addClass("ui-timepicker-prev-hover"),this.className.indexOf("ui-timepicker-next")!=-1&&$(this).addClass("ui-timepicker-next-hover"))}).end().find("."+this._dayOverClass+" a").trigger("mouseover").end().find(".ui-timepicker-now").bind("click",function(t){$.timepicker.selectNow(t)}).end().find(".ui-timepicker-deselect").bind("click",function(t){$.timepicker.deselectTime(t)}).end().find(".ui-timepicker-close").bind("click",function(t){$.timepicker._hideTimepicker()}).end()},_generateHTML:function(t){var e,i,n,o,s=1==this._get(t,"showPeriod"),r=1==this._get(t,"showPeriodLabels"),a=1==this._get(t,"showLeadingZero"),l=1==this._get(t,"showHours"),c=1==this._get(t,"showMinutes"),u=this._get(t,"amPmText"),d=this._get(t,"rows"),h=0,p=0,f=0,g=0,m=0,v=0,y=Array(),b=this._get(t,"hours"),_=null,w=0,k=this._get(t,"hourText"),x=this._get(t,"showCloseButton"),C=this._get(t,"closeButtonText"),D=this._get(t,"showNowButton"),T=this._get(t,"nowButtonText"),S=this._get(t,"showDeselectButton"),I=this._get(t,"deselectButtonText"),P=x||D||S;for(e=b.starts;e<=b.ends;e++)y.push(e);if(_=Math.ceil(y.length/d),r){for(w=0;w<y.length;w++)y[w]<12?f++:g++;w=0,h=Math.floor(f/y.length*d),p=Math.floor(g/y.length*d),d!=h+p&&(f&&(!g||!h||p&&f/h>=g/p)?h++:p++),m=Math.min(h,1),v=h+1,_=0==h?Math.ceil(g/p):0==p?Math.ceil(f/h):Math.ceil(Math.max(f/h,g/p))}if(o='<table class="ui-timepicker-table ui-widget-content ui-corner-all"><tr>',l){for(o+='<td class="ui-timepicker-hours"><div class="ui-timepicker-title ui-widget-header ui-helper-clearfix ui-corner-all">'+k+'</div><table class="ui-timepicker">',i=1;i<=d;i++){for(o+="<tr>",i==m&&r&&(o+='<th rowspan="'+h.toString()+'" class="periods" scope="row">'+u[0]+"</th>"),i==v&&r&&(o+='<th rowspan="'+p.toString()+'" class="periods" scope="row">'+u[1]+"</th>"),n=1;n<=_;n++)r&&i<v&&y[w]>=12?o+=this._generateHTMLHourCell(t,void 0,s,a):(o+=this._generateHTMLHourCell(t,y[w],s,a),w++);o+="</tr>"}o+="</table></td>"}if(c&&(o+='<td class="ui-timepicker-minutes">',o+=this._generateHTMLMinutes(t),o+="</td>"),o+="</tr>",P){var j='<tr><td colspan="3"><div class="ui-timepicker-buttonpane ui-widget-content">';D&&(j+='<button type="button" class="ui-timepicker-now ui-state-default ui-corner-all"  data-timepicker-instance-id="#'+t.id.replace(/\\\\/g,"\\")+'" >'+T+"</button>"),S&&(j+='<button type="button" class="ui-timepicker-deselect ui-state-default ui-corner-all"  data-timepicker-instance-id="#'+t.id.replace(/\\\\/g,"\\")+'" >'+I+"</button>"),x&&(j+='<button type="button" class="ui-timepicker-close ui-state-default ui-corner-all"  data-timepicker-instance-id="#'+t.id.replace(/\\\\/g,"\\")+'" >'+C+"</button>"),o+=j+"</div></td></tr>"}return o+="</table>"},_updateMinuteDisplay:function(t){var e=this._generateHTMLMinutes(t);t.tpDiv.find("td.ui-timepicker-minutes").html(e),this._rebindDialogEvents(t)},_generateHTMLMinutes:function(t){var e,n,o="",s=this._get(t,"rows"),r=Array(),a=this._get(t,"minutes"),l=null,c=0,u=1==this._get(t,"showMinutesLeadingZero"),d=this._get(t,"onMinuteShow"),h=this._get(t,"minuteText");for(a.starts||(a.starts=0),a.ends||(a.ends=59),a.manual||(a.manual=[]),e=a.starts;e<=a.ends;e+=a.interval)r.push(e);for(i=0;i<a.manual.length;i++){var p=a.manual[i];"number"!=typeof p||p<0||p>59||$.inArray(p,r)>=0||r.push(p)}if(r.sort(function(t,e){return t-e}),l=Math.round(r.length/s+.49),d&&0==d.apply(t.input?t.input[0]:null,[t.hours,t.minutes]))for(c=0;c<r.length;c+=1)if(e=r[c],d.apply(t.input?t.input[0]:null,[t.hours,e])){t.minutes=e;break}for(o+='<div class="ui-timepicker-title ui-widget-header ui-helper-clearfix ui-corner-all">'+h+'</div><table class="ui-timepicker">',c=0,n=1;n<=s;n++){for(o+="<tr>";c<n*l;){var e=r[c],f="";void 0!==e&&(f=e<10&&u?"0"+e.toString():e.toString()),o+=this._generateHTMLMinuteCell(t,e,f),c++}o+="</tr>"}return o+="</table>"},_generateHTMLHourCell:function(t,e,i,n){var o=e;e>12&&i&&(o=e-12),0==o&&i&&(o=12),o<10&&n&&(o="0"+o);var s="",r=!0,a=this._get(t,"onHourShow"),l=this._get(t,"maxTime"),c=this._get(t,"minTime");return void 0==e?s='<td><span class="ui-state-default ui-state-disabled">&nbsp;</span></td>':(a&&(r=a.apply(t.input?t.input[0]:null,[e])),r&&(!isNaN(parseInt(l.hour))&&e>l.hour&&(r=!1),!isNaN(parseInt(c.hour))&&e<c.hour&&(r=!1)),s=r?'<td class="ui-timepicker-hour-cell" data-timepicker-instance-id="#'+t.id.replace(/\\\\/g,"\\")+'" data-hour="'+e.toString()+'"><a class="ui-state-default '+(e==t.hours?"ui-state-active":"")+'">'+o.toString()+"</a></td>":'<td><span class="ui-state-default ui-state-disabled '+(e==t.hours?" ui-state-active ":" ")+'">'+o.toString()+"</span></td>")},_generateHTMLMinuteCell:function(t,e,i){var n="",o=!0,s=t.hours,r=this._get(t,"onMinuteShow"),a=this._get(t,"maxTime"),l=this._get(t,"minTime");return r&&(o=r.apply(t.input?t.input[0]:null,[t.hours,e])),void 0==e?n='<td><span class="ui-state-default ui-state-disabled">&nbsp;</span></td>':(o&&null!==s&&(!isNaN(parseInt(a.hour))&&!isNaN(parseInt(a.minute))&&s>=a.hour&&e>a.minute&&(o=!1),!isNaN(parseInt(l.hour))&&!isNaN(parseInt(l.minute))&&s<=l.hour&&e<l.minute&&(o=!1)),n=o?'<td class="ui-timepicker-minute-cell" data-timepicker-instance-id="#'+t.id.replace(/\\\\/g,"\\")+'" data-minute="'+e.toString()+'" ><a class="ui-state-default '+(e==t.minutes?"ui-state-active":"")+'" >'+i+"</a></td>":'<td><span class="ui-state-default ui-state-disabled" >'+i+"</span></td>")},_destroyTimepicker:function(t){var e=$(t),i=$.data(t,PROP_NAME);if(e.hasClass(this.markerClassName)){var n=t.nodeName.toLowerCase();$.removeData(t,PROP_NAME),"input"==n?(i.append.remove(),i.trigger.remove(),e.removeClass(this.markerClassName).unbind("focus.timepicker",this._showTimepicker).unbind("click.timepicker",this._adjustZIndex)):"div"!=n&&"span"!=n||e.removeClass(this.markerClassName).empty()}},_enableTimepicker:function(t){var e=$(t),i=e.attr("id"),n=$.data(t,PROP_NAME);if(e.hasClass(this.markerClassName)){var o=t.nodeName.toLowerCase();if("input"==o){t.disabled=!1;var s=this._get(n,"button");$(s).removeClass("ui-state-disabled").disabled=!1,n.trigger.filter("button").each(function(){this.disabled=!1}).end()}else if("div"==o||"span"==o){var r=e.children("."+this._inlineClass);r.children().removeClass("ui-state-disabled"),r.find("button").each(function(){this.disabled=!1})}this._disabledInputs=$.map(this._disabledInputs,function(t){return t==i?null:t})}},_disableTimepicker:function(t){var e=$(t),i=$.data(t,PROP_NAME);if(e.hasClass(this.markerClassName)){var n=t.nodeName.toLowerCase();if("input"==n){var o=this._get(i,"button");$(o).addClass("ui-state-disabled").disabled=!0,t.disabled=!0,i.trigger.filter("button").each(function(){this.disabled=!0}).end()}else if("div"==n||"span"==n){var s=e.children("."+this._inlineClass);s.children().addClass("ui-state-disabled"),s.find("button").each(function(){this.disabled=!0})}this._disabledInputs=$.map(this._disabledInputs,function(e){return e==t?null:e}),this._disabledInputs[this._disabledInputs.length]=e.attr("id")}},_isDisabledTimepicker:function(t){if(!t)return!1;for(var e=0;e<this._disabledInputs.length;e++)if(this._disabledInputs[e]==t)return!0;return!1},_checkOffset:function(t,e,i){var n=t.tpDiv.outerWidth(),o=t.tpDiv.outerHeight(),s=t.input?t.input.outerWidth():0,r=t.input?t.input.outerHeight():0,a=document.documentElement.clientWidth+$(document).scrollLeft(),l=document.documentElement.clientHeight+$(document).scrollTop();return e.left-=this._get(t,"isRTL")?n-s:0,e.left-=i&&e.left==t.input.offset().left?$(document).scrollLeft():0,e.top-=i&&e.top==t.input.offset().top+r?$(document).scrollTop():0,e.left-=Math.min(e.left,e.left+n>a&&a>n?Math.abs(e.left+n-a):0),e.top-=Math.min(e.top,e.top+o>l&&l>o?Math.abs(o+r):0),e},_findPos:function(t){for(var e=this._getInst(t),i=this._get(e,"isRTL");t&&("hidden"==t.type||1!=t.nodeType);)t=t[i?"previousSibling":"nextSibling"];var n=$(t).offset();return[n.left,n.top]},_getBorders:function(t){var e=function(t){return{thin:1,medium:2,thick:3}[t]||t};return[parseFloat(e(t.css("border-left-width"))),parseFloat(e(t.css("border-top-width")))]},_checkExternalClick:function(t){if($.timepicker._curInst){var e=$(t.target);e[0].id==$.timepicker._mainDivId||0!=e.parents("#"+$.timepicker._mainDivId).length||e.hasClass($.timepicker.markerClassName)||e.hasClass($.timepicker._triggerClass)||!$.timepicker._timepickerShowing||$.timepicker._inDialog&&$.blockUI||$.timepicker._hideTimepicker()}},_hideTimepicker:function(t){var e=this._curInst;if(e&&(!t||e==$.data(t,PROP_NAME))&&this._timepickerShowing){var i=this._get(e,"showAnim"),n=this._get(e,"duration"),o=function(){$.timepicker._tidyDialog(e),this._curInst=null};$.effects&&$.effects[i]?e.tpDiv.hide(i,$.timepicker._get(e,"showOptions"),n,o):e.tpDiv["slideDown"==i?"slideUp":"fadeIn"==i?"fadeOut":"hide"](i?n:null,o),i||o(),this._timepickerShowing=!1,this._lastInput=null,this._inDialog&&(this._dialogInput.css({position:"absolute",left:"0",top:"-100px"}),$.blockUI&&($.unblockUI(),$("body").append(this.tpDiv))),this._inDialog=!1;var s=this._get(e,"onClose");s&&s.apply(e.input?e.input[0]:null,[e.input?e.input.val():"",e])}},_tidyDialog:function(t){t.tpDiv.removeClass(this._dialogClass).unbind(".ui-timepicker")},_getInst:function(t){try{return $.data(t,PROP_NAME)}catch(e){throw"Missing instance data for this timepicker"}},_get:function(t,e){return void 0!==t.settings[e]?t.settings[e]:this._defaults[e]},_setTimeFromField:function(t){if(t.input.val()!=t.lastVal){var e=this._get(t,"defaultTime"),i="now"==e?this._getCurrentTimeRounded(t):e;if(0==t.inline&&""!=t.input.val()&&(i=t.input.val()),i instanceof Date)t.hours=i.getHours(),t.minutes=i.getMinutes();else{var n=t.lastVal=i;if(""==i)t.hours=-1,t.minutes=-1;else{var o=this.parseTime(t,n);t.hours=o.hours,t.minutes=o.minutes}}$.timepicker._updateTimepicker(t)}},_optionTimepicker:function(t,e,i){var n=this._getInst(t);if(2==arguments.length&&"string"==typeof e)return"defaults"==e?$.extend({},$.timepicker._defaults):n?"all"==e?$.extend({},n.settings):this._get(n,e):null;var o=e||{};"string"==typeof e&&(o={},o[e]=i),n&&(extendRemove(n.settings,o),this._curInst==n&&(this._hideTimepicker(),this._updateTimepicker(n)),n.inline&&this._updateTimepicker(n))},_setTimeTimepicker:function(t,e){var i=this._getInst(t);i&&(this._setTime(i,e),this._updateTimepicker(i),this._updateAlternate(i,e))},_setTime:function(t,e,i){var n=t.hours,o=t.minutes;if(e instanceof Date)t.hours=e.getHours(),t.minutes=e.getMinutes();else{var e=this.parseTime(t,e);t.hours=e.hours,t.minutes=e.minutes}n==t.hours&&o==t.minutes||i||t.input.trigger("change"),this._updateTimepicker(t),this._updateSelectedValue(t)},_getCurrentTimeRounded:function(t){var e=new Date,i=e.getMinutes(),n=this._get(t,"minutes"),o=Math.round(i/n.interval)*n.interval;return e.setMinutes(o),e},parseTime:function(t,e){var i=new Object;if(i.hours=-1,i.minutes=-1,!e)return"";var n=this._get(t,"timeSeparator"),o=this._get(t,"amPmText"),s=this._get(t,"showHours"),r=this._get(t,"showMinutes"),a=this._get(t,"optionalMinutes"),l=1==this._get(t,"showPeriod"),c=e.indexOf(n);if(c!=-1?(i.hours=parseInt(e.substr(0,c),10),i.minutes=parseInt(e.substr(c+1),10)):!s||r&&!a?!s&&r&&(i.minutes=parseInt(e,10)):i.hours=parseInt(e,10),s){var u=e.toUpperCase();i.hours<12&&l&&u.indexOf(o[1].toUpperCase())!=-1&&(i.hours+=12),12==i.hours&&l&&u.indexOf(o[0].toUpperCase())!=-1&&(i.hours=0)}return i},selectNow:function(t){var e=$(t.target).attr("data-timepicker-instance-id"),i=$(e),n=this._getInst(i[0]),o=new Date;n.hours=o.getHours(),n.minutes=o.getMinutes(),this._updateSelectedValue(n),this._updateTimepicker(n),this._hideTimepicker()},deselectTime:function(t){var e=$(t.target).attr("data-timepicker-instance-id"),i=$(e),n=this._getInst(i[0]);n.hours=-1,n.minutes=-1,this._updateSelectedValue(n),this._hideTimepicker()},selectHours:function(t){var e=$(t.currentTarget),i=e.attr("data-timepicker-instance-id"),n=parseInt(e.attr("data-hour")),o=t.data.fromDoubleClick,s=$(i),r=this._getInst(s[0]),a=1==this._get(r,"showMinutes");if($.timepicker._isDisabledTimepicker(s.attr("id")))return!1;e.parents(".ui-timepicker-hours:first").find("a").removeClass("ui-state-active"),e.children("a").addClass("ui-state-active"),r.hours=n;var l=this._get(r,"onMinuteShow"),c=this._get(r,"maxTime"),u=this._get(r,"minTime");return!l&&isNaN(parseInt(c.minute))&&isNaN(parseInt(u.minute))||this._updateMinuteDisplay(r),this._updateSelectedValue(r),r._hoursClicked=!0,(r._minutesClicked||o||0==a)&&$.timepicker._hideTimepicker(),!1},selectMinutes:function(t){var e=$(t.currentTarget),i=e.attr("data-timepicker-instance-id"),n=parseInt(e.attr("data-minute")),o=t.data.fromDoubleClick,s=$(i),r=this._getInst(s[0]),a=1==this._get(r,"showHours");return!$.timepicker._isDisabledTimepicker(s.attr("id"))&&(e.parents(".ui-timepicker-minutes:first").find("a").removeClass("ui-state-active"),e.children("a").addClass("ui-state-active"),r.minutes=n,this._updateSelectedValue(r),r._minutesClicked=!0,!(!r._hoursClicked&&!o&&0!=a)&&($.timepicker._hideTimepicker(),!1))},_updateSelectedValue:function(t){var e=this._getParsedTime(t);t.input&&(t.input.val(e),t.input.trigger("change"));var i=this._get(t,"onSelect");return i&&i.apply(t.input?t.input[0]:null,[e,t]),this._updateAlternate(t,e),e},_getParsedTime:function(t){if(t.hours==-1&&t.minutes==-1)return"";(t.hours<t.hours.starts||t.hours>t.hours.ends)&&(t.hours=0),(t.minutes<t.minutes.starts||t.minutes>t.minutes.ends)&&(t.minutes=0);var e="",i=1==this._get(t,"showPeriod"),n=1==this._get(t,"showLeadingZero"),o=1==this._get(t,"showHours"),s=1==this._get(t,"showMinutes"),r=1==this._get(t,"optionalMinutes"),a=this._get(t,"amPmText"),l=t.hours?t.hours:0,c=t.minutes?t.minutes:0,u=l?l:0,d="";u==-1&&(u=0),c==-1&&(c=0),i&&(0==t.hours&&(u=12),t.hours<12?e=a[0]:(e=a[1],u>12&&(u-=12)));var h=u.toString();n&&u<10&&(h="0"+h);var p=c.toString();return c<10&&(p="0"+p),o&&(d+=h),!o||!s||r&&0==p||(d+=this._get(t,"timeSeparator")),!s||r&&0==p||(d+=p),o&&e.length>0&&(d+=this._get(t,"periodSeparator")+e),d},_updateAlternate:function(t,e){var i=this._get(t,"altField");i&&$(i).each(function(t,i){$(i).val(e)})},_getTimeAsDateTimepicker:function(t){var e=this._getInst(t);return e.hours==-1&&e.minutes==-1?"":((e.hours<e.hours.starts||e.hours>e.hours.ends)&&(e.hours=0),(e.minutes<e.minutes.starts||e.minutes>e.minutes.ends)&&(e.minutes=0),new Date(0,0,0,e.hours,e.minutes,0))},_getTimeTimepicker:function(t){var e=this._getInst(t);return this._getParsedTime(e)},_getHourTimepicker:function(t){var e=this._getInst(t);return void 0==e?-1:e.hours},_getMinuteTimepicker:function(t){var e=this._getInst(t);return void 0==e?-1:e.minutes}}),$.fn.timepicker=function(t){$.timepicker.initialized||($(document).mousedown($.timepicker._checkExternalClick),$.timepicker.initialized=!0),0===$("#"+$.timepicker._mainDivId).length&&$("body").append($.timepicker.tpDiv);var e=Array.prototype.slice.call(arguments,1);return"string"!=typeof t||"getTime"!=t&&"getTimeAsDate"!=t&&"getHour"!=t&&"getMinute"!=t?"option"==t&&2==arguments.length&&"string"==typeof arguments[1]?$.timepicker["_"+t+"Timepicker"].apply($.timepicker,[this[0]].concat(e)):this.each(function(){"string"==typeof t?$.timepicker["_"+t+"Timepicker"].apply($.timepicker,[this].concat(e)):$.timepicker._attachTimepicker(this,t)}):$.timepicker["_"+t+"Timepicker"].apply($.timepicker,[this[0]].concat(e))},$.timepicker=new Timepicker,$.timepicker.initialized=!1,$.timepicker.uuid=(new Date).getTime(),$.timepicker.version="0.3.3",window["TP_jQuery_"+tpuuid]=$}(jQuery)},{}]},{},[34]);