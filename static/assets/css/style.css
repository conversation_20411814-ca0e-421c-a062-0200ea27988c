/*
Template Name: NewsForest -  Magazine / Blog HTML Template
Author: ScriptsBundle
Version: 1.0
Designed and Development by: ScriptsBundle

====================================
[ CSS TABLE CONTENT ]
------------------------------------
     1.0 - GENERAL CSS
    2.0 - HEADING STYLE
    3.0 - BREADCRUMB
    4.0 - CATEGORY COLOR CODE
    5.0 - TOP BAR
    6.0 - MENU CUSTOMIZATIONS
		6.1 - MENU CONTAINER SIZE
		6.2 - MENU DARK
		6.3 - MENU CSS FOR BLOCKS AND LISTING
		6.4 - <PERSON>NU CATEGORY COLOR CODE
	7.0 - VIDEO SECTION MAIN
	8.0 - PARALLEL POST STYLE
	9.0 - ARCHIVE POSTS
	10.0 - CATEGORY SIDEBAR
	11.0 - VIDEO SECTION
	12.0 - SOCIAL SHARE 2
	13.0 - HEADER
	14.0 - FEATURED SLIDER CSS
	15.0 - FULL WIDTH SLIDER MAIN
	16.0 - FULL WIDTH STYLE 2
	17.0 - OWL SLIDER CUSTOM CSS
	18.0 - CSS PREE LOADR
	19.0 - COMMENT SECTION
	20.0 - FULL WIDTH IMAGE SINGLE POST
	21.0 - FULL WIDTH SLIDER
	22.0 - PAGINATION
	23.0 - AUTHOR LIST
	24.0 - ARTICLE CAPTION AND RELATED CSS
	25.0 - GRID CSS
	26.0 - SMALL GRID CSS
	27.0 - SIDEBAR WIDGET
	28.0 - WIDGET CATEGORY
	29.0 - POL WIDGET
	30.0 - AUTHOR POST WIDGET
	31.0 - PHOTO GALLERY SECTION
	32.0 - TABS
	33.0 - ADVERTISEMENT CSS
	34.0 - CONTACT US CSS
	35.0 - FOOTER CSS
	36.0 - INSTAGRAM GALLERY
	37.0 - LOAD MORE POSTS
	38.0 - FOOTER TAGS
	39.0 - SOCIAL COUNTERS
	40.0 - MAIN TABS CSS
	41.0 - TOP SMALL POSTS SLIDER
	42.0 - RESPONSIVENESS QURIES

-------------------------------------
[ END CSS TABLE CONTENT ]
=====================================



COLOR CODES FOR CATEGORY

1- purple = photography
2- orange = nature
3- yellow = entertainment
4- gray = food and health
5- green = travel
6- pink = life style
7- red = fashion
8- blue = videos
9- maroon = sports
10-Dark Red = technology
10- lightblue =Technology

*/


/* GENERAL CSS */

body {
	color: #444444;
	font-weight: 400;
	position: relative;
	overflow-x: hidden;
	-webkit-text-size-adjust: 100%;
	-webkit-overflow-scrolling: touch;
	-webkit-font-smoothing: antialiased;
	line-height: 1.6em;
	max-width: 1585px;
	margin: 0 auto;
	font-family: 'Roboto', sans-serif;
}

.wrapper {
	width: 100%;
}

.mt10 {
	margin-top: 10px;
}

.mt20 {
	margin-top: 20px;
}

.mt30 {
	margin-top: 30px;
}

.mt40 {
	margin-top: 40px;
}

.mt50 {
	margin-top: 50px;
}

.mt60 {
	margin-top: 60px;
}

.mb10 {
	margin-bottom: 10px;
}

.mb20 {
	margin-bottom: 20px;
}

.mb30 {
	margin-bottom: 30px;
}

.mb40 {
	margin-bottom: 40px;
}

.mb50 {
	margin-bottom: 50px;
}

.nopadding {
	padding: 0;
}
.nomargin {
	margin: 0 !important;
}

.bg-white {
	background-color: #fff;
}

.bg-gray {
	background-color: #F9f9f9;
}

.bg-dark {
	background-color: #161b2a;
}

section {
	padding: 12px 0;
}

.last-heading{
    font-size: 34px;
    text-decoration: underline;
    letter-spacing: 1px;
    font-weight: 400;
    margin-bottom: -14px;
    margin-top: -3px;
    text-align: center;
    font-family: Copperplate, Papyrus, fantasy;
    text-decoration-color: #00aeef;

}

.section {
	margin-bottom: 20px;
	position: relative;
	overflow: hidden;
}
.main-content div.section:last-child {
	margin-bottom:0;	
}
a {
	outline: none;
	color: #242424;
	font-size: 15px;
	text-decoration: none;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

a:hover {
	text-decoration: none;
	color: #f44a56;
}

h1, h2 {
	line-height: 1.5em;
	font-weight: 400;
}

h3, h4 {
	font-weight: 400;
	line-height: 1.4em;
}

h5, h6 {
	line-height: 1.4em;
}

.post-entry p {
	color: #777;
	font-size: 16px;
	line-height: 30px;
	margin-bottom: 15px;
}

.btn-colored-blog {
	background-color: #f44a56;
	color: #FFF;
	border-radius: 4px;
	padding: 8px 20px;
	text-transform: capitalize;
	font-weight: 500;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.btn-colored-blog:hover {
	color: #f44a56;
	background-color: transparent;
	border: 1px solid #f44a56;
}

.show-more-btn {
	text-align: center;
}

.show-more-btn a i {
	margin-right: 5px;
}

.post-tools {
	list-style: none;
	position: relative;
	overflow: hidden;
	overflow: none;
	padding-left: 0;
}

.catname .btn, .post-tools .btn {
	border-radius: 3px;
	color: #fff;
	font-size: 12px;
	font-weight: 400;
	padding: 5px 10px;
	text-transform: capitalize;
	border-bottom:none;
	background-image: -moz-linear-gradient(center bottom, rgba(0, 0, 0, 0.17) 0%, rgba(255, 255, 255, 0.17) 100%);
}

.picture .category-image .catname a:hover {
	background-color:#242424;
}


p .readmore {
	text-transform: capitalize;
	margin-left: 10px;
	letter-spacing: 1px;
	color: #242424;
	font-weight: 500;
}


/*HEADING STYLE*/

.heading {
	position: relative;
	overflow: hidden;
	margin-bottom: 30px;
}

.heading.colored {
	background-color: #F1F1F1;
}

.heading .main-heading {
	font-size: 20px;
	text-transform: capitalize;
	color: #242424;
	font-weight: 500;
	padding: 10px 15px;
	margin: 0;
	float: left;
	line-height: 1.1em;
	border: 1px solid #444;
}

.heading .heading-ping {
	width: 0;
	position: absolute;
	top: 10px;
	height: 0;
	border-style: solid;
	border-width: 10px 0 10px 10px;
	border-color: transparent transparent transparent #242424;
}

.heading-read-more {
	float: right;
	padding: 5px;
}

.heading-read-more a {
	padding: ;
	text-transform: capitalize;
	padding: 6px 10px;
	font-size: 14px;
	font-weight: 500;
}

.heading-read-more a:hover {
	opacity: 0.8;
	color: #FFF;
}

.widget .heading .main-heading {
	color: #242424;
	font-size: 16px;
	font-weight: 500;
	margin: 0;
	padding: 10px 15px;
	line-height: 1.2em;
	border: 1px solid #444;
}


/* ----  BREADCRUMB ---- */

.my-breadcrumb {
	padding: 60px 0;
	text-align: center;
	position: relative;
}

.my-breadcrumb::before {
    background: rgb(0 0 0 / 77%) none repeat scroll 0 0;
	content: "";
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
}

.my-breadcrumb .page-banner h1 {
	color: #fff;
	font-size: 30px;
	font-weight: 500;
	margin: 0;
	text-transform: capitalize;
	font-family: serif;
}

.my-breadcrumb .page-banner .breadcrumb {
	background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
	margin: 0;
	padding: 0;
}

.my-breadcrumb .breadcrumb> li {
	display: inline-block;
	text-transform: capitalize;
	font-size: 16px;
}

.my-breadcrumb .page-banner .breadcrumb a {
	color: #fff;
	text-transform: capitalize;
}

.my-breadcrumb .breadcrumb> li+ li::before {
	content: "\f101 ";
	font-family: FontAwesome;
	padding: 0 5px;
	color: #FFF;
}

.my-breadcrumb .breadcrumb> .active {
	color: #FFF;
	text-transform: capitalize;
}


/*CATEGORY COLOR CODE*/

.btn-purple {
	background-color: #9c27b0;
	color: #FFF;
}

.btn-orange {
	background-color: #ff5722;
	color: #FFF;
}

.btn-yellow {
	background-color: #e28712;
	color: #FFF;
}

.btn-gray {
	background-color: #607d8b;
	color: #FFF;
}

.btn-green {
	background-color: #2ecc71;
	color: #FFF;
}

.btn-pink {
	background-color: #e91e63;
	color: #FFF;
}

.btn-blue {
	background-color: #00b6d8;
	color: #FFF;
}

.btn-blue {
	background-color: #00aeef;
	color: #FFF;
}

.btn-black {
	background-color: #2575fc;
	color: #FFF;
}

.btn-maroon {
	background-color: #7b2143;
	color: #FFF;
}

.btn-lightblue {
	background-color: #8e93c4;
	color: #FFF;
}

.btn-dark-red {
	background-color: #d11141;
	color: #FFF;
}

.section .owl-nav {
	position: absolute;
	right: 15px;
	top: -73px;
	margin: 0;
}

.section .owl-theme .owl-nav div.owl-prev::after, .section .owl-theme .owl-nav div.owl-next::after {
	color: #FFF;
	font-size: 20px;
}

.section .owl-theme .owl-nav div.owl-next, .section .owl-theme .owl-nav div.owl-prev {
	background-color: #444;
	border: none;
	line-height: 28px;
	width: 35px;
	height: 35px;
}
.box-style {
	background-image: url("../images/box-bg.jpg");
	background-position:center center;
	background-repeat:no-repeat;
	background-size:cover;
	background-attachment:fixed;	
}
.box-style .main-content, .box-style footer, .box-style .parallel-post-style {
	background-color:#FFF;	
}
.box-style .parallel-post-style .grid-box .thumb img {
    max-height: 175px;
    width: auto;
}
.box-style .insta-gallery a {
    width: 75px;
}
/*TOP BAR*/

.topbar {
	background-color: #002f49;
	padding: 10px 0;
}

.topbar.white {
	background-color: #FFF;
	padding: 15px 0;
	border-bottom: 1px solid #F1f1f1;
}

.topbar.white ul li, .topbar.white ul li a {
	color: #444;
}

.topbar ul {
	padding-left: 0;
	margin-bottom: 0;
	float: right;
}

.topbar ul li {
	position: relative;
	display: inline-block;
	margin-left: 20px;
	color: #FFF;
}

.topbar ul li a {
	color: #FFF;
}

.topbar ul li a:hover {
	color: #f44a56;
}

.topbar ul li i {
	margin-right: 5px;
}

.topbar .social-media-icons-top {
	float: left;
}

.topbar .social-media-icons-top li {
	margin-right: 10px;
	margin-left: 0;
}

.topbar .social-media-icons-top li i {
	margin: 0;
}

.topbar ul.pull-right li {
	margin-right: 0;
	margin-left: 20px;
}

.topbar .news-ticker-block {
	padding: 0;
	background-color: #FFF;
}

.topbar .breakingNews> .bn-title, .topbar .breakingNews> ul> li, .topbar .breakingNews> ul {
	height: 15px;
	background-color: transparent;
}

.topbar .breakingNews {
	min-height: 15px;
	background-color: #444;
}

.topbar .breakingNews> ul> li> a {
	color: #fff;
	line-height: 24px;
}

.topbar .breakingNews> ul> li> a:hover {
	color: #FFF;
}

.topbar .bn-red> .bn-title> span {
	top: 5px;
	border-left-color: #FFF;
	border-width: 7px 0 7px 7px;
}

.topbar .breakingNews> .bn-navi {
	display: none;
}

.topbar .breakingNews> .bn-title> h2 {
	font-size: 16px;
	font-weight: 700;
	text-transform: uppercase;
	padding: 0;
	padding-right: 10px;
	background-color: transparent;
	line-height: 24px;
	color: #FFF;
	height: 30px;
}

.topbar.white .breakingNews {
	background-color: transparent;
}

.topbar.white .breakingNews> .bn-title> h2 {
	color: #242424;
}

.topbar.white .bn-red> .bn-title> span {
	border-left-color: #242424;
}

.topbar.white .breakingNews> ul> li> a {
	color: #242424;
}


/*MENU CUSTOMIZATIONS*/

#menu-1 {
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.megaMenu ul.menu-search-bar-desktop> li> a> i.fa, .megaMenu ul.menu-search-bar-mobile> li> a> i.fa {
	background: #242424;
}

.megaMenu .drop-down li:hover> a, .megaMenu .drop-down li:hover> a .description, .megaMenu ul.menu-mobile-trigger:hover i.fa, .megaMenu ul.menu-links> li:hover> a {
	color: #FFF;
}

.megaMenu .drop-down-medium .list-items a:hover, .megaMenu .drop-down-large .list-items a:hover, .megaMenu .drop-down-medium .list-items ol li:hover:after, .megaMenu .drop-down-large .list-items ol li:hover:after, .megaMenu .drop-down-medium .nav-tabs.vertical li.active a, .megaMenu .drop-down-medium .nav-tabs.vertical li:hover a, .megaMenu .drop-down-medium .nav-tabs.vertical li:active a, .megaMenu .drop-down-medium .nav-tabs.vertical li:focus a, .megaMenu .drop-down-large .nav-tabs.vertical li.active a, .megaMenu .drop-down-large .nav-tabs.vertical li:hover a, .megaMenu .drop-down-large .nav-tabs.vertical li:active a, .megaMenu .drop-down-large .nav-tabs.vertical li:focus a {
	color: #f44a56;
}

.megaMenu .drop-down-medium, .megaMenu .drop-down-large, .megaMenu .drop-down-medium.nav-tabs, .megaMenu .drop-down-large.nav-tabs {
	/* drop down with medium size */
	background: #f9f9f9;
	border: none;
	border-top: 2px solid #f44a56;
	box-shadow: 0 3px 4px 1px rgba(0, 0, 0, 0.2);
}

.megaMenu .drop-down-medium .tab-content.vertical, .megaMenu .drop-down-large.nav-tabs .tab-content.vertical, .megaMenu .drop-down-medium .nav-tabs.vertical a::after, .megaMenu .drop-down-large .nav-tabs.vertical a::after {
	background-color: #FFF;
	border-color: #ccc;
}

.megaMenu .drop-down-medium .nav-tabs.vertical li::after, .megaMenu .drop-down-medium .nav-tabs.vertical li::before, .megaMenu .drop-down-large .nav-tabs.vertical li::after, .megaMenu .drop-down-large .nav-tabs.vertical li::before {
	background: #ccc;
}

.megaMenu ul.menu-links> li> a {
	font-size: 14px;
}

#menu-1 .menu-links li a.home-icon {
	background-color: #242424;
	padding: 15px;
}

#menu-1 .menu-links li a.home-icon i {
	color: #FFF;
	font-size: 20px;
}

.megaMenu .drop-down {
	background-color: #FFF;
	box-shadow: 0 3px 4px 1px rgba(0, 0, 0, 0.2);
	border-top: 2px solid #f44a56;
}

#menu-1.megaMenu ul.menu-links> li {
	border-left: 1px solid #F2f2f2;
}

#menu-1.megaMenu ul.menu-links> li:first-child {
	border-left: none;
}

.megaMenu ul.menu-links> li:first-child {
	border-right: none;
}

.megaMenu ul.menu-links> li:last-child {
	border-right: none;
}

.megaMenu ul.menu-links> li:last-child a {
	border: none;
}

#menu-1.megaMenu .drop-down li> a::after {
	background-color: #f44a56;
}

.megaMenu .drop-down li {
	border-color: #F2F2F2;
}

.megaMenu .sticky-container {
	background-color: #FFF;
	box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.2));
}

.megaMenu.mega-menu-sticky-move .sticky-container {
	animation-name: sticky_expand;
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}


/*MENU CONTAINER SIZE*/

.menu-container-section {
	padding: 0;
	box-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.1);
	
}

#menu-2.megaMenu {
	padding: 15px 0;
}

#menu-2.megaMenu ul.menu-links> li> a {
	height: 2.75em;
}

#menu-2.megaMenu ul.menu-logo> li> a img {
	max-height: 2em;
	margin: 0;
}

#menu-2.megaMenu ul.menu-logo> li> a {
	height: auto;
	padding: 0;
}

#menu-2.megaMenu ul.menu-logo li:hover a::after {
	opacity: 0;
}

#menu-3.megaMenu ul.menu-logo> li> a img {
	max-height: 2em;
	margin: 0;
}

#menu-3.megaMenu ul.menu-logo li:hover a::after {
	opacity: 0;
}


/*MENU DARK*/

.menu-dark {
	background-color: #242424;
}

.menu-dark #menu-2.megaMenu {
	background-color: #242424;
	padding: 0;
	min-height: 3em;
}

.menu-dark #menu-2.megaMenu ul.menu-links> li> a {
	color: #FFF;
	height: 3em;
}

.menu-dark .megaMenu .sticky-container {
	background-color: #242424;
}


/*MENU CSS FOR BLOCKS AND LISTING*/

.megaMenu .category-image a img {
	border: none;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.megaMenu .category-image:hover img {
	-webkit-transition: all 0.6s ease 0s;
	-moz-transition: all 0.6s ease- 0s;
	-o-transition: all 0.6s ease 0s;
	transition: all 0.6s ease 0s;
}

.megaMenu .catname .btn, .post-tools .btn {
	border-radius: 4px;
	color: #fff;
	font-size: 12px;
	font-weight: 500;
	padding: 5px 10px;
	text-transform: capitalize;
	line-height: 1em;
	border: none;
	background-image: -moz-linear-gradient(center bottom, rgba(0, 0, 0, 0.17) 0%, rgba(255, 255, 255, 0.17) 100%);
}

.megaMenu .grid-1 {
	margin-bottom: 0;
	border: none;
}

.megaMenu .grid-1 .detail {
	background-color: #f9f9f9;
	padding: 15px;
}

.megaMenu .caption h5 {
	font-size: 18px;
	color: #444;
}

.megaMenu .caption h5 a:hover {
	color: #f44a56;
}

.megaMenu .hover-show-div .post-type {
	padding: 10px 12px;
}

.megaMenu .small-grid .col-md-6:last-child .small-post {
	margin-bottom: 0;
	border: none;
}


/*MENU CATEGORY COLOR CODE*/

.megaMenu .category-image .btn:hover {
	color: #FFF;
}

.megaMenu .category-image .btn-purple {
	background-color: #9c27b0;
	color: #FFF;
}

.megaMenu .category-image .btn-orange {
	background-color: #ff5722;
	color: #FFF;
}

.megaMenu .category-image .btn-yellow {
	background-color: #e28712;
	color: #FFF;
}

.megaMenu .category-image .btn-gray {
	background-color: #607d8b;
	color: #FFF;
}

.megaMenu .category-image .btn-green {
	background-color: #2ecc71;
	color: #FFF;
}

.megaMenu .category-image .btn-pink {
	background-color: #e91e63;
	color: #FFF;
}

.megaMenu .category-image .btn-red {
	background-color: #f44336;
	color: #FFF;
}

.megaMenu .category-image .btn-blue {
	background-color: #00aeef;
	color: #FFF;
}

.megaMenu .category-image .btn-black {
	background-color: #242424;
	color: #FFF;
}

.megaMenu .category-image .btn-maroon {
	background-color: #7b2143;
	color: #FFF;
}

.megaMenu .category-image .btn-lightblue {
	background-color: #8e93c4;
	color: #FFF;
}

.megaMenu .category-image .btn-dark-red {
	background-color: #d11141;
	color: #FFF;
}




.megaMenu ul.menu-search-bar-desktop .drop-down, .megaMenu ul.menu-search-bar-mobile .drop-down {
  background: #f9f9f9;

}
.megaMenu ul.menu-search-bar-desktop button[type="search"], .megaMenu ul.menu-search-bar-mobile button[type="search"], .megaMenu ul.menu-search-bar-desktop input[type="search"], .megaMenu ul.menu-search-bar-mobile input[type="search"] {
	background:#FFF;
	color:#242424;	
}
.megaMenu ul.menu-search-bar-desktop button[type="submit"]::after, .megaMenu ul.menu-search-bar-mobile button[type="submit"]::after, .megaMenu ul.menu-search-bar-desktop input[type="submit"]::after, .megaMenu ul.menu-search-bar-mobile input[type="submit"]::after {
	background:#FFF !important;
	border:1px solid #f44a56;
	color:#242424 !important;	
}
.megaMenu ul.menu-search-bar-desktop button[type="submit"], .megaMenu ul.menu-search-bar-mobile button[type="submit"], .megaMenu ul.menu-search-bar-desktop input[type="submit"], .megaMenu ul.menu-search-bar-mobile input[type="submit"] {
		background:#f44a56 !important;	
}
.megaMenu ul.menu-search-bar-desktop button[type="submit"]:hover, .megaMenu ul.menu-search-bar-mobile button[type="submit"]:hover, .megaMenu ul.menu-search-bar-desktop input[type="submit"]:hover, .megaMenu ul.menu-search-bar-mobile input[type="submit"]:hover {
	color:#f44a56;	
}

/*ZERO GRID*/

.zerogrid-section {
	padding: 0;
}

.zerogrid-section img {
	height: auto;
	overflow: hidden;
	transition: all 4.3s ease-out 0s;
	width: 100%;
}

.zerogrid-section .zerogrid {
	width: 100%;
}

.zerogrid-section .post-content {
	background-color: transparent;
	bottom: 0;
	left: 0;
	padding: 15px;
	position: absolute;
	width: 100%;
	z-index: 9;
}

.zerogrid-section .catname {
	position: absolute;
	top: -15px;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.zerogrid-section .catname a:hover {
	background-color:#444;
}

.zerogrid-section .btn {
	border-radius: 3px;
	color: #fff;
	font-size: 12px;
	font-weight: 500;
	padding: 5px;
	text-transform: capitalize;
}

.zerogrid-section .post-content h5 {
	color: #fff;
	font-size: 18px;
	font-weight: 500;
	text-transform: capitalize;
}

.zerogrid-section .post-content h5 a {
	color: #fff;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.zerogrid-section .post-content h5 a:hover {
	color: #f44a56;
}

.zerogrid-section .post-content .post-tools {
	list-style: outside none none;
	margin: 0;
	padding: 0;
}

.zerogrid-section .post-content .post-tools li::after {
	color: #e1e1e1;
	content: "/";
	padding: 0 10px;
}

.zerogrid-section .grid-item, .zerogrid-section .post-thumb {
	overflow: hidden;
	position: relative;
}

.zerogrid-section .zerogrid .wrap-col {
	margin: 0;
}

.zerogrid-section .post-content .post-tools li, .zerogrid-section .post-content .post-tools li a {
	color: #e1e1e1;
}

.zerogrid-section .post-content .post-tools li:hover, .zerogrid-section .post-content .post-tools li a:hover {
	color: #f44a56;
}

.zerogrid-section .post-thumb::after {
	background: rgba(0, 0, 0, 0.9);
	/* fallback for old browsers */
	background: -webkit-linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(0, 0, 0, 0.9));
	/* Chrome 10-25, Safari 5.1-6 */
	background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(0, 0, 0, 0.9));
	/* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+,Opera 12+,Safari 7+ */
	/*background: rgba(0, 0, 0, 0.6) none repeat scroll 0 0;*/
	content: "\a ";
	height: 100%;
	left: 0;
	opacity: 1;
	position: absolute;
	top: 0;
	transition: all 0.5s ease 0s;
	width: 100%;
}

.full-width-slider {
	background-color: #F1f1f1;
}

.full-width-slider .detail {
	padding: 15px;
	background-color: #FFF;
	height: 190px;
}

.full-width-slider .picture .category-image .catname {
	left: 20px;
	bottom: 20px;
	position: absolute;
}

.full-width-slider .caption h5 {
	margin-top: 0;
}

.full-width-slider .heading-read-more {
	float: right;
	margin-right: 80px;
	padding: 7px;
}

.full-width-slider .owl-nav {
	margin: 0;
	position: absolute;
	right: 15px;
	top: -72px;
}

.full-width-slider .owl-theme .owl-nav div.owl-next, .full-width-slider .owl-theme .owl-nav div.owl-prev {
	background-color: #444;
	border: medium none;
	border-radius: 5px;
	height: 30px;
	line-height: 24px;
	width: 30px;
}

.full-width-slider .owl-theme .owl-nav div.owl-prev::after, .full-width-slider .owl-theme .owl-nav div.owl-next::after {
	color: #fff;
	font-size: 20px;
}


/*VIDEO SECTION MAIN*/

#video-gallery {
	width: 100%;
}

.videoGallery .rsTmb {
	padding: 20px;
}

.videoGallery .rsThumbs .rsThumb {
	width: 220px;
	height: 80px;
	border-bottom: 1px solid #2E2E2E;
}

.videoGallery .rsThumbs {
	width: 220px;
	padding: 0;
}

.videoGallery .rsThumb:hover {
	background: #000;
}

.videoGallery .rsThumb.rsNavSelected {
	background-color: #02874A;
	border-bottom: -color #02874A;
}

.sampleBlock {
	left: 3%;
	top: 1%;
	width: 100%;
	max-width: 400px;
}

@media screen and (min-width: 0px) and (max-width: 500px) {
	.videoGallery .rsTmb {
		padding: 6px 8px;
	}
	.videoGallery .rsTmb h5 {
		font-size: 12px;
		line-height: 17px;
	}
	.videoGallery .rsThumbs.rsThumbsVer {
		width: 100px;
		padding: 0;
	}
	.videoGallery .rsThumbs .rsThumb {
		width: 100px;
		height: 47px;
	}
	.videoGallery .rsTmb span {
		display: none;
	}
	.videoGallery .rsOverflow, .royalSlider.videoGallery {
		height: 300px !important;
	}
	.sampleBlock {
		font-size: 14px;
	}
}


/*PARALLEL POST STYLE*/

.parallel-post-style {
	padding-bottom:0px;
	background-color: #f9f9f9;
}

.parallel-post-style .grid-box {
	position: relative;
	overflow: hidden;
	margin-bottom: 30px;
	background-color: #FCFCFC;
}

.parallel-post-style .grid-box ul {
	padding-left: 0px;
	font-size: 14px;
	text-transform: capitalize;
	overflow: hidden;
	position: relative;
	border-radius: 0;
	box-shadow: none;
	list-style: none;
	margin-bottom: 0;
	height: 118px;
}

.parallel-post-style .grid-box .thumb img {
	 /* width: auto; */
	height: 119px;
    object-fit: cover;
}

.parallel-post-style .grid-box .post-content {
	padding: 10px 0;
	position: relative;
	overflow: hidden;
}

.parallel-post-style .grid-box .post-content h5 {
	font-size: 16px;
	margin-top: 0;
	text-transform: capitalize;
	font-weight:500;
	font-family: system-ui;
}

.parallel-post-style .grid-box .post-content .post-tools {
	margin-bottom: 10px;
}

.parallel-post-style .grid-box p {
	margin-bottom: 0;
}

.parallel-post-style .grid-box .heading-read-more {
	float: none;
	padding: 0;
}


/*ARCHIVE POSTS*/

.new-archives {
	background-color: #F1F1F1;
	padding-bottom:60px;
}

.new-archives .section .nav-tabs.nav-justified> li> a {
	text-transform: uppercase;
	font-weight: 500;
}

.new-archives .nav-tabs.nav-justified> li> a {
	font-size: 14px;
}

.new-archives .post-content h3 {
	margin-bottom: 0;
}

.new-archives .small-grid .small-post {
	border-bottom: 1px solid #CCC;
}

.new-archives .latest-news-grid .detail {
	background-color: #FFF;
	padding: 15px;
}

.new-archives .small-grid {
	margin-bottom: 0;
}


/*CATEGORY SIDEBAR*/

.category-sidebar {
	background-color: #242424;
}

.category-sidebar .ad-div {
	padding-top: 10px;
	margin-bottom: 30px;
}

.category-widget h4 {
	background-color: #f44a56;
	color: #FFF;
	text-transform: capitalize;
	font-weight: 700;
	padding: 10px;
	font-size: 16px;
	margin-top: 0;
}

.category-widget .category-widget-detail {
	padding: 15px;
}

.category-widget .category-widget-detail ul {
	padding-left: 0;
	list-style: none;
	margin: 0;
}

.category-widget .category-widget-detail ul li {
	padding: 8px 0;
	border-bottom: 1px solid #999;
}

.category-widget .category-widget-detail ul li:last-child {
	border-bottom: none;
}

.category-widget .category-widget-detail ul li a {
	color: #FFF;
	text-transform: capitalize;
	font-size: 14px;
	display: block;
}

.sidebar-middle .category-widget .category-widget-detail ul li a:hover {
	color: #f44a56;
}

.category-widget .category-widget-detail ul li a i {
	margin-right: 10px;
	font-size: 14px;
}

.category-widget .category-widget-detail.category-widget-block ul li a i {
	display: block;
	font-size: 30px;
	text-align: center;
	margin-bottom: 10px;
	margin-right: 0;
}

.category-widget .category-widget-detail.category-widget-block ul li {
	text-align: center;
	padding: 15px 0;
	border-bottom: none;
}

.category-widget .category-widget-block ul li:nth-child(even) {
	background-color: #444;
}

.category-widget .category-widget-block ul li a {
	font-size: 16px;
	text-align: center;
	text-transform: capitalize;
}

.sidebar-middle {
	background-color: transparent;
	padding: 0 15px;
	border-right: 1px solid #F5f5f5;
	border-left: 1px solid #F5f5f5;
}

.sidebar-middle .category-widget .category-widget-detail ul li a {
	color: #444;
}

.sidebar-middle .category-widget .category-widget-detail {
	padding: 0;
}

.category-widget .category-widget-detail ul li {
	border-bottom: 1px solid #f5f5f5
}

.category-widget .detail .caption h5 {
	font-size: 16px;
	text-align: center;
}

.category-widget .detail ul {
	margin-bottom: 0;
	text-align: center;
}

.category-widget .detail ul li {
	float: none;
	display: inline;
}


/*VIDEO SECTION*/

.video-section {
	background-color: #F1F1F1;
}

.video-section iframe {
	border: 0 none;
	min-height: 425px;
	width: 100%;
}

.video-section .rsDefault .rsThumb.rsNavSelected {
	background-color: #f44a56;
}

.bx-controls-direction a {
	display: none;
}

.scrollup {
	background-color: #f44a56;
	border-radius: 5px;
	bottom: 11px;
	box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.6);
	display: none;
	height: 40px;
	opacity: 0.5;
	padding: 5px;
	position: fixed;
	right: 9px;
	width: 40px;
}

.scrollup:hover {
	opacity: 1;
}

.scrollup:focus {
	text-decoration: none;
}

.scrollup i {
	color: #fff;
	display: block;
	line-height: 30px;
	text-align: center;
}

.info {
	color: #aaaaaa;
	padding-right: 10px;
}

.info .date, .info .comments-count, .info .likes {
	padding-right: 20px;
}

.three-grid {
	border-bottom: none !important;
}

.three-grid .info span {
	font-size: 12px;
}

.custom {
	padding-left: 25px;
}

.custom-box {
	width: 32%;
}

.modal-header {
	border-bottom: 1px solid #e5e5e5;
	min-height: 16.43px;
	padding: 15px;
}

.modal-content {
	border-radius: 0;
}

.modal-body {
	overflow: hidden;
}

.custom small {
	font-weight: bold;
	color: #000;
}

.author {
	padding-top: 15px;
}

.author .pic .name {
	color: #aaaaaa;
	padding-left: 12px;
}

.post-entry blockquote {
	background-color: #f9f9f9;
	font-size: 16px;
	line-height: 26px;
	margin: 30px 80px;
	padding: 10px 20px;
	position: relative;
}

.post-entry blockquote::before {
	color: #f44a56;
	content: "";
	display: block;
	font-family: "FontAwesome";
	font-size: 34px;
	left: -70px;
	position: absolute;
	top: 15px;
}

.post-entry .post-tools li {
	font-size: 16px;
	line-height: 50px;
}

.post-entry h3 {
	margin-top: 0;
	text-transform: capitalize;
}

.post-entry h2 {
	margin-top: 0;
	text-transform: capitalize;
	font-weight: 500;
}

.post-entry .post-image {
	margin: 20px 0;
	position: relative;
	overflow: hidden;
}

.post-entry ul.post-entry-poits {
	list-style: none;
	margin-bottom: 30px;
}

.post-entry ul.post-entry-poits li {
	padding: 6px 0;
	font-size: 16px;
	color: #777;
}

.post-entry ul.post-entry-poits li i {
	font-size: 16px;
	color: #f44a56;
	margin-right: 10px;
}

.post-entry img.pull-left {
	margin-bottom: 10px;
	margin-right: 20px;
}

.post-entry .picture {
	margin-bottom: 30px;
	position:relative;
}
.post-entry .picture.quote-post h2 {
	position:absolute;
	top:10px;
	padding:30px 70px;
	color:#FFF;
}
.post-entry .picture.quote-post::before {
    background: rgba(0, 0, 0, 0.7) none repeat scroll 0 0;
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}
.post-entry .picture.quote-post h2::before {
	color: #FFF;
	content: "";
	display: block;
	font-family: "FontAwesome";
	font-size: 20px;
	left: 25px;
	position: absolute;
}
.post-entry .catname {
	margin-bottom: 10px;
}

.post-entry .social-share {
	position: relative;
	overflow: hidden;
	margin-bottom: 20px;
	padding-left: 0;
}

.post-entry .social-share li {
	display: inline-block;
	margin: 0 5px 5px 0;
	background-color: #244872;
	border-radius: 4px;
	box-shadow: none;
	background-image: -moz-linear-gradient(center bottom, rgba(0, 0, 0, 0.17) 0%, rgba(255, 255, 255, 0.17) 100%);
}

.post-entry .social-share li i {
	margin-right: 5px;
}

.post-entry .social-share li a {
	color: #FFF;
	display: block;
	padding: 5px 15px;
}

.post-entry .social-share li.dribbble {
	background-color: #f46899;
}

.post-entry .social-share li.vimeo {
	background-color: #00B3EC;
}

.post-entry .social-share li.stumbleupon {
	background-color: #eb4924;
}

.post-entry .social-share li.reddit {
	background-color: #5f99cf;
}

.post-entry .social-share li.facebook {
	background-color: #3c599f;
}

.post-entry .social-share li.rss {
	background-color: #f26522;
}

.post-entry .social-share li.lastfm {
	background-color: #d51007;
}

.post-entry .social-share li.flickr {
	background-color: #ff0084;
}

.post-entry .social-share li.instagram {
	background-color: #685243;
}

.post-entry .social-share li.google {
	background-color: #cf3d2e;
}

.post-entry .social-share li.linkedin {
	background-color: #0085ae;
}

.post-entry .social-share li.pinterest {
	background-color: #cc2127;
}

.post-entry .social-share li.twitter {
	background-color: #32ccfe;
}

.post-entry .post-share {
	color: #ccc;
	float: right;
	line-height: 25px !important;
	text-align: center;
	text-transform: capitalize;
}

.post-entry .post-share strong {
	font-size: 30px;
	display: block;
	color: #CCC;
}

.post-entry .post-tools li.post-share::after {
	color: #444;
	content: normal;
	padding: 0;
}

.post-entry .tag-list {
	padding: 0;
}

.post-entry .tag-list a {
	border-radius: 4px;
	font-size: 12px;
	padding: 0 10px;
	box-shadow: none;
}

.videoWrapper {
	position: relative;
	padding-bottom: 56.25%;
	/* 16:9 */
	padding-top: 25px;
	height: 0;
	margin-bottom: 20px
}

.videoWrapper iframe {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.post-entry iframe {
	width: 100%;
	height: 100%;
}

.author-box {
	padding: 15px;
	background-color: #f9f9f9;
	position: relative;
	overflow: hidden;
	margin: 50px 0;
}

.author-box h3 {
	font-size: 20px;
	margin-top:0;
	text-transform:capitalize;
}

.author-box p {
	margin-bottom: 0;
	color: #777;
    font-size: 16px;
    line-height: 30px;
}

.author-box ul {
	padding: 0;
}

.author-box .social-media-icons {
	margin: 0;
}


/*SOCIAL SHARE 2*/

.post-entry .social-share.social-share-style-2 li {
	width: 100%;
	text-align: center;
	margin: 0;
	margin-bottom: 5px;
}

.post-entry .social-share.social-share-style-2 li a {
	padding: 10px 0;
}

.post-entry .social-share.social-share-style-2 li.post-share {
	background-color: transparent;
	background: none;
	border-bottom: 2px solid #f44a56;
	border-radius: 0;
	margin-bottom: 30px;
	padding-bottom: 10px;
	color: #999;
}

.share-ping {
	border-color: #f44a56 transparent transparent;
	border-style: solid;
	border-width: 16px;
	height: 0;
	left: 20px;
	position: absolute;
	top: 65px;
	width: 0;
}

.post-tools.nolineheight li {
	line-height: 1.2em;
}

.post-entry .social-share.social-share-style-2 li.post-share strong {
	color: #f44a56;
	font-size: 38px;
	line-height: 30px;
}

.next-n-prev {
	margin: 30px 0;
	position: relative;
	overflow: hidden;
	padding: 20px 0;
	border-top: 1px solid #f1f1f1;
	border-bottom: 1px solid #f1f1f1;
}

.next-n-prev .pre-post {
	border-right: 1px solid #F1f1f1;
	text-align: left;
	padding-right: 15px;
}

.next-n-prev .next-post {
	text-align: right;
	padding-left: 15px;
}

.next-n-prev span {
	margin-right: 10px;
	font-size: 18px;
}

.next-n-prev .next-post i {
	margin-left: 10px;
	color: #f44a56;
}

.next-n-prev .pre-post i {
	margin-right: 10px;
	color: #f44a56;
}

.next-n-prev a {
	color: #f44a56;
}

.post-entry .related-posts .picture {
	margin-bottom: 0;
}

.post-entry .related-posts .post-tools li {
	font-size: 12px;
	line-height: 24px;
}

.post-entry .related-posts .caption h5 {
	font-size: 16px;
}

.post-entry .related-posts {
	margin-bottom: 20px;
}

.post-entry .related-posts .grid-1 {
	margin-bottom: 30px;
	border: none;
}

.post-entry .related-posts .grid-1 .detail {
	padding: 10px;
	background-color: #F9F9f9;
}


/* HEADER*/
.logo {
	margin-top:20px;	
}
.logo-row {
	padding: 25px 0;
}

.logo-center .logo a {
	display: table;
	margin: 0 auto;
}

/*.dark-logo-row {
	border-bottom: 1px solid #444;
	background-color: #242424;
}*/
.dark-logo-row {
	background: rgba(0, 0, 0, 0) url("../images/fullslide7.jpg") no-repeat scroll center center / cover ;
	position:relative;
}
.dark-logo-row::after {
	background: rgba(0, 0, 0, 0.9);
	content: "\a ";
	height: 100%;
	left: 0;
	opacity: 1;
	position: absolute;
	top: 0;
	transition: all 0.5s ease 0s;
	width: 100%;
}
.dark-logo-row .logo, .dark-logo-row .advertisement {
	position:relative;
	z-index:9;	
}

.dark-logo-row  .advertisement {
	float:right;	
}
.navbar-fixed-top {
	position: fixed !important;
}

.sticky {
	position: fixed;
	width: 100%;
	left: 0;
	top: 0;
	z-index: 100;
	border-top: 0;
	background: #fff;
}


/*FEATURED SLIDER CSS */

.featured-news-slider .item, .featured-news-slider .post-thumb {
	position: relative;
	overflow: hidden;
}

.featured-news-slider .item, .featured-news-slider .post-thumb a {
	display: block;
	z-index: 9;
}

.featured-news-slider .post-content {
	position: absolute;
	left: 0px;
	bottom: 0px;
	background-color: transparent;
	width: 100%;
	padding: 15px 20px 15px;
	z-index: 9;
}

.featured-news-slider .post-content h5 {
	font-size: 18px;
	color: #fff;
	font-weight: 500;
}

.featured-news-slider .post-content h5 a {
	color: #fff;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
	font-family: system-ui;
}

.featured-news-slider .post-content h5 a:hover {
	color: #f44a56;
}

.featured-news-slider .post-content .post-tools {
	margin: 0px;
	padding: 0px;
	list-style: none;
}

.featured-news-slider .post-content .post-tools li::after {
	color: #e1e1e1;
	content: "/";
	padding: 0 10px;
}

.featured-news-slider .post-content .post-tools li {
	float: left;
	color: #e1e1e1;
	font-size: 10px;
	text-transform: uppercase;

}

.featured-news-slider .post-content .post-tools li a {
	color: #e1e1e1;
}
.featured-news-slider .post-content .post-tools li strong {
    color: #e1e1e1;
}

.featured-news-slider .btn {
	border-radius: 3px;
	color: #fff;
	font-size: 12px;
	font-weight: 500;
	padding: 5px;
	text-transform: capitalize;
}
.featured-news-slider .catname {
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}
.featured-news-slider .catname:hover .btn {
	background-color:#242424;
}

.featured-slider img {
	width: 100%;
	height: auto;
	transition: all 4.3s ease-out 0s;
	overflow: hidden;
}

.post-thumb:after {
	content: '\A';
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: rgba(0, 0, 0, 0.6);
	/* fallback for old browsers */
	background: -webkit-linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(0, 0, 0, 0.9));
	/* Chrome 10-25, Safari 5.1-6 */
	background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(0, 0, 0, 1));
	/* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+,Opera 12+,Safari 7+ */
	opacity: 1;
	-webkit-transition: all 0.5s ease 0s;
	-moz-transition: all 0.5s ease- 0s;
	-o-transition: all 0.5s ease 0s;
	transition: all 0.5s ease 0s;
}


/*.post-thumb:hover:after {
	opacity: 0;
}*/

.hover-show-div {
	position: absolute;
	top: 30px;
	right: 20px;
}

.hover-show-div .post-type {
	background-color: rgba(244, 74, 86, 0.7);
	border-radius: 4px;
	overflow: hidden;
	padding: 10px 12px;
	position: relative;
	font-size: 20px;
}

.hover-show-div .post-type:hover {
	background-color: rgba(244, 74, 86, 1);
}

.hover-show-div .post-type i {
	color: #FFF;
}

.main-content .socialHolder .dropdown-menu {
	top: 41 !important;
	left: 50px;
}

.main-content .socialHolder ul {
	top: 40px !important;
}

.socialHolder ul li a {
	border-radius: 0;
}

.socialShare> a {
	padding: 10px 19px;
	background-color: rgba(106, 202, 195, 0.7);
}

.socialHolder .dropdown-menu {
	background-color: transparent;
	border: medium none;
	margin: 0;
	min-width: 50px !important;
	padding: 0;
}

.socialHolder .btn-info {
	border: none;
	margin: 0px;
	border-radius: 0;
}

.main-content .hover-show-div {
	position: absolute;
	right: 20px;
	top: 30px;
}

.mobile-social-share ul {
	float: right;
	list-style: none outside none;
	margin: 0;
	min-width: 61px;
	padding: 0;
}

.share {
	min-width: 17px;
}

.mobile-social-share li {
	display: block;
	font-size: 18px;
	list-style: none outside none;
	margin-bottom: 3px;
	margin-left: 4px;
	margin-top: 3px;
}

.btn-share {
	background-color: #BEBEBE;
	border-color: #CCCCCC;
	color: #333333;
}

.btn-twitter {
	background-color: #3399CC !important;
	width: 50px;
	color: #FFFFFF!important;
}

.btn-facebook {
	background-color: #3D5B96 !important;
	width: 50px;
	color: #FFFFFF!important;
}

.btn-facebook {
	background-color: #3D5B96 !important;
	width: 50px;
	color: #FFFFFF!important;
}

.btn-google {
	background-color: #DD3F34 !important;
	width: 50px;
	color: #FFFFFF!important;
}

.btn-linkedin {
	background-color: #1884BB !important;
	width: 50px;
	color: #FFFFFF!important;
}

.btn-pinterest {
	background-color: #CC1E2D !important;
	width: 50px;
	color: #FFFFFF!important;
}

.btn-mail {
	background-color: #FFC90E !important;
	width: 50px;
	color: #FFFFFF!important;
}

.caret {
	border-left: 4px solid rgba(0, 0, 0, 0);
	border-right: 4px solid rgba(0, 0, 0, 0);
	border-top: 4px solid;
	display: inline-block;
	height: 0;
	margin-left: 2px;
	vertical-align: middle;
	width: 0;
}

.main-content {
	width: 100%;
	/*background: url(../images/content_background.png);*/
}

.section-title {
	width: 100%;
	float: left;
	clear: both;
	margin-bottom: 25px
}

.featured-slider-2.owl-carousel .owl-item img {
	height: auto;
	overflow: hidden;
	width: auto;
	transition: all 4.3s ease-out 0s;
}


/*FULL WIDTH SLIDER MAIN*/

.full-with-slider-section {
	position: relative;
	overflow: hidden;
	background-color: #242424;
	padding: 0;
}

.full-with-slider-section .post-thumb::after {
	background: none;
	content: "";
}

.full-with-slider-section .post-content {
	background-color: rgba(0, 0, 0, 0.6);
	bottom: 0;
	left: 30%;
	padding: 20px;
	position: absolute;
	width: 370px;
	z-index: 9;
	text-align: center;
	display: none;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.full-with-slider-section .item, .full-with-slider-section .post-thumb {
	position: relative;
	overflow: hidden;
}

.full-with-slider-section .post-content h5 {
	font-size: 18px;
	color: #fff;
	font-weight: 500;
}

.full-with-slider-section .post-content h5 a {
	color: #fff;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.full-with-slider-section .post-content h5 a:hover {
	color: #f44a56;
}

.full-with-slider-section .post-content .post-tools {
	padding: 0px;
	list-style: none;
	margin: 0 auto;
	display: table;
}

.full-with-slider-section .post-content .post-tools li {
	float: left;
	color: #FFF;
	font-size: 14px;
	text-transform: capitalize;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.full-with-slider-section .post-content .post-tools li::after {
	color: #e1e1e1;
	content: "/";
	padding: 0 10px;
}
.full-with-slider-section .post-content .post-tools li, .full-with-slider-section .post-content .post-tools li a, .full-with-slider-section .post-content .post-tools li a strong {
	color: #e1e1e1;
}

.full-with-slider-section .post-content .post-tools li:last-child::after {
	display: none;
}

.full-with-slider-section .post-content .post-tools li:hover {
	color: #f44a56;
}

.full-with-slider-section .post-content .post-tools li a:hover {
	color: #f44a56;
}

.full-with-slider-section .post-content .post-tools li a {
	color: #FFF;
}

.full-with-slider-section .post-content .post-tools li:last-child {
	margin: 0;
}

.full-with-slider-section .btn {
	border-radius: 3px;
	color: #fff;
	font-size: 12px;
	font-weight: 500;
	padding: 5px;
	text-transform: capitalize;
	line-height: 1em;
}

.full-with-slider-section img {
	width: 100%;
	height: auto;
	transition: all 4.3s ease-out 0s;
	overflow: hidden;
}

.full-with-slider-section .owl-carousel .owl-stage-outer {
	overflow: inherit;
}

.full-with-slider-section .owl-carousel .owl-item {
	padding: 60px 60px 0 60px;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.full-with-slider-section .owl-carousel .owl-item.active.center {
	padding: 0;
	margin: 25px 0;
}

.full-with-slider-section .owl-carousel .owl-item.active.center .post-content {
	display: block;
}

.full-with-slider-section .owl-theme .owl-nav {
	margin-top: 0;
}

.full-with-slider-section .owl-theme .owl-nav .owl-next {
	right: -30px;
	position: absolute;
	top: 45%;
	height: 50px;
	width: 50px;
	padding: 10px;
	line-height: 28px;
}

.full-with-slider-section .owl-theme .owl-nav .owl-prev {
	left: -30px;
	position: absolute;
	top: 45%;
	height: 50px;
	width: 50px;
	padding: 10px;
	line-height: 28px;
}


/*FULL WIDTH STYLE 2*/

.full-width-style-2 {
	background-color: transparent;
}

.full-width-style-2 .owl-carousel .owl-item {
	padding: 0;
}

.full-width-style-2 .owl-carousel .owl-item.active.center {
	margin: 0;
}


/*OWL SLIDER CUSTOM CSS*/

.owl-theme .owl-nav div.owl-prev, .owl-theme .owl-nav div.owl-next {
	background: transparent;
	border-radius: 3px;
	width: 40px;
	height: 40px;
	text-align: center;
	float: left;
	opacity: 1;
	font-size: 0;
}

.owl-theme .owl-nav div.owl-next {
	float: right;
}

.owl-theme .owl-nav div.owl-prev:after, .owl-theme .owl-nav div.owl-next:after {
	content: "\f104";
	font-family: FontAwesome;
	font-size: 30px;
	color: #FFF;
}

.owl-theme .owl-nav div.owl-next:after {
	content: "\f105";
}

.owl-theme .owl-nav div {
	padding: 3px 0px;
	margin: 0px;
}

.owl-theme .owl-nav div.owl-next, .owl-theme .owl-nav div.owl-prev {
	border: none;
	color: #999;
	line-height: 25px;
	border-radius: 0;
	width: 35px;
	height: 35px;
	background-color: #242424;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.owl-theme .owl-nav div.owl-next:hover, .owl-theme .owl-nav div.owl-prev:hover {
	background-color: #f44a56;
	color: #FFF;
}


/*CSS PREE LOADR*/

#preloader {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #2c3e50;
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: cover;
	height: 100%;
	z-index: 99;
}

#status {
	width: 50px;
	height: 30px;
	position: fixed;
	left: 50%;
	top: 50%;
	margin: -25px 0 0 -15px;
}

.spinner {
	margin: 0px auto;
	width: 50px;
	height: 30px;
	text-align: center;
	font-size: 10px;
}

.spinner> div {
	background-color: #fff;
	height: 100%;
	width: 6px;
	display: inline-block;
	-webkit-animation: stretchdelay 1.2s infinite ease-in-out;
	animation: stretchdelay 1.2s infinite ease-in-out;
}

.spinner .rect2 {
	-webkit-animation-delay: -1.1s;
	animation-delay: -1.1s;
}

.spinner .rect3 {
	-webkit-animation-delay: -1.0s;
	animation-delay: -1.0s;
}

.spinner .rect4 {
	-webkit-animation-delay: -0.9s;
	animation-delay: -0.9s;
}

.spinner .rect5 {
	-webkit-animation-delay: -0.8s;
	animation-delay: -0.8s;
}

@-webkit-keyframes stretchdelay {
	0%, 40%, 100% {
		-webkit-transform: scaleY(0.4);
	}
	20% {
		-webkit-transform: scaleY(1);
	}
}

@keyframes stretchdelay {
	0%, 40%, 100% {
		transform: scaleY(0.4);
		-webkit-transform: scaleY(0.4);
	}
	20% {
		transform: scaleY(1);
		-webkit-transform: scaleY(1);
	}
}


/* COMMENT SECTION */

.post-entry .comment-list {
	list-style: outside none none;
	margin: 0;
	padding: 0;
}

.post-entry .comment li {
	padding-right: 10px;
}

.post-entry .comment li:last-child {
	border-bottom: 0;
}

.post-entry .comment-info {
	background-color: #fcfcfc;
	display: block;
	margin-bottom: 30px;
	overflow: hidden;
	padding: 15px;
	position: relative;
}

.post-entry .comment-info img {
	margin-right: 10px;
	margin-bottom: 0;
}

.post-entry .comment-info p {
	margin-bottom: 0;
}

.post-entry .comment-info img {
	max-width: 90px;
}

.post-entry .comment-info .author-title {
	color: #444;
	line-height: 16px;
	margin-bottom: 10px;
}
.post-entry .comment-info .author-desc {
    margin-left: 115px;
}
.post-entry .author-title strong {
	color: #444;
	font-size: 16px;
	text-transform: capitalize;
}

.post-entry .author-title .list-inline li {
	border-right: 1px solid #999;
	padding-right: 10px;
}

.post-entry .author-title .list-inline li:last-child {
	border: 0;
	padding-right: 0;
}

.post-entry .author-title .list-inline li a {
	font-size: 16px;
	color: #777;
}

.post-entry .author-title .list-inline li a:hover {
	color: #f44a56;
}

.post-entry .comment-date {
	border-left: 1px solid #777;
	color: #777;
	font-size: 12px;
	margin-left: 17px;
	padding-left: 17px;
}

.post-entry .comment-list .children {
	list-style: outside none none;
	padding-left: 67px;
}

.post-entry .commentform {}

.post-entry .commentform .form-group {
	margin-bottom: 20px;
}

.post-entry .commentform .form-group input {
	height: 40px;
	border-radius: 0;
	box-shadow: none;
	border: none;
	border-radius: 4px;
	background-color: #F9f9f9;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.post-entry .commentform .form-group textarea {
	border-radius: 0;
	box-shadow: none;
	border: none;
	border-radius: 4px;
	background-color: #F9f9f9;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.post-entry .commentform .form-group input:focus, .post-entry .commentform .form-group textarea:focus {
	background-color: #F9f9f9;
	box-shadow: none;
}


/*FULL WIDTH IMAGE SINGLE POST*/

.full-width-post-section {
	padding: 0;
}

.full-width-post-section img {
	width: 100%;
}

.full-width-post-section .post-content {
	background-color: rgba(0, 0, 0, 0.8);
	bottom: 0;
	left: 30%;
	padding: 20px;
	position: absolute;
	width: 500px;
}

.full-width-post-section .post-content h5 {
	font-size: 26px;
	color: #FFF;
	text-transform: capitalize;
}

.full-width-post-section .post-content .post-tools a, .full-width-post-section .post-content .post-tools li, .full-width-post-section .post-tools li::after {
	color: #FFF;
}


/*FULL WIDTH SLIDER*/

.post-content h3 {
	padding: 0px;
	font-weight: normal;
	margin-bottom: 10px;
	margin-top: 0px;
	line-height: 20px;
}

.post-content h3 a {
	text-decoration: none;
	color: #222;
}

.thumb iframe {
	width: 100%;
	border: 0px;
	min-height: 350px;
}

.fullwidth-news-post-excerpt .owl-theme .owl-nav {
	position: absolute;
	top: 35%;
	z-index: 999;
	left: 0;
	width: 100%;
}

.thumb img {
	width: 100%;
	height: auto;
}


/*  PAGINATION */

.pagination-holder {
	clear: both;
	float: left;
	width: 100%;
	margin-bottom: 25px;
}

.pagination-holder nav {
	background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
	box-shadow: none;
	height: inherit;
	text-align: center;
}

.pagination-holder nav ul {
	margin: 0;
	padding: 0;
}

.pagination-holder nav ul li {
	display: inline-block;
	margin-left: 5px;
}

.paginationc{
text-align: center;
    font-weight: 500;
    color: #0021c1;
    font-family: ui-sans-serif;
    font-size: 19px;
}


.pagination-holder .pagination> li> a, .pagination-holder .pagination> li> span, .pagination-holder .pagination> li:first-child> a, .pagination-holder .pagination> li:first-child> span, .pagination-holder .pagination> li:last-child> a, .pagination-holder .pagination> li:last-child> span {
	border-radius: 4px;
	font-size: 15px;
	height: 40px;
	padding: 0;
	line-height: 40px;
	width: 78px;
	color: #444;
}

.pagination-holder .pagination> .active> a, .pagination-holder .pagination> .active> span, .pagination-holder .pagination> .active> a:hover, .pagination-holder .pagination> .active> span:hover, .pagination-holder .pagination> .active> a:focus, .pagination-holder .pagination> .active> span:focus {
	background-color: #f44a56;
	border-color: #f44a56;
	color: #FFF;
}

.pagination-holder .pagination> li> a:hover, .pagination-holder .pagination> li> span:hover, .pagination-holder .pagination> li> a:focus, .pagination-holder .pagination> li> span:focus {
	background-color: #f44a56;
	border-color: #f44a56;
	color: #FFF;
}


/* AUTHOR LIST */

.authors-grid {
	width: 100%;
	padding: 0px;
	margin: 0px;
}

.authors-grid li {
	list-style: none;
}

.authors-grid .author-box {
	width: 100%;
	float: left;
	clear: both;
	border-radius: 2px;
	overflow: hidden;
	margin: 15px 0;
	text-align: center;
}

.authors-grid .author-box h3 {
	margin: 15px 0 12px;
	font-size: 20px;
	font-weight: 500;
	text-transform: uppercase;
	width: 100%;
	float: left;
}

.authors-grid .author-box p {
	color: #FFF;
	margin: 0px;
}

.auth-arti-no {
	float: left;
}

.auth-arti-no a {
	background-color: #f44a56;
	color: #fff;
	font-size: 20px;
	font-weight: bold;
	padding: 15px;
}

.auth-arti-name {
	float: left;
}


/*ARTICLE CAPTION AND RELATED CSS */

.picture .category-image {
	position: relative;
	overflow: hidden;
}

.picture .category-image .catname {
	position: absolute;
	bottom: 20px;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.picture .category-image:hover img {
	transform: scale(1.1);
}

.category-image a img {
	 height: 100%;
	overflow: hidden;
	transition: all 0.9s ease-out 0s;
	width: 100%;
	object-fit: cover;
}

.picture .overlay-category {
	background-color: #fff;
	bottom: 0;
	font-size: 12px;
	font-weight: 400;
	left: 10px;
	letter-spacing: 2px;
	margin-bottom: 0;
	margin-top: 0;
	padding: 6px 10px;
	position: absolute;
	z-index: 100;
}

.grid-1 .detail p {
	color: #777;
	font-size: 14px;
	line-height: 1.8em;
	margin-bottom: 0;
	margin-top: 10px;
}

.post-detail .detail {
	padding-top: 20px;
}

.detail .info {
	color: #aaaaaa;
}

.detail .info .likes {
	padding-right: 16px;
}

.detail .info i {
	padding-right: 2px;
}

.caption {
	position: relative;
	overflow: hidden;
}

.caption a {
	color: #242424;
	font-family: system-ui;
}

.caption h5 {
	color: #242424;
	font-size: 18px;
	font-weight: 500;
	margin-top: 0;
	text-transform: capitalize;
}

.caption a:hover {
	color: #f44a56;
}

.small-caption a {
	color: #444;
}

.small-caption {
	color: #000;
	font-size: 20px;
	font-weight: 400;
	padding-top: 10px;
	padding-bottom: 10px;
}


/* GRID CSS */

.grid-1 {
	border-bottom: 1px solid #f1f1f1;
	border-radius: 0;
	margin-bottom: 20px;
	width: 100%;
}

.grid-1 .detail {
	overflow: hidden;
	padding: 15px 0;
	position: relative;
}

.category-image img:hover {
	transform: scale(1.6);
	overflow: hidden;
	width: 100%;
}

.grey-inner-values span {
	margin-right: 10px;
}

.grid-1 .catname {
	bottom: 0px;
	left: 20px;
	position: absolute;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.grid-1 .thumb .catname {
	bottom: 20px;
	left: 22px;
	position: absolute;
	font-style: normal;
}

.post-content h3 a:hover {
	color: #444 !important;
}

.grid-1 .catname a {
	color: #fff;
}
.listing {
	margin-bottom:0;	
}
.listing .grid-1 {
	margin-bottom: 30px;
	position: relative;
	overflow: hidden;
	border: none;
}
.listing .grid-1:last-child {
	margin-bottom:0;	
}

.grid-1 ul {
	padding-left: 0px;
	color: #fff;
	font-size: 14px;
	text-transform: capitalize;
	overflow: hidden;
	position: relative;
	border-radius: 0;
	box-shadow: none;
	list-style: none;
	margin-bottom: 0;
}

.grid-1 .catname span {
	background-color: #f44a56;
	color: #fff;
	display: block;
	font-size: 11px;
	padding: 5px;
	text-align: center;
	text-transform: uppercase;
}

.grid-1 .desc h5 {
	color: #242424;
	font-size: 20px;
	font-weight: 500;
	margin-top: 0;
	font-style: normal;
}

.grid-1 .desc h5 a {
	color: #242424;
}

.grid-1 .desc h5 a:hover {
	color: #f44a56;
}

.grid-1 .desc p {
	color: #444;
	margin-bottom: 0;
	font-size: 14px;
	line-height: 2.2em;
	font-style: normal;
	text-transform: none;
	padding-top: 10px;
}

.thumb {
	transition: all 0.6s ease-in-out 0s;
}

.thumb {
	background: #fff none repeat scroll 0 0;
	transition: all 0.6s ease-in-out 0s;
}

.thumb:hover img {
	/*opacity: 0.7;*/
	transform: scale(1.1);
}

.thumb img, .gallery-hover, .gallery-grid img, .news-grid-style-1 .news-post-excerpt h3 a, .catname-btn, .news-post-excerpt img, .footer-social li a, .thumb, .pro-content, .product-grid ul.grid> li, .cart-button, .post-content h3 a, .news-grid-style-3 .grid-holder .thumb {
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
	overflow: hidden;
}

.news-list h3 {
	font: 500 20px/20px "Roboto", sans-serif;
	margin: 16px 0 9px;
	text-transform: uppercase;
}

.news-list p {
	color: #444;
	font-size: 14px;
	margin: 5px 0 0;
}

.news-list.owl-theme .owl-nav {
	position: absolute;
	top: 35%;
	width: 100%;
	z-index: 99;
}

.content-slider-2 {
	position: relative;
}

.content-slider-2 .grid-1 {
	margin-bottom: 0;
}


/*GRID STYLE 2*/

.grid-1.gride-style-2 {
	border: none;
	margin-bottom: 30px;
}

.grid-1.gride-style-2 .detail {
	padding: 12px;
	background-color: #fdfdfd;
	height: auto;
}


/*SMALL GRID CSS */

.small-grid-container {
	position: relative;
	overflow: hidden;
}

.small-grid, .small-post {
	padding: 0px;
	margin-bottom:0;
}

.small-grid li {
	list-style: outside none none;
}

.small-grid li:last-child .small-post {
	margin-bottom:0;	
}

.small-grid .small-post {
	margin-bottom: 15px;
	position: relative;
	overflow: hidden;
	padding-bottom: 15px;
	border-bottom: 1px solid #f2f2f2;
}

.small-grid .small-thumb {
	float: left;
	margin-right: 15px;
	overflow: hidden;
}

.small-thumb img {
	height: auto;
	width: 100%;
}

.post-content .post-tools {
	color: #fff;
	font-size: 14px;
	margin-right: 0;
	padding-left: 0;
	text-transform: capitalize;
	overflow: hidden;
}

.post-content .post-tools li {
	color: #777;
	float: left;
	font-size: 12px;
}

.post-tools li {
	color: #444;
	float: left;
	font-size: 10px;
	text-transform: uppercase;
}
.post-tools li a {
	color:#777;	
}
.post-tools li::after {
	color: #777;
	content: "/";
	padding: 0 10px;
}

.post-tools li:last-child::after {
	display: none;
}

.post-tools li i {
	margin-right: 5px;
}

.post-tools li strong {
	color: #777;
}

.caption .post-tools {
	color: #fff;
	font-size: 14px;
	text-transform: capitalize;
	list-style: none;
	position: relative;
	overflow: hidden;
}

.latest-news-grid {
	position: relative;
	overflow: hidden;
	margin-bottom: 7px;
}

.latest-news-grid .detail {
	padding: 10px 0;
}

.latest-news-grid .post-tools {
	color: #fff;
	font-size: 14px;
	text-transform: capitalize;
}

.post-tools li:hover {
	color: #f44a56;
}

.small-post .post-content h3 a {
	color: #242424;
	font-size: 16px;
	text-decoration: none;
	font-style: normal;
	text-transform: capitalize;
	font-weight: 500;
}

.small-post .post-content h3 a:hover {
	color: #f44a56 !important;
}

.news-list {
	border-radius: 2px;
	margin-bottom: 30px;
	overflow: hidden;
}

.news-list {
	clear: both;
	float: left;
	width: 100%;
}

.news-list .post-content {
	padding: 24px 30px 30px 15px;
}

.news-list h3 {
	font: 500 18px/42px "Roboto", sans-serif;
	margin: 0;
	padding: 0;
	text-transform: uppercase;
}

#sidebar .headings {
	margin-left: 0px;
}

#sidebar iframe {
	width: 100%;
	border: 0px;
	min-height: 250px;
	border-radius: 3px;
}

.social-icons {
	font-size: 22px;
	margin: 0px;
	overflow: auto;
	text-align: center;
}

.social-icons .social-container {
	overflow: hidden;
	position: relative;
}

.social-icons .social-grid {
	float: left;
	width: 50%;
}

.social-icons:hover .icons {
	color: #fff;
}

.social-icons .icons {
	display: block;
	padding: 10px 0;
}

.authors ul li {
	display: inline-block;
	float: left;
	margin: 0;
	padding: 0 2px 2px 0;
}


/*SIDEBAR WIDGET*/

.sidebar-color {
	background-color: #F1f1f1;
}

.sidebar-color .widget {
	margin-bottom: 0;
}

.sidebar-no-color .widget {
	background-color: transparent;
	padding: 0
}

.sidebar-no-color .widget .grid-1 .detail {
	padding: 20px 0;
}

.sidebar-no-color .widget .grid-1 .detail .caption h5 {
	font-size: 16px;
}

.widget {
	position: relative;
	overflow: hidden;
	margin-bottom: 6px;
}
#sidebar div.widget:last-child {
	margin-bottom:0;	
}
.widget-bg {
	background-color: #F9F9F9;
	padding: 10px;
}

.widget-bg-white {
	background-color: #FFF;
	padding: 20px;
}

.widget .ad-div {
	margin-bottom: 0px;
}

.widget .ad-div img {
	display: inline-block;
}

.rss-widget {
	background-color: #ffab2e;
}

.rss-widget .heading .main-heading {
	background-color: #f2f2f2;
	color: #444;
}

.rss-widget .heading .heading-ping {
	border-color: transparent transparent transparent #f2f2f2;
}

.rss-widget .form-inline {
	margin-bottom: 20px;
}

.rss-widget .form-inline .form-group .form-control:focus {
	box-shadow: none;
	border-color: #F2f2f2;
}

.rss-widget .form-inline .form-control {
	width: 300px;
	border-radius: 3;
	height: 45px;
    border: 2px solid #fff;

}

.rss-widget .form-inline .btn {
	border-radius: 3;
    height: 45px;
    padding: 6px 10px;
    border: 2px solid #fff;
}

.rss-widget p {
	color: #FFF;
}

.widget .grid-1 .detail {
	padding: 20px;
	background-color: #FFF;
}

.widget .grid-1 {
	margin-bottom: 0;
	border: none;
}

.widget .owl-nav {
	position: absolute;
	top: -72px;
	right: 0;
	margin-top: 0;
}

.widget .owl-theme .owl-nav div.owl-prev::after, .widget .owl-theme .owl-nav div.owl-next::after {
	font-size: 20px;
	color: #FFF;
}

.widget .video-section-widget iframe {
	/*height:280px;*/
}

.widget .video-section-widget iframe {
	height: 100%;
	width: 100%;
}


/*WIDGET CATEGORY*/

.cat-holder {
	float: left;
	margin: 0;
	width: 100%;
	padding: 0;
}

.cat-holder li:first-child {
	padding-top: 0;
}

.cat-holder li {
	float: left;
	list-style: outside none none;
	width: 100%;
	padding: 10px 0;
	border-bottom: 1px solid #ddd;
}

.cat-holder li:last-child {
	margin-bottom: 20px;
}

.cat-holder a {
	color: #444;
	font-size: 14px;
}

.cat-holder a:hover {
	color: #f44a56;
}

.cat-holder .count {
	color: #444;
	float: right;
	font-size: 14px;
	font-weight: 500;
	text-align: center;
	font-style: normal;
}

.cat-holder a::before {
	color: #444;
	content: "";
	font-family: FontAwesome;
	font-size: 16px;
	margin-right: 10px;
}


/*POL WIDGET*/

.pol-holder li {
	border-bottom: none;
	float: left;
	list-style: outside none none;
	padding: 10px 0;
	width: 100%;
}

.pol-holder a {
	color: #777;
	font-size: 14px;
}

.pol-holder ul {
	margin: 0;
	padding: 0;
	width: 100%;
	position: relative;
	overflow: hidden;
	padding-left: 10px;
}

.pol-holder li input {
	margin-right: 10px;
}

.widget .pol-btn {
	position: relative;
	overflow: hidden;
	margin-top: 20px;
}

.widget .pol-btn a {
	background-color: transparent;
	border: 1px solid #f44a56;
	padding: 10px 15px;
	text-transform: uppercase;
	text-align: center;
	margin-right: 10px;
	font-size: 14px;
	color: #f44a56;
}

.widget .pol-btn a:hover {
	background-color: #f44a56;
	color: #FFF;
}

.widget .pol-holder p {
	font-size: 16px;
	font-weight: 500;
}

.search-widget input.form-control {
	height: 40px;
	box-shadow: none;
}

.search-widget input.form-control:focus {
	border: 1px solid #ccc;
}

.search-widget {
	position: relative;
	overflow: hidden;
}

.search-widget button {
	position: absolute;
	right: 0;
	top: 0;
	padding: 10px;
	background-color: transparent;
	box-shadow: none;
	border: none;
}


/*AUTHOR POST WIDGET*/

.author-widget {
	position: relative;
	overflow: hidden;
}

.author-widget .auth-pic {
	margin-bottom: 20px;
	padding: 0 80px;
}

.author-widget .auth-meta {
	text-align: center;
}

.author-widget .auth-meta h4 {
	margin-top: 0;
	font-size: 20px;
	font-weight: 500;
	text-transform: capitalize;
}

.author-widget .social-media-icons ul {
	padding: 0;
}

.author-widget .social-media-icons ul li i {
	color: #444;
}


/*PHOTO GALLERY SECTION*/

.photo-gallery-section {
	position: relative;
	overflow: hidden;
	padding: 0;
}


/* TABS*/

ul.tabs-posts {
	margin: 0px;
	padding: 0px;
	clear: both;
}

ul.tabs-posts>li {
	list-style-type: none;
	margin-top: 16px;
	overflow: hidden;
	position: relative;
}

.tabs-posts .post-tools li {
	border: none;
}

.tabs-posts .post-tools {
	margin-top: 10px;
}

.nav-tabs.nav-justified> li> a {
	border-bottom: none !important;
	border-radius: 0px !important;
	color: #FFF;
	background-color: #444;
	font-size: 16px;
	font-weight: 500;
	text-transform: capitalize;
}

.nav-tabs> li.active> a, .nav-tabs> li.active> a:focus, .nav-tabs> li.active> a:hover {
	border-radius: 0px;
	border-color: #f44a56 !important;
	background-color: #f44a56;
	color: #FFF;
}

#sidebar ul.nav-tabs {
	padding-left: 0px;
	border-bottom: 2px solid #f44a56;
}

ul.tabs-posts li .pic {
	float: left;
	margin-right: 10px;
}

ul.tabs-posts li .info {
	color: #aaaaaa;
	padding-top: 10px;
}

ul.tabs-posts li .info .likes {
	padding-right: 16px;
}

ul.tabs-posts li i {
	margin-right: 5px;
}

ul.tabs-posts li .caption a {
	color: #242424;
	font-size: 16px;
	text-transform: capitalize;
	font-weight: 400;
	font-family: system-ui;
}

ul.nav-tabs.nav-justified> li {
	border-right: 1px solid #fff;
}

.nav-tabs.nav-justified> li> a:hover {
	border: 1px solid #444;
}

.tag-list {
	padding-top: 20px;
}

.tag-list a {
	border: 1px solid #f9f9f9;
	color: #444;
	background-color: #f9f9f9;
	display: inline-block;
	font-size: 14px;
	margin-bottom: 10px;
	margin-right: 3px;
	padding: 5px 15px;
	text-transform: uppercase;
	transition: all 0.3s ease 0s;
}

.tag-list a, .tag-list a:hover, .tag-list a:active, .tag-list a:focus {
	text-decoration: none;
	outline: none;
}

.tag-list a:hover {
	color: #fff;
	background-color: #f44a56;
}

.tabs-posts li .caption a:hover {
	text-decoration: underline;
	color: #242424;
}


/*ADVERTISEMENT CSS  */

.style-box {
	text-align: center;
	width: 100%;
}

.ad-728x90 {
	padding-top: 14px;
	padding-bottom: 14px;
	text-align: right;
}

.ad-728x90 img {
	padding: 0px;
}

.ad-div {
	display: table;
	width: 100%;
	height: 100%;
	margin-bottom: 50px;
}

.ad-div img {
	display: inline-block;
}

.section .ad-div {
	margin-bottom: 30px;
	margin-top: 10px;
}

section.ad-section {
	padding: 40px 0;
	background-color: #f2f2f2;
}

.ad-section .ad-div {
	margin-bottom: 0;
}


/*CONTACT US CSS*/

.contact-detail p {
	margin-bottom: 50px;
	line-height: 30px;
	width: 50;
}

.contact-form h3 {
	text-transform: capitalize;
	margin-top: 0;
}

.contact-form .form-group {
	margin-bottom: 20px;
}

.contact-form .form-group input {
	height: 40px;
	border-radius: 0;
	box-shadow: none;
	border: none;
	border-bottom: 1px solid #CCC;
	padding-left: 0;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.contact-form .form-group textarea {
	border-radius: 0;
	box-shadow: none;
	border: none;
	border-bottom: 1px solid #CCC;
	padding-left: 0;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.contact-form .form-group input:focus, .contact-form .form-group textarea:focus {
	background-color: #F9f9f9;
	box-shadow: none;
	border-color: #ccc;
	padding-left: 15px;
}

.contact-detail {
	width: 35%;
	text-align: center;
	display: block;
	margin: 0 auto;
}

.contact-detail h3 {
	font-size:20px;
	margin-top:0;	
}

.contact-detail-box .icon-box {
	background-color: #f44a56;
	float: left;
	margin-right: 10px;
	padding: 10px 12px;
	border-radius: 4px;
}

.contact-detail-box .icon-box i {
	color: #fff;
	font-size: 24px;
}

.contact-detail-box {
	margin-bottom: 20px;
	overflow: hidden;
	position: relative;
	padding: 15px;
	background-color: #F2f2f2;
}

.content-area h4 {
	font-size: 18px;
	margin: 0;
}

.content-area p {
	margin-bottom: 0;
}

.contact-form-style-2 {}

.contact-form-style-2 .contact-form .form-group input {
	height: 40px;
	border-radius: 4px;
	background-color: #F2f2f2;
	padding-left: 15px;
	border: none;
}

.contact-form-style-2 .contact-form .form-group textarea {
	border-radius: 4px;
	background-color: #F2f2f2;
	padding-left: 15px;
	border: none;
}

.contact-form-style-2 .contact-detail-box {
	border-radius: 4px;
}


/* FOOTER CSS*/

footer {
	position: relative;
	width: 100%;
	overflow: hidden;
}

footer h2 {
	text-transform: capitalize;
	color: #FFF;
	font-size: 20px;
	margin-top: 0;
	margin-bottom: 20px;
}

.bg-dark .post-tools li::after {
	color: #fff;
	content: "/";
	padding: 0 10px;
}
.bg-dark .post-tools li a {
	color:#FFF;	
}
.bg-dark .post-tools li strong {
    color: #FFF;
}
.footer-top {
	padding: 17px 0;
}

.footer-link ul {
	position: relative;
	float: left;
	margin: 0;
	padding-left: 0;
}

.footer-link ul li {
	position: relative;
	float: left;
	font-size: 13px;
	font-weight: 400;
	color: #fff;
	margin-right: 17px;
	letter-spacing: 1px;
}

.copyright {
    position: relative;
    padding-top: -3px;
    margin-bottom: -16px;
    margin-top: -8px;
}

.copyright span {
    color: white;
    font-size: 14px;
    text-transform: capitalize;
    font-family: system-ui;
    font-weight: 400;
}


footer p, footer a {
	color: #fff;
}

footer p {
	display: inline-block;
	position: relative;
	overflow: hidden;
}

footer .footer-block p {
	margin-top:20px;	
}
footer .footer-block ul {
	position: relative;
	padding-left: 12px;
	padding:0;
}

footer .footer-block a {
	width: 100%;
	display: block;
	padding: 6px 0;
	text-transform: capitalize;
}

footer .footer-block a span {
	float: right;
}

.footer-link.bg-white, footer.bg-white {
    border-top: 1px solid #f6f6f6;
    padding: 15px 0px;
    background-color: #22293E;
}

.footer-link.bg-white ul li a {
	color: #242424;
	font-weight: 400;
}

.footer-link.bg-white ul li a:hover {
	color: #f44a56;
}

.footer-top ul.tabs-posts li .caption a, .footer-top .post-tools li {
	color: #fff;
}

.footer-top ul.tabs-posts li .pic a img {
	width: 75px;
}

.footer-block {
	position: relative;
}

footer .personal-info li {
    color: #F9f9f9;
    display: inline-block;
    font-size: 16px;
    padding-bottom: 10px;
    width: 100%;
    font-weight: 300;
}

footer .personal-info li i {
	color: #fff;
	float: left;
	font-size: 18px;
	line-height: 24px;
	margin-right: 10px;
}

.my-pined-post {
	position: relative;
	overflow: hidden;
}

.my-pined-post .thumb {
	float: none;
}

.my-pined-post-meta {
	position: relative;
	overflow: hidden;
}

footer .my-pined-post a {
	padding: 0;
}

footer {
	background-attachment: scroll;
	background-image: url("../images/footer-bg-2.png");
	background-position: center bottom;
	padding-bottom: 0;
	position: relative;
}

.footer-white h2, .footer-white p, .footer-white a, .footer-white .personal-info li, .footer-white .personal-info li i, .footer-white ul.tabs-posts li .caption a, .footer-white .post-tools li, .footer-white .social-media-icons ul li i {
	color: #242424;
}

.footer-white .personal-info li, .footer-white ul.tabs-posts li .caption a {
	font-size: 14px;
}

.footer-white ul.post-tools li {
	margin-bottom: 0;
}

.footer-2 {
	background-color: #242424;
}

.footer-2 .footer-block {
	display: block;
	margin: 0 auto;
	text-align: center;
}

.footer-2 .footer-top {
	padding: 30px 0;
}

.footer-footer-area {
	border-top: 1px solid #999;
	margin-top: 20px;
	overflow: hidden;
	padding-top: 20px;
	position: relative;
}

.footer-footer-area ul {
	list-style: outside none none;
	margin: 0;
	padding-left: 0;
	position: relative;
	float: right;
}

.footer-footer-area ul li {
	color: #fff;
	float: left;
	font-weight: 400;
	letter-spacing: 1px;
	margin-right: 0;
	margin-left: 15px;
	position: relative;
}

.footer-footer-area .copyright {
	float: left;
	padding: 0;
	position: relative;
}

.footer-footer-area .copyright a {
	font-size: 16px;
	font-weight: 500;
	color: #F44e56;
	margin-left: 5px;
}

.footer-footer-area .copyright span {
	font-size: 14px;
	color: #FFF;
}

.footer-footer-area p, .footer-footer-area a {
	color: #FFF;
}


/*INSTAGRAM GALLERY*/

.insta-gallery a {
	width: 80px;
	display: inline-block;
	margin-right: 5px;
	margin-bottom: 5px;
}

footer .footer-top ul.twitter-widget .caption a.url {
	display: block;
	font-weight: 700;
	color: #f44a56;
	margin-top: 5px;
	text-transform: none;
}


/*LOAD MORE POSTS*/

#loadMore {
	margin: 30px 0;
}

div.post {
	display: none;
}

#fluid-posts .ad-div {
	margin: 20px 0;
}

.zerogrid-section .grid-item .ad-div img {
	height: 450px;
}


/*FOOTER TAGS*/

.bg-dark .tag-list a {
	background-color: rgba(255, 255, 255, 0.1);
	border: none;
	border-radius: 4px;
	color: #FFF;
	font-size: 12px;
}

footer .tag-list a {
	border: none;
	border-radius: 4px;
	color: #444;
	font-size: 12px;
}

footer .tag-list {
	padding: 0;
}

.footer-detail ul {
	padding: 0;
}


/* SOCIAL COUNTERS*/

.social-icons .facebook .icons {
	color: #3b5998;
}

.social-icons .googleplus .icons {
	color: #dd4b39;
}

.social-icons .twitter .icons {
	color: #2aa9e0;
}

.social-icons .instagram .icons {
	color: #685243;
}

.social-icons .pinterest .icons {
	color: #cc2028;
}

.social-icons .youtube .icons {
	color: #de1829;
}

.social-icons .vine .icons {
	color: #35b57c;
}

.social-icons .soundcloud .icons {
	color: #ff4100;
}

.social-icons .vk .icons {
	color: #45668e;
}

.social-icons .foursquare .icons {
	color: #f94877;
}

.social-icons .github .icons {
	color: #333333;
}

.social-icons .dribbble .icons {
	color: #ea4c89;
}

ul.social-counter-list li.colored-social i {
	float: left;
	font-size: 20px;
	height: 45px;
	line-height: 45px;
	margin-right: 10px;
	text-align: center;
	vertical-align: middle;
	width: 45px;
	padding: 0;
	color: #FFF;
}

ul.social-counter-list li.colored-social .sc-num {
	margin: 0;
}

.colored-social .fa-dribbble, .social-grid-icons .fa-dribbble:hover, .social-icons .dribbble:hover .icons {
	background-color: #f46899;
}

.colored-social .fa-vimeo, .social-grid-icons .fa-vimeo:hover, .social-icons .vimeo:hover .icons {
	background-color: #00B3EC;
}

.colored-social .fa-stumbleupon, .social-grid-icons .fa-stumbleupon:hover {
	background-color: #eb4924;
}

.colored-social .fa-reddit, .social-grid-icons .fa-reddit:hover {
	background-color: #5f99cf;
}

.colored-social .fa-facebook, .social-grid-icons .fa-facebook:hover, .social-icons .facebook:hover .icons {
	background-color: #3c599f;
}

.colored-social .fa-rss, .social-grid-icons .fa-rss:hover {
	background-color: #f26522;
}

.colored-social .fa-lastfm, .social-grid-icons .fa-lastfm:hover {
	background-color: #d51007;
}

.colored-social .fa-flickr, .social-grid-icons .fa-flickr:hover {
	background-color: #ff0084;
}

.colored-social .fa-instagram, .social-grid-icons .fa-instagram:hover, .social-icons .instagram:hover .icons {
	background-color: #685243;
}

.colored-social .fa-foursquare, .social-grid-icons .fa-foursquare:hover, .social-icons .foursquare:hover .icons {
	background-color: #0086be;
}

.colored-social .fa-github, .social-grid-icons .fa-github:hover, .social-icons .github:hover .icons {
	background-color: #070709;
}

.colored-social .fa-google-plus, .social-grid-icons .fa-google-plus:hover, .social-icons .googleplus:hover .icons {
	background-color: #cf3d2e;
}

.colored-social .fa-instagram, .social-grid-icons .fa-instagram:hover {
	background-color: #a1755c;
}

.colored-social .fa-linkedin, .social-grid-icons .fa-linkedin:hover {
	background-color: #0085ae;
}

.colored-social .fa-pinterest, .social-grid-icons .fa-pinterest:hover, .social-icons .pinterest:hover .icons {
	background-color: #cc2127;
}

.colored-social .fa-twitter, .social-grid-icons .fa-twitter:hover, .social-icons .twitter:hover .icons {
	background-color: #32ccfe;
}

.colored-social .fa-vk, .social-grid-icons .fa-vk:hover, .social-icons .vk:hover .icons {
	background-color: #375474;
}

.colored-social .fa-soundcloud, .social-grid-icons .fa-soundcloud:hover, .social-icons .soundcloud:hover .icons {
	background-color: #ff4100;
}

.colored-social .fa-vine, .social-grid-icons .fa-vine:hover, .social-icons .vine:hover .icons {
	background-color: #35b57c;
}

.colored-social .fa-xing, .social-grid-icons .fa-xing:hover {
	background-color: #00555c;
}

.colored-social .fa-youtube, .social-grid-icons .fa-youtube:hover, .social-icons .youtube:hover .icons {
	background-color: #c52f30;
}

.social-counter-widget li.social-counter-twitter i {
	border-color: #00baff;
	color: #00baff;
}

.social-counter-widget li.social-counter-facebook i {
	border-color: #44619d;
	color: #44619d;
}

.social-counter-widget li.social-counter-gplus i {
	border-color: #e71414;
	color: #e71414;
}

.social-counter-widget li.social-counter-youtube i {
	border-color: #c30000;
	color: #c30000;
}

.social-counter-widget li.social-counter-vimo i {
	border-color: #44bbff;
	color: #44bbff;
}

.social-counter-widget li.social-counter-soundcloud i {
	border-color: #ff681e;
	color: #ff681e;
}

.social-counter-widget li.social-counter-instgram i {
	border-color: #0b79e5;
	color: #0b79e5;
}

.social-counter-widget li.social-counter-dribbble i {
	border-color: #d875a2;
	color: #d875a2;
}

.social-counter-widget li.social-counter-delicious i {
	border-color: #2567ee;
	color: #2567ee;
}

ul.social-counter-list {
	padding-left: 0;
	list-style: none;
}

ul.social-counter-list li {
	margin-bottom: 10px;
	width: 50%;
	float: left;
}

ul.social-counter-list li a {
	position: relative;
	overflow: hidden;
}

ul.social-counter-list li i {
	font-size: 20px;
	padding: 15px;
	background-color: #FFF;
	margin-right: 20px;
	float: left;
}

ul.social-counter-list .sc-num {
	display: block;
	font-size: 20px;
	margin: 5px 0;
}

.social-ping {
	border-color: transparent transparent transparent #FFF;
	border-style: solid;
	border-width: 10px 0 10px 10px;
	height: 0;
	position: absolute;
	top: 28px;
	width: 0;
	left: -20px;
}

.social-counter-list-2 {
	position: relative;
	overflow: hidden;
	padding: 0;
	list-style: none;
	margin-bottom: 0;
}

.social-counter-list-2 li.social-item {
	display: block;
	position: relative;
	overflow: hidden;
	margin-bottom: 20px;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}
.social-counter-list-2 li.social-item:last-child {
	margin-bottom:0;	
}

.social-counter-list-2 li.social-item a:hover .social-item-meta {
	background-color: #242424;
	color: #FFF;
}

.social-counter-list-2 li.social-item .social-item-icon.twitter {
	background-color: #00baff;
}

.social-counter-list-2 li.social-item .social-item-icon.facebook {
	background-color: #44619d;
}

.social-counter-list-2 li.social-item .social-item-icon.gplus {
	background-color: #e71414;
}

.social-counter-list-2 li.social-item .social-item-icon.youtube {
	background-color: #c30000;
}

.social-counter-list-2 li.social-item .social-item-icon.vimo {
	background-color: #44bbff;
}

.social-counter-list-2 li.social-item .social-item-icon.soundcloud {
	background-color: #ff681e;
}

.social-counter-list-2 li.social-item .social-item-icon.instgram {
	background-color: #0b79e5;
}

.social-counter-list-2 li.social-item .social-item-icon.dribbble {
	background-color: #d875a2;
}

.social-counter-list-2 li.social-item .social-item-icon.delicious {
	background-color: #2567ee;
}

.social-counter-list-2 li.social-item .social-item-icon.linkedin {
	background-color: #0085ae;
}

.social-counter-list-2 li.social-item .social-item-icon {
	border-radius: 4px 4px 0 0;
	color: #FFF;
	text-align: center;
	padding: 10px 0;
}

.social-counter-list-2 li.social-item .social-item-icon i {
	font-size: 20px;
}

.social-counter-list-2 li.social-item .social-item-meta {
	padding: 10px 0;
	text-align: center;
	background-color: #F2f2f2;
	border-radius: 0 0 4px 4px;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.social-counter-list-2 li.social-item .social-item-num {
	font-size: 20px;
	font-weight: 500;
}

.social-style-3 li.social-item .social-item-meta {
	background-color: #242424;
	border-radius: 0 4px 4px 0;
	float: right;
	padding: 10px 20px;
	text-align: center;
	transition: all 0.3s ease 0s;
}

.social-style-3 li.social-item .social-item-icon i {
	float: left;
	font-size: 20px;
	line-height: 25px;
	padding: 10px;
}

.social-style-3 li.social-item .social-item-icon {
	color: #fff;
	padding: 10px 0;
	text-align: left;
	position: relative;
	overflow: hidden;
	padding: 0;
	border-radius: 4px;
}

.social-style-3 li.social-item .social-item-icon span.title {
	display: inline-block;
	font-size: 16px;
	overflow: hidden;
	line-height: 45px;
	position: relative;
	vertical-align: middle;
}

.social-style-3 li.social-item .social-item-num {
	font-size: 20px;
	font-weight: 400;
}


/*SOCIAL MEDIA ICONS*/

.social-media-icons {
	position: relative;
	margin: 10px 0;
}

footer .social-media-icons ul {
	position: relative;
	padding-left: 0;
	list-style: none;
	margin-bottom: 0;
}

.social-media-icons ul li {
	display: inline-block;
}

.social-media-icons ul li i {
	font-size: 16px;
	color: #777;;
	padding: 8px;
	background-color: ;
	display: block;
	-webkit-transition: all 0.3s ease 0s;
	-moz-transition: all 0.3s ease- 0s;
	-o-transition: all 0.3s ease 0s;
	transition: all 0.3s ease 0s;
}

.social-media-icons ul li:hover i {
	color: #f44a56;
	border-color: #f44a56;
}

.social-media-icons ul li a {
	padding: 0;
}


/* MAIN TABS CSS */

.section .tabs .nav-tabs.nav-justified {
	margin-bottom: 30px;
	border-bottom: 3px solid #f44a56;
}

.section .nav-tabs.nav-justified> li> a {
	text-transform: capitalize;
	font-weight: 500;
}

.section ul.nav-tabs.nav-justified> li {
	border: none;
}


/*TOP SMALL POSTS SLIDER*/

.top-small-post-slider {
	padding: 10px 0;
	position:relative;
}
.top-small-post-slider::after {
	background: rgba(247, 247, 247, 0.9);
	content: "\a ";
	height: 100%;
	left: 0;
	opacity: 1;
	position: absolute;
	top: 0;
	transition: all 0.5s ease 0s;
	width: 100%;
}
.top-small-post-slider .small-post {
	overflow: hidden;
	position: relative;
	background-color: #f9f9f9;
	margin: 0 15px;
	border-radius: 5px;
	border: 1px solid #f2f2f2;
}

.top-small-post-slider .small-thumb {
    float: left;
    overflow: hidden;
    width: 386px;
    height: 100px;
}

.top-small-post-slider .post-content {
	position: relative;
	overflow: hidden;
	padding: 10px;
}

.top-small-post-slider .post-content h3 {
	font-weight: normal;
	line-height: 20px;
	margin-bottom: 10px;
	margin-top: 0;
	padding: 0;
}

.top-small-post-slider .post-content h3 a {
	color: #242424;
	font-size: 16px;
	font-style: normal;
	font-weight: 400;
	text-decoration: none;
	text-transform: capitalize;
}

.top-small-post-slider .post-content .post-tools {
	margin-bottom: 0;
}

.top-small-post-slider .post-content .post-tools li, .top-small-post-slider .post-content .post-tools li a {
	color: #444;
}

.top-small-post-slider .post-content .post-tools li:hover, .top-small-post-slider .post-content .post-tools li a:hover {
	color: #f44a56;
}

.top-small-post-slider .post-content .post-tools li::after {
	color: #777;
	content: "/";
	padding: 0 10px;
}

.top-small-post-slider .owl-nav {
	margin: 0;
}

.top-small-post-slider .owl-theme .owl-nav div.owl-prev::after, .top-small-post-slider .owl-theme .owl-nav div.owl-next::after {
	font-size: 30px;
}

.top-small-post-slider .owl-theme .owl-nav div.owl-prev {
	left: -3px;
	position: absolute;
	top: 28px;
	border-radius: 5px;
}

.top-small-post-slider .owl-theme .owl-nav div.owl-next {
	right: -3px;
	position: absolute;
	top: 32px;
	border-radius: 5px;
}
/*404 PAGE*/

.error-info {
	text-align:center;
	
}
.error-info img {
	margin-bottom: 30px;
}
.error-info .error-sub {
	font-size: 20px;
}
.error-info p {
	margin-top:10px;
	margin-bottom:20px;
}





.listing .author-box {
	margin-top:0;	
}

/* ---- COLOR SWITCHER CSS ----- */
.color-switcher {
	width: 142px;
	position: fixed;
	left: -142px;
	top: 25%;
	background: #fff;
	z-index: 9999;
	padding: 0 0 5px;
	-webkit-transition: all 500ms ease;
	-moz-transition: all 500ms ease;
	-ms-transition: all 500ms ease;
	-o-transition: all 500ms ease;
	transition: all 500ms ease;
	border: 1px solid #F1F1F1;
}
.color-switcher h5 {
	 border-bottom: 1px solid #f1f1f1;
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    margin: 20px 15px;
    padding: 20px 0;
    text-transform: uppercase;
	margin-top:0;
}
.color-switcher ul {
	list-style: outside none none;
    overflow: hidden;
    padding: 0 15px;
    position: relative;
}
.color-switcher ul li {
	float: left;
	margin-right: 10px;
	margin-bottom: 10px
}
.color-switcher ul li:nth-child(even) {
	margin-right:0;
}
.color-switcher ul li a {
	display: block;
	width: 45px;
	height: 45px;
	outline: none
}
.color-switcher ul li a.defualt {
 background: #f44a56;
}
.color-switcher ul li a.midnight-blue {
 background: #2c3e50;
}
.color-switcher ul li a.blue {
 background: #2196f3;
}
.color-switcher ul li a.amethyst {
 background: #9b59b6;
}
.color-switcher ul li a.golden {
 background: #e4b714;
}
.color-switcher ul li a.see-green {
 background: #40c37d;
}
.color-switcher ul li a.carrat {
 background: #e67e22;
}
.color-switcher ul li a.java-color {
 background: #1F9EA3;
}

.picker_close {
	width: 35px;
	height: 50px;
	position: absolute;
	right: -35px;
	top: -1px;
	border-radius:0 5px 5px 0;
	border: 1px solid #F1F1F1;
	text-align: center;
	background: no-repeat center 5px #fff;
	border-left:0;
}
.picker_close i {
	font-size: 24px;
	margin-top: 12px;
	-webkit-transition: all 500ms ease;
	-moz-transition: all 500ms ease;
	-ms-transition: all 500ms ease;
	-o-transition: all 500ms ease;
	transition: all 500ms ease
}
.position {
	left: 0;
	-webkit-transition: all 500ms ease;
	-moz-transition: all 500ms ease;
	-ms-transition: all 500ms ease;
	-o-transition: all 500ms ease;
	transition: all 500ms ease
}
.rotate-arrow {
	 -ms-transform: rotate(180deg); /* IE 9 */
    -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
    transform: rotate(180deg);	
}




/* RESPONSIVENESS QURIES */


@media (min-width: 1300px) and (max-width: 3000px) {
	.box-style .container {
		width:1280px;
	}
}
/*
@media (min-width: 768px) and (max-width: 991px) {
}*/

@media screen and (min-width: 768px) and (max-width: 1279px) {
	.megaMenu {
		border-radius: 0;
		background-color: #FFF;
	}
	.megaMenu ul.menu-logo> li> a {
		color: #444;
	}
	.advertisement img {
		margin-top: 20px;
	}
	.logo-row .logo a {
		text-align: center;
		display: block;
	}
	.megaMenu ul.menu-logo> li> a:hover {
		color: #444;
	}
	.megaMenu ul.menu-mobile-trigger i.fa {
		border-radius: 0;
	}
	.footer-block {
		margin-bottom: 30px;
	}
	.zerogrid .col-1-4 {
		width: 100%;
	}
	.zerogrid-section .zerogrid .col-1-4 .wrap-col {
		float: left;
		width: 50%;
	}
	.zerogrid-section .zerogrid .col-2-4 {
		width: 100%;
	}
	.heading .main-heading {
		padding: 8px 12px;
		font-size: 18px;
	}
	.small-grid .small-thumb {
		margin-right: 10px;
		width: 75px;
	}
	.listing .desc p {
		display: none;
	}
	.heading .heading-ping {
		top: 8px;
	}
	.breakingNews> .bn-title> h2 {
		font-size: 18px;
		font-weight: 500;
	}
	.top-small-post-slider .small-thumb {
		margin-right: 8px;
		width: 386px;
	}
	.megaMenu ul.menu-logo> li {
		border: none;
	}
	footer h2 {
		margin-top: 20px;
	}
	.topbar {
		display: none;
	}
	.zerogrid .col-1-3 {
		width: 50%;
	}
	.parallel-post-style .grid-box ul {
	height: 96px;
	}
}

@media screen and (min-width: 320px) and (max-width: 767px) {
	.heading .main-heading {
		padding: 8px 12px;
		font-size: 16px;
	}
	.megaMenu {
		border-radius: 0;
		background-color: #FFF;
	}
	.megaMenu ul.menu-logo> li> a {
		color: #444;
	}
	.advertisement img {
		margin-top: 20px;
	}
	.logo-row .logo a {
		text-align: center;
		display: block;
	}
	.megaMenu ul.menu-mobile-trigger i.fa {
		border-radius: 0;
	}
	.megaMenu ul.menu-logo> li> a:hover {
		color: #444;
	}
	.breakingNews {
		min-height: inherit;
		height: 40px;
	}
	.heading .main-heading {
		font-size: 18px;
	}
	.heading-read-more a {
		margin-top: 10px;
	}
	.caption h5 {
		font-size: 18px;
	}
	.small-grid .small-thumb {
		width: 70px;
	}
	.heading-read-more a {
		margin-top: 0;
	}
	.grid-1 {
		margin-bottom: 20px;
	}
	.grid-1 .desc h5 {
		margin-top: 10px;
		font-size: 18px;
	}
	ul.tabs-posts li .pic {
		margin-right: 10px;
		width: 70px;
	}
	.widget-bg {
		padding: 20px 15px;
	}
	#sidebar ul.nav-tabs {
		border: none;
	}
	ul.social-counter-list li i {
		font-size: 16px;
		margin-right: 15px;
		padding: 15px;
	}
	.social-ping {
		top: 20px;
	}
	ul.social-counter-list .sc-num {
		font-size: 16px;
		margin: 0;
	}
	.newsletter .form-group {
		float: left;
		width: 86%;
	}
	.rss-widget .form-inline .form-control {
		width: 100%;
	}
	.widget .owl-nav {
		display: none;
	}
	.copyright {
		display: inline-block;
		float: none;
	}
	.footer-link {
		text-align: center;
	}
	.footer-link ul {
		display: table;
		margin: 0 auto;
		float: none;
	}
	.social-media-icons ul li i {
		margin-bottom: 10px;
	}
	.featured-news-slider .post-content h5 {
		font-size: 18px;
	}
	.topbar .social-media-icons-top {
		float: none;
		text-align: center;
	}
    .topbar ul li {
	    margin-right: 6px;
        font-size: 14px;
        padding-right: -7px;
        margin-left: 79px;
	}
	.topbar .social-media-icons-top li {
		margin-bottom: 0;
		margin-left: 0;
		margin-right: 10px;
	}
	.top-small-post-slider .small-thumb {
	}
	.top-small-post-slider .post-content {
		padding: 15px;
	}
	.parallel-post-style .grid-box .thumb img {
		width: 100%;
	}
	footer h2 {
		margin-top: 20px;
	}
	.topbar {
		/* display: none; */
	}
	.full-width-slider .owl-nav {
		top: -68px;
	}
	.megaMenu ul.menu-logo> li> a {
		padding-top: 10px;
	}
	.megaMenu .grid-1 {
		margin-bottom: 15px;
	}
	.featured-news-slider .post-content .post-tools li::after {
		color: #fff;
		content: "/";
		padding: 0 5px;
	}
	.post-tools li::after {
		color: #444;
		content: "/";
		padding: 0 5px;
	}
		.parallel-post-style .grid-box ul {
	height: 209px;
	}
	.ad-div {
		margin-bottom: 35px;
	}
	.full-with-slider-section .post-content {
		left: 0;
		width: 100%;
	}
	.full-with-slider-section .post-content h5 {
		font-size: 16px;
	}
	.full-with-slider-section .owl-theme .owl-nav .owl-prev {
		top: 35%;
	}
	.full-with-slider-section .owl-theme .owl-nav .owl-next {
		top: 35%;
	}
}




/* STRUCTURE */

.wrapper {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    min-height: 100%;
    padding: 50px;
    background-color: #fffff;
}

#formContent {
  -webkit-border-radius: 10px 10px 10px 10px;
  border-radius: 10px 10px 10px 10px;
  background: #fff;
  padding: 30px;
  width: 124%;
  max-width: 580px;
  position: relative;
  padding: 0px;
  -webkit-box-shadow: 0 30px 60px 0 rgba(0,0,0,0.3);
  box-shadow: 0 30px 60px 0 rgba(0,0,0,0.3);
  text-align: center;
}

#formFooter {
  background-color: #f6f6f6;
  border-top: 1px solid #dce8f1;
  padding: 25px;
  text-align: center;
  -webkit-border-radius: 0 0 10px 10px;
  border-radius: 0 0 10px 10px;
}



/* TABS */


/* FORM TYPOGRAPHY*/

input[type=button], input[type=submit], input[type=reset]  {
    background-color: #2fa5e6;
    border: none;
    color: white;
    padding: 9px 38px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    text-transform: uppercase;
    font-size: 14px;
    border-radius: 5px 5px 5px 5px;
    margin: 5px 16px 6px 20px;
    transition: all 0.3s ease-in-out;
}

input[type=button]:hover, input[type=submit]:hover, input[type=reset]:hover  {
  background-color: #39ace7;
}

input[type=button]:active, input[type=submit]:active, input[type=reset]:active  {
  -moz-transform: scale(0.95);
  -webkit-transform: scale(0.95);
  -o-transform: scale(0.95);
  -ms-transform: scale(0.95);
  transform: scale(0.95);
}


input[type=text]:focus {
  background-color: #fff;
  border-bottom: 2px solid #5fbae9;
}

input[type=text]:placeholder {
  color: #cccccc;
}



/* ANIMATIONS */

/* Simple CSS3 Fade-in-down Animation */
.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

@-webkit-keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

/* Simple CSS3 Fade-in Animation */
@-webkit-keyframes fadeIn { from { opacity:0; } to { opacity:1; } }
@-moz-keyframes fadeIn { from { opacity:0; } to { opacity:1; } }
@keyframes fadeIn { from { opacity:0; } to { opacity:1; } }

.fadeIn {
  opacity:0;
  -webkit-animation:fadeIn ease-in 1;
  -moz-animation:fadeIn ease-in 1;
  animation:fadeIn ease-in 1;

  -webkit-animation-fill-mode:forwards;
  -moz-animation-fill-mode:forwards;
  animation-fill-mode:forwards;

  -webkit-animation-duration:1s;
  -moz-animation-duration:1s;
  animation-duration:1s;
}

.fadeIn.first {
    color: #0d0d0d;
    padding: 9px 20px;
    /* text-align: center; */
    */text-decoration: none;
    display: inline-block;
    font-family: system-ui;
    font-size: 15px;
    margin: 5px;
    width: 96%;
    border: 2px solid #2fa5e6;
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    -webkit-border-radius: 5px 5px 5px 5px;
    border-radius: 5px 5px 5px 5px;
     border-top-style: hidden;
     border-right-style: hidden;
     border-left-style: hidden;

}

.fadeIn.second {
  -webkit-animation-delay: 0.6s;
  -moz-animation-delay: 0.6s;
  animation-delay: 0.6s;
}

.fadeIn.third {
  -webkit-animation-delay: 0.8s;
  -moz-animation-delay: 0.8s;
  animation-delay: 0.8s;
}

.fadeIn.fourth {
  -webkit-animation-delay: 1s;
  -moz-animation-delay: 1s;
  animation-delay: 1s;
}

/* Simple CSS3 Fade-in Animation */
.underlineHover:after {
  display: block;
  left: 0;
  bottom: -10px;
  width: 0;
  height: 2px;
  background-color: #56baed;
  content: "";
  transition: width 0.2s;
}

.underlineHover:hover {
  color: #0d0d0d;
}

.underlineHover:hover:after{
  width: 100%;
}



/* OTHERS */

*:focus {
    outline: none;
}

#icon {
  width:60%;
}

table {
  font-family: arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

td, th {
  border: 1px solid #e0e0e0;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #d4ebff;
}

/*WHATSAPP BUTTON */
.float{
    position: fixed;
    width: 50px;
    height: 47px;
    bottom: 11px;
    left: 8px;
    background-image: linear-gradient(to bottom right, #5EF978, #2FBE2D);
    color: #FFF;
    border-radius: 10px;
    text-align: center;
    font-size: 41px;
    z-index: 100;
    padding-bottom: 10px;
    padding-top: 3px;
    padding-left: 1px;
}

.banner-big-img {
   /* width:900px; */
   height:412px;
   object-fit:cover;
}

.banner-middle-img{
     height:206px;
}

.section-blog-image{
    height: 100%;
    object-fit:cover;
    width: 100%;
}

.menu-links li a.active {
    background-color: #D52925;
    color: white;
}
