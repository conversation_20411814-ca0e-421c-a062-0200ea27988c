from django.contrib.auth.forms import UserCreationForm
from user.models import FmcgUser


class UserRegisterForm(UserCreationForm):
    class Meta:
        model = FmcgUser
        fields = ('username', 'first_name', 'last_name', 'email', 'mobile', 'password1', 'password2', )

    def __init__(self, *args, **kwargs):
        super(UserRegisterForm, self).__init__(*args, **kwargs)
        del self.fields['password2']
        self.fields['password1'].help_text = None
        self.fields['username'].help_text = None



