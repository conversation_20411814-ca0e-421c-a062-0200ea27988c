from django.contrib.auth.models import AbstractUser
from django.db import models

# Create your models here.

class FmcgUser(AbstractUser):
    CHOICES = [
        ('Pending', 'Pending'),
        ('Conformed', 'Conformed')
    ]
    mobile = models.CharField(max_length=255)
    status = models.CharField(max_length=1022, choices=CHOICES, default='Conformed')
    created = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated = models.DateTimeField(auto_now=True, null=True, blank=True)

