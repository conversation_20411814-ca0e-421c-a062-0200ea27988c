{% extends 'include/base.html' %}
{% load static %}
{% block title %} Register - FMCG Horeca Business {% endblock %}


{% block content %}

<section class="my-breadcrumb" style="background-image: url('/static/assets/images/banner/blog-details.jpg');">
         <div class="container page-banner">
            <div class="row">
               <div class="col-sm-12 col-md-12 col-xs-12">
                  <h1 style="font-size: 24px;">Register</h1>
               </div>
            </div>
         </div>
      </section>

<br>

<section class="main-content">
         <div class="container">
            <div class="row">
               <div class="col-md-8 col-sm-8 col-xs-12">
                    <div class="ad-div text-center">
                        {% for ads_section in ads_section %}
                           {% if ads_section.area == 'Width Ads 2' %}
                     <a href="{{ ads_section.link }}" target="_blank">
                     <img src="{{ ads_section.image.url }}" class="img-responsive">
                     </a>
                        {% endif %}
                        {% endfor %}
                  </div>

  <div class="wrapper" style="margin-top: -71px;">
  <div id="formContent">
    <!-- Tabs Titles -->

    <!-- Icon -->
    <div class="fadeIn">
     <h3 style="font-family: fantasy;">REGISTER</h3>
    </div>

    <!-- Login Form -->
    <form method="post" id="signup-form">
    {% csrf_token %}
      <input type="text" class="fadeIn first" name="username"   hx-post="{% url 'check_username' %}"
                                                               hx-target="#username-check"
                                                               hx-trigger="keyup" placeholder="Username" required>
                <p>Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.</p>
<div id="username-check"></div>
<!--        <div class="col-sm-6 col-md-6 col-xs-6">-->
        <input type="text"  class="fadeIn first" name="first_name" placeholder="First Name" required>

        <input type="text" class="fadeIn first" name="last_name" placeholder="Last Name" required>

        <input type="email"  class="fadeIn first" name="email" placeholder="Email Address" required>

        <input type="text" class="fadeIn first" name="mobile" placeholder="Mobile Number" required>

        <input type="password"  class="fadeIn first" name="password1" minlength="6" placeholder="Password" id="password1" required>
        <input type="password" class="fadeIn first" name="password2" minlength="6" placeholder="Conform Password" id="password2" required>

<!--        <ul style="margin-left: -27px;">-->
<!--  <div style="text-align: left;">-->
<!--    <input type="checkbox" onclick="togglePasswordVisibility()"> Show Password-->
<!--  </div>-->
<!--</ul>-->

            <p id="error-message" style="color: red;"></p>

      <input type="submit" class="fadeIn fourth" value="Sign Up">
    </form>

    <!-- Remind Passowrd -->
    <div id="formFooter">
      <a class="underlineHover" href="{% url 'login' %}">Have an account? Log in</a>
    </div>

  </div>
</div>
               </div>



               <div class="col-md-4 col-sm-12 col-xs-12" id="side-bar-right">
                  <div class="theiaStickySidebar">
                       <aside class="sidebar-color ">
                        <div class="widget widget-bg">
                           <div class="heading">
                              <h2 class="main-heading">EVENTS</h2>
                           </div>
                           <ul class="tabs-posts">
                              {% for events_blog in events_blog %}
                              <li>
                                 <div class="pic">
                                    <img alt="{{ events_blog.event.name }}" title="{{ events_blog.event.name }}" class="img-responsive" src="{{ events_blog.event.image.url }}" style="width:75px; height:75px; object-fit:cover;"> </div>
                                 <div class="caption">
                                    <a href="{% url 'blog_details' events_blog.event.slug %}">{{ events_blog.event.name|truncatechars:50 }}
                                    </a>
                                 </div>
<!--                                 <ul class="post-tools">-->
<!--                                    <li>{{ events_blog.created }} </li>-->
<!--                                 <li><i class="ti-user"></i>FMCG Horeca Business</li>-->
<!--                                   </ul>-->
                              </li>
                              {% endfor %}
                           </ul>
                        </div>
                     </aside>
                      <br>

                       {% include "subscribe.html" %}

                     <aside>

                          {% for ads_section in ads_section %}
                           {% if ads_section.area == 'Right Side Ads' %}
                                   {% if forloop.counter < 2 %}

   <div class="widget widget-bg">
                           <div class="ad-div style-box">
                               {% if ads_section.link %}
                              <a href="{{ ads_section.link }}" target="_blank">
                                  {% endif %}

                              <img src="{{ ads_section.image.url }}" class="img-responsive">
                              </a>
                           </div>
                        </div>
                         {% else %}
                            <div class="widget widget-bg">
                           <div class="ad-div style-box">
                                {% if ads_section.link %}
                              <a href="{{ ads_section.link }}" target="_blank">
                                  {% endif %}
                              <img src="{{ ads_section.image.url }}" class="img-responsive">
                              </a>
                           </div>
                        </div>

                          {% endif %}
                              {% endif %}
                              {% endfor %}
                     </aside>
                  </div>
               </div>
            </div>
         </div>
      </section>

<script>
 document.getElementById("signup-form").addEventListener("submit", function(event) {
    event.preventDefault(); // Prevent the form from submitting normally
    var form = event.target;
    var password1 = form.querySelector("input[name='password1']").value;
    var password2 = form.querySelector("input[name='password2']").value;

    if (password1 !== password2) {
        document.getElementById("error-message").textContent = "Passwords do not match";
    } else {
        // Passwords match, submit the form normally
        form.submit();
    }
});
 </script>

<script>
function togglePasswordVisibility() {
  var password1 = document.getElementById("password1");
  var password2 = document.getElementById("password2");

  if (password1.type === "password" && password2.type === "password") {
    password1.type = "text";
    password2.type = "text";
  } else {
    password1.type = "password";
    password2.type = "password";
  }
}
</script>

{% endblock %}

        <script src="https://unpkg.com/htmx.org@1.8.4" integrity="sha384-wg5Y/JwF7VxGk4zLsJEcAojRtlVp1FKKdGy1qN+OMtdq72WRvX/EdRdqg/LOhYeV" crossorigin="anonymous"></script>
