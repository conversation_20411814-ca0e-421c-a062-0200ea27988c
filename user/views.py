from django.contrib import messages, auth
from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login as dj_login, get_user_model
from django.http import HttpResponse

# Create your views here.
from blog.models import Ads, Blog, EventCount
from user.forms import UserRegisterForm



def check_username(request):
    username = request.POST.get('username')
    if get_user_model().objects.filter(username=username).exists():
        return HttpResponse('<div style="font-size: 100%; color: #ef6767;">This username already exists')
    else:
        return HttpResponse('<div style="font-size: 100%; color: #00ab05;">This username available')



def login(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(username=username, password=password)
        if user is not None:
            dj_login(request, user)
            messages.info(request, 'Login SuccessFull', extra_tags='login')
            return redirect('home')
        else:
            messages.error(request, 'Login Failed', extra_tags='login')
    return render(request, 'login.html')

def register(request):
    events_blog = EventCount.objects.order_by('order_no')[:6]
    ads_section = Ads.objects.all()
    if request.method == 'POST':
        userregsiterform = UserRegisterForm(request.POST)
        if userregsiterform.is_valid():
            password1 = request.POST.get('password1')
            password2 = request.POST.get('password2')
            if password1 != password2:
                messages.error(request, 'Password does not match', extra_tags='register')
                return redirect('register')
            else:
                userregsiterform.save()
                messages.info(request, 'Register Success', extra_tags='register')
                user = authenticate(username=userregsiterform.cleaned_data['username'], password=userregsiterform.cleaned_data['password1'],)
                dj_login(request, user)
                return redirect('home')
        else:
            messages.error(request, 'Register Failed', extra_tags='register')
            return redirect('register')
    else:
        userregsiterform = UserRegisterForm()

    context = {
        'userregsiterform':userregsiterform,
        'events_blog':events_blog,
        'ads_section':ads_section,
    }
    return render(request, 'register.html', context)


def logout(request):
    auth.logout(request)
    messages.info(request, 'Logout', extra_tags='logout')
    return redirect('login')