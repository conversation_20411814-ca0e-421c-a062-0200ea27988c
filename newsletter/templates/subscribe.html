


     <div class="widget widget-bg rss-widget"  id="newsletter" role="group">
                           <div class="heading">
                              <h2 class="main-heading" style="background-color: #ff5722; color: #fff;">Subscribe to our Newsletter</h2>
                           </div>
                             <p style="margin-top: -17px;">To get latest news of FMCG Industry, trends, and event updates etc.</p>
                           <div class="newsletter" id="post-form">
        <form class="form-inline">
    {% csrf_token %}
 <div class="form-group">
    <input type="email" name="email" class="form-control" placeholder="Enter Your Email">
 </div>
           <button hx-post="{% url 'subscribe' %}" hx-target="#newsletter" hx-swap="outerHTML" class="btn btn-default"><i class="ti-angle-right"></i></button>
</form>

{%for message in messages %}
     {%if "newsletter" in message.tags %}
          <p> {{message}} </p>   {# show login error message #}
     {%endif%}
{%endfor%}

                           </div>
     </div>




 <script src="https://unpkg.com/htmx.org@1.6.0"></script>
    <script>
        document.body.addEventListener('htmx:configRequest', (e) => {
          e.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
          })
      </script>



















