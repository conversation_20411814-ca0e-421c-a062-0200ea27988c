# Generated by Django 4.2.7 on 2023-11-21 05:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('blog', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Ads',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('position', models.CharField(choices=[('HEADER', 'HEADER'), ('GRID', 'GRID')], max_length=25)),
                ('image', models.ImageField(upload_to='Newsletter/Ads')),
                ('name', models.CharField(max_length=255)),
                ('link', models.URLField(blank=True, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
            ],
        ),
        migrations.CreateModel(
            name='Newsletter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('subscribe_status', models.CharField(choices=[('UN SUBSCRIBE', 'UN SUBSCRIBE'), ('SUBSCRIBE', 'SUBSCRIBE')], default='SUBSCRIBE', max_length=255)),
                ('message_status', models.CharField(choices=[('SENT', 'SENT'), ('PENDING', 'PENDING'), ('UN SUBSCRIBE', 'UN SUBSCRIBE')], default='PENDING', max_length=55)),
                ('created', models.DateField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='PromoCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=55)),
            ],
        ),
        migrations.CreateModel(
            name='AssignedBlogs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_no', models.IntegerField(blank=True, null=True)),
                ('blog', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='blog.blog')),
            ],
        ),
    ]
