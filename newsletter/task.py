import time
import base64
from celery import shared_task
from django.core.mail import EmailMultiAlternatives
from django.template.loader import get_template
from django_pandas.io import read_frame
from newsletter.models import Newsletter, AssignedBlogs, Ads
from newsletter.models import Newsletter
from django.urls import reverse

@shared_task(bind=True)
def newsletter_func(self):
    fmcg_insights = AssignedBlogs.objects.filter(blog__category__name__contains='FMCG INSIGHTS').order_by('order_no')[
                    :3]
    equipment_blog = AssignedBlogs.objects.filter(blog__category__name__contains='EQUIPMENT & SUPPLIES').order_by(
        'order_no')[:2]
    catering_blog = AssignedBlogs.objects.filter(blog__category__name__contains='CATERING').order_by('order_no')[:2]
    hotels_blog = AssignedBlogs.objects.filter(blog__category__name__contains='HOTELS').order_by('order_no')[:1]
    restaurant_blog = AssignedBlogs.objects.filter(blog__category__name__contains='RESTAURANTS & REVIEWS').order_by(
        'order_no')[:1]
    events_blog = AssignedBlogs.objects.filter(blog__category__name__contains='EVENTS').order_by('order_no')[:3]
    ads_section = Ads.objects.all()

    emails = Newsletter.objects.filter(subscribe_status='SUBSCRIBE').filter(message_status='PENDING')
    df = read_frame(emails, fieldnames=['email'])
    mail_list = df['email'].values.tolist()

    subject = '"Stay Up-to-Date on the FMCG & HORECA Industry with FMCG & HORECA Business Magazine"'
    message = ''
    html_template = get_template("mail.html")

    count = 0

    sent_emails = []
    for recipient in mail_list:
        if recipient in sent_emails:
            continue
        try:
            encrypted_email = base64.b64encode(recipient.encode()).decode()
            unsubscribe_url = reverse('unsubscribe', kwargs={'email': encrypted_email})
            html_content = html_template.render(
                {"unsubscribe_url": unsubscribe_url, 'fmcg_insights': fmcg_insights, 'equipment_blog': equipment_blog,
                 'catering_blog': catering_blog, 'hotels_blog': hotels_blog, 'restaurant_blog': restaurant_blog,
                 'events_blog': events_blog, 'ads_section': ads_section})
            msg = EmailMultiAlternatives(
                subject,
                message,
                '',
                [recipient]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()
            Newsletter.objects.filter(email=recipient).update(message_status='SENT')
            sent_emails.append(recipient)
            count += 1
            print(count)
            time.sleep(5)  # in seconds
        except:
            Newsletter.objects.filter(email=recipient).update(message_status='PENDING')
    return "Done"
