from ckeditor.fields import <PERSON><PERSON>ext<PERSON>ield
from colorfield.fields import <PERSON>Field
from django.db import models
from taggit.managers import TaggableManager
from django.db.models.signals import pre_delete
from django.dispatch import receiver
# Create your models here.
from django_ckeditor_5.fields import C<PERSON><PERSON>or<PERSON><PERSON>ield
from django.utils.text import slugify


class BlogCategory(models.Model):
    parent_category = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True)
    order_no = models.IntegerField(null=True, blank=True)
    name = models.CharField(max_length=1022)
    image = models.ImageField(upload_to='Blog Category', null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    color = ColorField(default='#FF0000')
    slug = models.SlugField(max_length=200, unique=True, blank=True)  # Allow blank for auto-generation
    seo_title = models.TextField(blank=True, null=True)
    seo_des = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            # Automatically generate the slug from the first 5 words of the title if it's a new blog post
            words = self.name.split()[:5]
            self.slug = slugify(" ".join(words))
        super().save(*args, **kwargs)

@receiver(pre_delete, sender=BlogCategory)
def blog_category_delete(sender, instance, **kwargs):
    # Pass false so FileField doesn't save the model.
    instance.image.delete(False)


class Blog(models.Model):
    CHOICES = [
        ('Latest Events', 'Latest Events'),
    ]
    ATTEND_CHOICES = [
        ('YES', 'YES'),
        ('NO', 'NO'),
    ]
    category = models.ForeignKey(BlogCategory, on_delete=models.SET_NULL, blank=True, null=True)
    name = models.CharField(max_length=1022)
    image = models.ImageField(upload_to='Blog')
    descriptions = RichTextField()
    blog_tags = TaggableManager(blank=True)
    extra_section = models.CharField(max_length=255, choices=CHOICES, blank=True, null=True)
    event_attend = models.CharField(max_length=255, choices=ATTEND_CHOICES, blank=True, null=True)
    created = models.DateField()
    updated = models.DateField(auto_now=True, blank=True, null=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True)  # Allow blank for auto-generation
    seo_title = models.TextField(blank=True, null=True)
    seo_des = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            words = self.name.split()[:5]
            base_slug = slugify(" ".join(words))
            suffix = 1

            while self.__class__.objects.filter(slug=self.generate_slug(base_slug, suffix)).exists():
                suffix += 1

            self.slug = self.generate_slug(base_slug, suffix)

        super().save(*args, **kwargs)

    def generate_slug(self, base_slug, suffix):
        return f"{base_slug}-{suffix}" if suffix > 1 else base_slug


class  EventCount(models.Model):
    event = models.ForeignKey(Blog, on_delete=models.CASCADE)
    order_no = models.IntegerField()


    def __str__(self):
        return self.event.name



@receiver(pre_delete, sender=Blog)
def blog_delete(sender, instance, **kwargs):
    # Pass false so FileField doesn't save the model.
    instance.image.delete(False)

class Comment(models.Model):
    blog = models.ForeignKey(Blog, on_delete=models.CASCADE)
    name = models.CharField(max_length=1022)
    email = models.CharField(max_length=255)
    website = models.CharField(max_length=255)
    comments = models.TextField()
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name




class Ads(models.Model):
    CHOICES = [
        ('Right Side Ads', 'Right Side Ads'),
        ('Grid Ads', 'Grid Ads'),
        ('Width Ads 1', 'Width Ads 1'),
        ('Width Ads 2', 'Width Ads 2'),
        ('Top Ads', 'Top Ads'),
        ('MAGAZINE', 'MAGAZINE')
    ]
    area = models.CharField(max_length=255, choices=CHOICES, null=True, blank=True)
    image = models.FileField(upload_to='Ads')
    name = models.CharField(max_length=255)
    link = models.URLField(blank=True, null=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

@receiver(pre_delete, sender=Ads)
def ads_delete(sender, instance, **kwargs):
    # Pass false so FileField doesn't save the model.
    instance.image.delete(False)


class AdvertiseWithUs(models.Model):
    company_name = models.CharField(max_length=1022)
    contact_number = models.CharField(max_length=1022)
    contact_person = models.CharField(max_length=1022, blank=True, null=True)
    email = models.CharField(max_length=255)
    website = models.CharField(max_length=1022)
    message = models.TextField(blank=True, null=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.company_name

class SubscribeNewsletter(models.Model):
    email = models.EmailField()
    created = models.DateField(auto_now=True)

    def __str__(self):
        return self.email




