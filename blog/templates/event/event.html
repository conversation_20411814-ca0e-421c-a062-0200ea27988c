{% extends 'include/base.html' %}
{% load static %}

{% block head %}

<title>Top Business Events in Dubai, UAE | FMCG & HORECA</title>
<meta name="description" content="Explore the top business events happening in Dubai, UAE related to hotels, restaurants & catering. Get insights & network professionals in the industry.">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="Top Business Events in Dubai, UAE | FMCG & HORECA">
<meta property="og:description" content="Explore the top business events happening in Dubai, UAE related to hotels, restaurants & catering. Get insights & network professionals in the industry.">
<meta property="og:image" content="https://fmcghorecabusiness.com/static/assets/images/favicon.png">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="Top Business Events in Dubai, UAE | FMCG & HORECA">
<meta name="twitter:description" content="Explore the top business events happening in Dubai, UAE related to hotels, restaurants & catering. Get insights & network professionals in the industry.">
<meta property="og:image" content="https://fmcghorecabusiness.com/static/assets/images/favicon.png">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}

{% block content %}

<section class="my-breadcrumb" style="background-image: url(https://fmcghorecabusiness.com/media/Blog%20Category/events.JPG);">

         <div class="container page-banner">
            <div class="row">
               <div class="col-sm-12 col-md-12 col-xs-12">
                 <h1>Events</h1>
               </div>
            </div>
         </div>
      </section>

 <section class="main-content"  style="margin-bottom: -23px;">
         <div class="container">
         <div class="row">
            <div class="col-md-12 col-sm-12 col-xs-12">
               <div class="section">
                  <div class="row">
                     <div class="posts-masonry">
                         {% for blog in blog_paginator %}
                        <article class="col-md-3 col-sm-4 col-xs-12">
                           <div class="grid-1 gride-style-2">
                              <div class="picture">
                                 <div class="category-image">
                                    <a>
                                    <img alt="{{ blog.event.name }}" class="img-responsive" title="{{ blog.event.name }}" src="{{ blog.event.image.url }}">
                                    </a>
                                 </div>
                              </div>
                              <div class="detail" style="border-bottom: 2px solid #9C27B0; height:68px;">
                                 <div class="caption">
                                    <h5>
                                       <a href="{% url 'blog_details' blog.event.slug %}">{{ blog.event.name|truncatechars:55 }}</a>
                                    </h5>
                                 </div>
                              </div>
                           </div>
                        </article>
                         {% endfor %}
                     </div>
                  </div>


                   <div class="pagination-holder">
                     <nav>
                        <ul class="pagination">


                            {% if blog_paginator.has_previous %}
                           <li>
                              <a href="?page=1"><span aria-hidden="true" style="font-size: 13px;">FIRST</span></a>
                           </li>
                            <li>
                              <a href="?page={{ blog_paginator.previous_page_number }}"><span aria-hidden="true" style="font-size: 13px;">PREVIOUS</span></a>
                           </li>
                           {% endif %}

      {% if blog_paginator.has_next %}
                           <li>
                              <a href="?page={{ blog_paginator.next_page_number }}" style="font-size: 13px;">NEXT</a>
                           </li>
                           <li>
                              <a href="?page={{ blog_paginator.paginator.num_pages }}" style="font-size: 13px;">LAST</a>
                           </li>
      {% endif %}
                        </ul>
                     </nav>
                  </div>
               </div>
            </div>
         </div>
         </div>
      </section>

{% endblock %}