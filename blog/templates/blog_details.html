{% extends 'include/base.html' %}
{% load static %}
{% load social_share %}

{% block head %}

<title>{% if blog.seo_title %}{{ blog.seo_title }}{% else %}{{ blog.name }}{% endif %}</title>
<meta name="description" content="{{ blog.seo_des }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{% if blog.seo_title %}{{ blog.seo_title }}{% else %}{{ blog.name }}{% endif %}">
<meta property="og:description" content="{{ blog.seo_des }}">
<meta property="og:image" content="{{ blog.image.url }}">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{% if blog.seo_title %}{{ blog.seo_title }}{% else %}{{ blog.name }}{% endif %}">
<meta name="twitter:description" content="{{ blog.seo_des }}">
<meta property="og:image" content="{{ blog.image.url }}">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}

{% block content %}

<section class="my-breadcrumb" style="background-image: url('/static/assets/images/banner/blog-details.jpg');">
         <div class="container page-banner">
            <div class="row">
               <div class="col-sm-12 col-md-12 col-xs-12">
                  <h1 style="font-size: 24px;">{{ blog.name }}</h1>
               </div>
            </div>
         </div>
      </section>

<br>

<section class="main-content">
         <div class="container">
            <div class="row">
               <div class="col-md-8 col-sm-8 col-xs-12">
                    <div class="ad-div text-center">
                        {% for ads_section in ads_section %}
                           {% if ads_section.area == 'Width Ads 2' %}
                     <a href="{{ ads_section.link }}" target="_blank">
                     <img src="{{ ads_section.image.url }}" class="img-responsive">
                     </a>
                        {% endif %}
                        {% endfor %}
                  </div>
                  <div class="post-entry">
                        <div class="catname">
                        <a class="btn" href="{% url 'category' blog.category.slug %}" style="background-color:{{ blog.category.color }}">
                           <div>{{ blog.category }}</div>
                        </a>
                     </div>
                     <h2 style="margin-bottom: -5px; font-size: 24px;">{{ blog.name }} </h2>
                     <ul class="post-tools">
                        <li>{{ blog.created }}</li>
                        <li> <i class="ti-user"></i> FMCG HORECA BUSINESS</li>
                     </ul>
                      <ul class="social-share">
                          {% with base_url="https://fmcgandhorecabusiness.com/" %}
                         <li style="background-color:#25D366;"> <a href="whatsapp://send?text={{ base_url|urlencode }}{{ request.get_full_path|urlencode }}&t={{ object.title }}"> <i class="fa fa-whatsapp"></i> WhatsApp</a></li>
                          {% endwith %}
                           {% with base_url="https://fmcgandhorecabusiness.com/" %}
                         <li style="background-color:#3b5998;"> <a href="https://www.facebook.com/sharer/sharer.php?u={{ base_url|urlencode }}{{ request.get_full_path|urlencode }}&t={{ object.title }}" target="_blank"> <i class="fa fa-facebook"></i> Facebook</a></li>
                          {% endwith %}
<!--                          {% with base_url="https://fmcgandhorecabusiness.com/" %}-->
<!--                          <li style="background-color:#cd486b;"> <a href="https://www.instagram.com/?url={{ base_url|urlencode }}{{ request.get_full_path|urlencode }}&t={{ object.title }}" target="_blank"> <i class="fa fa-instagram"></i> Instagram</a></li>-->
<!--                          {% endwith %}-->
                          {% with base_url="https://fmcgandhorecabusiness.com/" %}
                          <li class="twitter"> <a href="https://twitter.com/home?status={{ base_url|urlencode }}{{ request.get_full_path|urlencode }}&t={{ object.title }}" target="_blank"> <i class="fa fa-twitter"></i> Twitter</a></li>
                          {% endwith %}
                          {% with base_url="https://fmcgandhorecabusiness.com/" %}
                        <li class="linkedin"> <a href="https://www.linkedin.com/shareArticle?mini=true&url={{ base_url|urlencode }}{{ request.get_full_path|urlencode }}&t={{ object.title }}" target="_blank"> <i class="fa fa-linkedin"></i> LinkedIn</a></li>
                          {% endwith %}


                     </ul>
                     <div class="picture">
                         <img alt="{{ blog.name }}" class="img-responsive" src="{{ blog.image.url }}" style="width: 615px;">
                     </div>
                     <div>
                      {{ blog.descriptions|safe }}
                     </div>


<!--                      <div class="tag-list">-->
<!--                       {% for blog in blog.blog_tags.all %} <a href="#">{{ blog }}</a> {% endfor %}-->
<!--                     </div>-->

                  </div>
               </div>



               <div class="col-md-4 col-sm-12 col-xs-12" id="side-bar-right">
                  <div class="theiaStickySidebar">

                        {% for ads_section in ads_section %}
                            {% if ads_section.area == 'MAGAZINE' %}
                    <div class="widget widget-bg">
                           <div class="ad-div style-box">
                             {% if ads_section.link %}
                              <a href="{{ ads_section.link }}" target="_blank">
                                 {% endif %}
                              <img src="{{ ads_section.image.url }}" title="{{ ads_section.name }}"  class="img-responsive">
                              </a>
                           </div>
                        </div>
                     {% endif %}
                              {% endfor %}

                       <aside class="sidebar-color ">
                        <div class="widget widget-bg">
                           <div class="heading">
                              <h2 class="main-heading">EVENTS</h2>
                           </div>
                           <ul class="tabs-posts">
                              {% for events_blog in events_blog %}
                              <li>
                                 <div class="pic">
                                    <img alt="{{ events_blog.event.name }}" title="{{ events_blog.event.name }}" class="img-responsive" src="{{ events_blog.event.image.url }}" style="width:75px; height:75px; object-fit:cover;"> </div>
                                 <div class="caption">
                                    <a href="{% url 'blog_details' events_blog.event.slug %}">{{ events_blog.event.name|truncatechars:50 }}
                                    </a>
                                 </div>
<!--                                 <ul class="post-tools">-->
<!--                                    <li>{{ events_blog.created }} </li>-->
<!--                                 <li><i class="ti-user"></i>FMCG Horeca Business</li>-->
<!--                                   </ul>-->
                              </li>
                              {% endfor %}
                           </ul>
                        </div>
                     </aside>
                      <br>

                      

                       {% include "subscribe.html" %}
<!--                     <aside>-->

<!--                          {% for ads_section in ads_section %}-->
<!--                           {% if ads_section.area == 'Right Side Ads' %}-->
<!--                                   {% if forloop.counter < 2 %}-->

<!--   <div class="widget widget-bg">-->
<!--                           <div class="ad-div style-box">-->
<!--                               {% if ads_section.link %}-->
<!--                              <a href="{{ ads_section.link }}" target="_blank">-->
<!--                                  {% endif %}-->

<!--                              <img src="{{ ads_section.image.url }}" class="img-responsive">-->
<!--                              </a>-->
<!--                           </div>-->
<!--                        </div>-->
<!--                         {% else %}-->
<!--                            <div class="widget widget-bg">-->
<!--                           <div class="ad-div style-box">-->
<!--                                {% if ads_section.link %}-->
<!--                              <a href="{{ ads_section.link }}" target="_blank">-->
<!--                                  {% endif %}-->
<!--                              <img src="{{ ads_section.image.url }}" class="img-responsive">-->
<!--                              </a>-->
<!--                           </div>-->
<!--                        </div>-->

<!--                          {% endif %}-->
<!--                              {% endif %}-->
<!--                              {% endfor %}-->

                             <div class="widget widget-bg">
                           <div class="heading">
                              <h2 class="main-heading">RELATED POSTS </h2>
                           </div>
                           <ul class="tabs-posts">

                           {% for related_blog in related_blog %}

                              <li>
                                 <div class="pic">
                                 <img alt="{{ related_blog.name }}" class="img-responsive" src="{{ related_blog.image.url }}" style="width: 75px; height: 75px; object-fit: cover;"></div>
                                 <div class="caption">
                                    <a href="{% url 'blog_details' related_blog.slug %}">{{ related_blog.name|slice:50 }}</a>
                                 </div>
                                 <ul class="post-tools">
                                    <li>{{ related_blog.created }}</li>
                                    <li style="font-size: 11px;"> <i class="ti-user"></i> FMCG HORECA BUISINESS </li>
                                 </ul>
                              </li>
                              {% endfor %}
                           </ul>
                        </div>
                     </aside>
                  </div>
               </div>
            </div>
         </div>
      </section>



{% endblock %}