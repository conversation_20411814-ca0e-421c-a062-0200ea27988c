{% extends 'include/base.html' %}
{% load static %}

{% block head %}

<title>{% if category.seo_title %}{{ category.seo_title }}{% else %}{{ category.name }}{% endif %}</title>
<meta name="description" content="{{ category.seo_des }}">
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<meta property="og:title" content="{% if category.seo_title %}{{ category.seo_title }}{% else %}{{ category.name }}{% endif %}">
<meta property="og:description" content="{{ category.seo_des }}">
<meta property="og:image" content="{{ category.image.url }}">
<meta property="og:url" content="{{ request.build_absolute_uri }}">

<meta name="twitter:title" content="{% if category.seo_title %}{{ category.seo_title }}{% else %}{{ category.name }}{% endif %}">
<meta name="twitter:description" content="{{ category.seo_des }}">
<meta property="og:image" content="{{ category.image.url }}">
<meta name="twitter:card" content="summary_large_image">

{% endblock %}


{% block content %}

<section class="my-breadcrumb" style="background-image: url( {{category.image.url}} );">

         <div class="container page-banner">
            <div class="row">
               <div class="col-sm-12 col-md-12 col-xs-12">
                 <h1>{{ category.name }}</h1>
               </div>
            </div>
         </div>
      </section>

 <section class="main-content"  style="margin-bottom: -23px;">
         <div class="container">
         <div class="row">
            <div class="col-md-12 col-sm-12 col-xs-12">
               <div class="section">
                  <div class="row">
                     <div class="posts-masonry">
                         {% for blog in blog_paginator %}
                        <article class="col-md-3 col-sm-4 col-xs-12">
                           <div class="grid-1 gride-style-2">
                              <div class="picture">
                                 <div class="category-image">
                                    <a>
                                    <img alt="{{ blog.name }}" class="img-responsive" title="{{ blog.name }}" src="{{ blog.image.url }}" style="height:200px;">
                                    </a>
                                   <div class="catname">
                                       <a  href="{% url 'category' blog.category.slug %}">
                                          <div class="btn" style="background-color:{{ blog.category.color }};">{{ blog.category }}</div>
                                       </a>
                                    </div>
                                 </div>
                              </div>
                              <div class="detail" style="border-bottom: 2px solid {{ blog.category.color }}">
                                 <div class="caption">
                                    <h5>
                                       <a href="{% url 'blog_details' blog.slug %}">{{ blog.name|truncatechars:55 }}</a>
                                    </h5>
                                 </div>
                                 <ul class="post-tools">
                                    <li>{{ blog.created }} </li>
                                   </ul>
                              </div>
                           </div>
                        </article>
                         {% endfor %}
                     </div>
                  </div>


                   <div class="pagination-holder">
                     <nav>
                        <ul class="pagination">


                            {% if blog_paginator.has_previous %}
                           <li>
                              <a href="?page=1"><span aria-hidden="true" style="font-size: 13px;">FIRST</span></a>
                           </li>
                            <li>
                              <a href="?page={{ blog_paginator.previous_page_number }}"><span aria-hidden="true" style="font-size: 13px;">PREVIOUS</span></a>
                           </li>
                           {% endif %}

      {% if blog_paginator.has_next %}
                           <li>
                              <a href="?page={{ blog_paginator.next_page_number }}" style="font-size: 13px;">NEXT</a>
                           </li>
                           <li>
                              <a href="?page={{ blog_paginator.paginator.num_pages }}" style="font-size: 13px;">LAST</a>
                           </li>
      {% endif %}
                        </ul>
                     </nav>
                  </div>
               </div>
            </div>
         </div>
         </div>
      </section>

{% endblock %}