# Generated by Django 4.2.7 on 2023-11-21 05:10

import ckeditor.fields
import colorfield.fields
from django.db import migrations, models
import django.db.models.deletion
import taggit_selectize.managers


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('taggit', '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx'),
    ]

    operations = [
        migrations.CreateModel(
            name='Ads',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('area', models.CharField(blank=True, choices=[('Right Side Ads', 'Right Side Ads'), ('Grid Ads', 'Grid Ads'), ('Width Ads 1', 'Width Ads 1'), ('Width Ads 2', 'Width Ads 2'), ('Top Ads', 'Top Ads'), ('MAGAZINE', 'MAGAZINE')], max_length=255, null=True)),
                ('image', models.FileField(upload_to='Ads')),
                ('name', models.CharField(max_length=255)),
                ('link', models.URLField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='AdvertiseWithUs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(max_length=1022)),
                ('contact_number', models.CharField(max_length=1022)),
                ('contact_person', models.CharField(blank=True, max_length=1022, null=True)),
                ('email', models.CharField(max_length=255)),
                ('website', models.CharField(max_length=1022)),
                ('message', models.TextField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Blog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=1022)),
                ('image', models.ImageField(upload_to='Blog')),
                ('descriptions', ckeditor.fields.RichTextField()),
                ('extra_section', models.CharField(blank=True, choices=[('Latest Events', 'Latest Events')], max_length=255, null=True)),
                ('event_attend', models.CharField(blank=True, choices=[('YES', 'YES'), ('NO', 'NO')], max_length=255, null=True)),
                ('created', models.DateField()),
                ('updated', models.DateField(auto_now=True, null=True)),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True)),
                ('seo_title', models.TextField(blank=True, null=True)),
                ('seo_des', models.TextField(blank=True, null=True)),
                ('blog_tags', taggit_selectize.managers.TaggableManager(blank=True, help_text='A comma-separated list of tags.', through='taggit.TaggedItem', to='taggit.Tag', verbose_name='Tags')),
            ],
        ),
        migrations.CreateModel(
            name='SubscribeNewsletter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('created', models.DateField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='EventCount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_no', models.IntegerField()),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blog.blog')),
            ],
        ),
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=1022)),
                ('email', models.CharField(max_length=255)),
                ('website', models.CharField(max_length=255)),
                ('comments', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('blog', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blog.blog')),
            ],
        ),
        migrations.CreateModel(
            name='BlogCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_no', models.IntegerField(blank=True, null=True)),
                ('name', models.CharField(max_length=1022)),
                ('image', models.ImageField(blank=True, null=True, upload_to='Blog Category')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('color', colorfield.fields.ColorField(default='#FF0000', image_field=None, max_length=25, samples=None)),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True)),
                ('seo_title', models.TextField(blank=True, null=True)),
                ('seo_des', models.TextField(blank=True, null=True)),
                ('parent_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='blog.blogcategory')),
            ],
        ),
        migrations.AddField(
            model_name='blog',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='blog.blogcategory'),
        ),
    ]
