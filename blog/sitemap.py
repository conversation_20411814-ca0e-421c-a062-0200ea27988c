from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from .models import Blog, BlogCategory
from magazine.models import Magazine

class OtherSitemap(Sitemap):
    priority = 1.0

    def items(self):
        return ['home', 'about_us', 'events', 'magazine', 'magazine_plans', 'login', 'register']

    def location(self, item):
        return reverse(item)


class BlogSitemap(Sitemap):
    priority = 1.0

    def items(self):
        return Blog.objects.all()

    def lastmod(self, obj):
        return obj.updated

    def location(self, obj):
        return reverse('blog_details', args=[obj.slug])

class BlogCategorySitemap(Sitemap):
    priority = 1.0

    def items(self):
        return BlogCategory.objects.all()

    def lastmod(self, obj):
        return obj.updated

    def location(self, obj):
        return reverse('category', args=[obj.slug])

class MagazineSitemap(Sitemap):
    priority = 1.0

    def items(self):
        return Magazine.objects.all()

    def lastmod(self, obj):
        return obj.updated

    def location(self, obj):
        return reverse('view_magazine', args=[obj.slug])

sitemaps = {
    'blog_sitemap': BlogSitemap,
    'other_sitemap': OtherSitemap,
    'category_sitemap': BlogCategorySitemap,
    'magazine_sitemap' : MagazineSitemap,
}