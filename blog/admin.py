from django.contrib import admin
from import_export.admin import ImportExportMixin

# Register your models here.
from blog.models import BlogCategory, Blog, Comment, Ads, AdvertiseWithUs, EventCount


class BlogCategoryAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'parent_category', 'order_no', 'image', 'color', 'created', 'updated')
    search_fields = ('name',)

class BlogAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('category', 'name', 'image', 'blog_tags',  'created', 'updated')
    search_fields = ('name', 'slug')
    list_filter = ('category',)



class CommentAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('blog', 'name', 'email', 'website', 'comments', 'created', 'updated')
    search_fields = ('name',)


class AdsAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('area', 'name', 'link', 'image', 'created', 'updated')
    search_fields = ('name',)


class AdvertiseWithUsAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('company_name', 'contact_number', 'contact_person', 'email',  'website', 'created', 'updated')
    search_fields = ('company_name',)

class EventCountAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('event', 'order_no',)
    autocomplete_fields = ['event']

admin.site.register(BlogCategory, BlogCategoryAdmin)
admin.site.register(Blog, BlogAdmin)
admin.site.register(Comment, CommentAdmin)
admin.site.register(Ads, AdsAdmin)
admin.site.register(AdvertiseWithUs, AdvertiseWithUsAdmin)
admin.site.register(EventCount, EventCountAdmin)

