from django import forms
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.paginator import Paginator
from django.core.validators import validate_email
from django.db.models import Count
from django.http import HttpResponse
from django.shortcuts import render, get_object_or_404, get_list_or_404, redirect
from django.db.models import Count

# import requests
# from rest_framework import generics
# from blog.serializers import BlogSerializer

# Create your views here.
from odf import form
from blog.models import Blog, BlogCategory, Ads, EventCount


# class BlogListView(generics.ListAPIView):
#     queryset = Blog.objects.all()
#     serializer_class = BlogSerializer


def blog_details(request, slug):
    blog = Blog.objects.get(slug=slug)
    blogs = Blog.objects.all().order_by('-created')[:6]
    ads_section = Ads.objects.all()
    blog_tags = blog.blog_tags.all()
    related_blog = Blog.objects.filter(blog_tags__in=blog_tags).exclude(slug=slug)
    related_blog = related_blog.annotate(tag_count=Count('blog_tags')).order_by('-tag_count', '-created')[:6]
    events_blog = EventCount.objects.order_by('order_no')[:6]

    context = {
        'blog':blog,
        'blogs':blogs,
        'ads_section':ads_section,
        'related_blog':related_blog,
        'events_blog':events_blog,
    }
    return render(request, 'blog_details.html', context)


def category(request, slug):
    category = get_object_or_404(BlogCategory, slug=slug)

    blog_paginator = Paginator(Blog.objects.filter(category=category).order_by('-created'), 12)
    page = request.GET.get('page')
    blog_paginator = blog_paginator.get_page(page)

    blog = Blog.objects.filter(category=category).order_by('-created')
    context = {
        'category': category,
        'blog': blog,
        'blog_paginator': blog_paginator,
    }
    return render(request, 'category/category.html', context)

def events(request):
    blog_paginator = Paginator(EventCount.objects.order_by('order_no'), 12)
    page = request.GET.get('page')
    blog_paginator = blog_paginator.get_page(page)

    blog = EventCount.objects.order_by('order_no')
    context = {
        'blog': blog,
        'blog_paginator': blog_paginator,
    }
    return render(request, 'event/event.html', context)