"""fmcgbusiness URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf.urls import handler404, handler500
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include
from fmcgbusiness import settings
from blog.sitemap import BlogSitemap, BlogCategorySitemap, OtherSitemap, MagazineSitemap
from django.contrib.sitemaps.views import sitemap
from django.views.generic import TemplateView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('core.urls')),
    path('', include('blog.urls')),
    path('myadmin/', include('adminpanel.urls')),
    path('', include('magazine.urls')),
    path('', include('user.urls')),
    path('taggit/', include('taggit_selectize.urls')),
    path('newsletter/', include('newsletter.urls')),
    path("ckeditor5/", include('django_ckeditor_5.urls')),
    path('sitemap-blog.xml', sitemap, {'sitemaps': {'blog_sitemap': BlogSitemap}}, name='blog_sitemap'),
    path('sitemap-other.xml', sitemap, {'sitemaps': {'other_sitemap': OtherSitemap}}, name='other_sitemap'),
    path('sitemap-category.xml', sitemap, {'sitemaps': {'category_sitemap': BlogCategorySitemap}}, name='category_sitemap'),
    path('sitemap-magazine.xml', sitemap, {'sitemaps': {'magazine_sitemap': MagazineSitemap}},name='magazine_sitemap'),
    path('robots.txt', TemplateView.as_view(template_name="robots.txt", content_type="text/plain")),

    ] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


handler404 = 'core.views.error_404'


if settings.DEBUG:
   urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
