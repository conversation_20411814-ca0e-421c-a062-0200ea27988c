from django.contrib import messages
from django.contrib.auth import authenticate
from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login as dj_login

# Create your views here.
from django.urls import reverse

from magazine.forms import SubscribeForm
from magazine.models import Magazine, MagazinePlans, SubscribeCheckout


def magazine(request):
    magazine = Magazine.objects.order_by('-order_no')
    context = {
        'magazine':magazine,
    }
    return render(request, 'magazine.html', context)

def view_magazine(request, slug):
    magazine = Magazine.objects.get(slug=slug)
    subscribe = SubscribeCheckout.objects.filter(user=request.user.id, order_status='Conformed')

    context = {
        'magazine':magazine,
        'subscribe':subscribe,
    }
    return render(request, 'view_magazine.html', context)

def magazine_plans(request):
    magazine_plans = MagazinePlans.objects.all()
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(username=username, password=password)
        if user is not None:
            dj_login(request, user)
            messages.info(request, 'Login SuccessFull')
            return redirect('magazine_plans')
        else:
            messages.error(request, 'Login Failed')
    context = {
        'magazine_plans':magazine_plans,
    }
    return render(request, 'magazine_plans.html', context)


def magazine_subscribe(request, slug):
    magazineplans = MagazinePlans.objects.get(slug=slug)
    if request.method == 'POST':
        subscribeform = SubscribeForm(request.POST)
        if subscribeform.is_valid():
            subscribe = subscribeform.save(commit=False)
            subscribe.plans = magazineplans
            subscribe.user = request.user
            subscribe.save()
            messages.info(request, 'Thanks for submit', extra_tags='magazine_subscribe')
            return redirect('magazine_plans')
        else:
            messages.error(request, 'Error Submit', extra_tags='magazine_subscribe')
            return redirect(reverse('magazine_subscribe', kwargs={'slug':slug}))
    else:
        subscribeform = SubscribeForm()
    context = {
        'subscribeform':subscribeform,
        'magazine_plans': magazine_plans,
        'title': f"{magazineplans.seo_title}",
        'des': f"{magazineplans.seo_des} ",

    }
    return render(request, 'magazine_subscribe.html', context)

