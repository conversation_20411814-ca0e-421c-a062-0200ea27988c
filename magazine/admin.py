from django.contrib import admin
from import_export.admin import ImportExportMixin

# Register your models here.
from magazine.models import Magazine, MagazinePlans, SubscribeCheckout

class MagazineAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('order_no', 'image', 'name', 'pdf', 'created', 'updated')
    search_fields = ('name',)

class MagazinePlansAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('title', 'subtitle', 'price', 'created', 'updated')
    search_fields = ('title',)

class SubscribeCheckoutAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('plans', 'address', 'emirate', 'state', 'city', 'zipcode', 'order_status', 'payment_status', 'created', 'updated')
    search_fields = ('title',)

class SubscribeCheckoutAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('user', )

admin.site.register(Magazine, MagazineAdmin)
admin.site.register(MagazinePlans, MagazinePlansAdmin)
admin.site.register(SubscribeCheckout, SubscribeCheckoutAdmin)
