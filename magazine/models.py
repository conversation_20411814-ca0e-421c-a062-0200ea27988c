from django.db import models
from django.db.models.signals import pre_delete
from django.dispatch import receiver
from django.utils.text import slugify

# Create your models here.
from user.models import FmcgUser


class Magazine(models.Model):
    order_no = models.IntegerField()
    image = models.ImageField(upload_to='Magazine')
    name = models.CharField(max_length=1022)
    pdf = models.FileField(upload_to='Magazines Pdf', null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True)  # Allow blank for auto-generation
    seo_title = models.TextField(blank=True, null=True)
    seo_des = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            # Automatically generate the slug from the first 5 words of the title if it's a new blog post
            words = self.name.split()[:5]
            self.slug = slugify(" ".join(words))
        super().save(*args, **kwargs)


@receiver(pre_delete, sender=Magazine)
def magazine_delete(sender, instance, **kwargs):
    # Pass false so FileField doesn't save the model.
    instance.image.delete(False)
    instance.pdf.delete(False)

class MagazinePlans(models.Model):
    title = models.CharField(max_length=1022)
    subtitle = models.CharField(max_length=1022)
    price = models.CharField(max_length=1022)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True)  # Allow blank for auto-generation
    seo_title = models.TextField(blank=True, null=True)
    seo_des = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            # Automatically generate the slug from the first 5 words of the title if it's a new blog post
            words = self.subtitle.split()[:5]
            self.slug = slugify(" ".join(words))
        super().save(*args, **kwargs)


class SubscribeCheckout(models.Model):
    ORDER_CHOICES = [
        ('Pending', 'Pending'),
        ('Conformed', 'Conformed')
    ]
    PAYMENT_CHOICES = [
        ('Pending', 'Pending'),
        ('Conformed', 'Conformed')
    ]
    user = models.ForeignKey(FmcgUser, on_delete=models.CASCADE, null=True)
    plans = models.ForeignKey(MagazinePlans, on_delete=models.SET_NULL, null=True, blank=True)
    address = models.CharField(max_length=1022, null=True, blank=True)
    emirate = models.CharField(max_length=1022, null=True, blank=True)
    city = models.CharField(max_length=1022, null=True, blank=True)
    zipcode = models.CharField(max_length=1022, null=True, blank=True)
    order_status = models.CharField(max_length=1022, choices=ORDER_CHOICES, default='Conformed',null=True, blank=True)
    payment_status = models.CharField(max_length=1022, choices=PAYMENT_CHOICES, default='Conformed', null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.address

class Flipbook(models.Model):
    title = models.CharField(max_length=1022)
    pdf = models.URLField()
    slug = models.SlugField(max_length=200, unique=True, blank=True)  # Allow blank for auto-generation

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            # Automatically generate the slug from the first 5 words of the title if it's a new blog post
            words = self.title.split()[:10]
            self.slug = slugify(" ".join(words))
        super().save(*args, **kwargs)
