# Generated by Django 4.2.7 on 2023-11-24 14:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('magazine', '0002_alter_subscribecheckout_order_status_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Flipbook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=1022)),
                ('pdf', models.URLField()),
            ],
        ),
    ]
