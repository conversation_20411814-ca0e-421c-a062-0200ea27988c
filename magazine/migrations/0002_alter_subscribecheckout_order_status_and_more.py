# Generated by Django 4.2.7 on 2023-11-24 14:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('magazine', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='subscribecheckout',
            name='order_status',
            field=models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Conformed', 'Conformed')], default='Conformed', max_length=1022, null=True),
        ),
        migrations.AlterField(
            model_name='subscribecheckout',
            name='payment_status',
            field=models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Conformed', 'Conformed')], default='Conformed', max_length=1022, null=True),
        ),
    ]
