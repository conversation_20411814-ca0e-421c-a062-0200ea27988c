# Generated by Django 4.2.7 on 2023-11-21 05:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Magazine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_no', models.IntegerField()),
                ('image', models.ImageField(upload_to='Magazine')),
                ('name', models.CharField(max_length=1022)),
                ('pdf', models.FileField(blank=True, null=True, upload_to='Magazines Pdf')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True)),
                ('seo_title', models.TextField(blank=True, null=True)),
                ('seo_des', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='MagazinePlans',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=1022)),
                ('subtitle', models.CharField(max_length=1022)),
                ('price', models.CharField(max_length=1022)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True)),
                ('seo_title', models.TextField(blank=True, null=True)),
                ('seo_des', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='SubscribeCheckout',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(blank=True, max_length=1022, null=True)),
                ('emirate', models.CharField(blank=True, max_length=1022, null=True)),
                ('city', models.CharField(blank=True, max_length=1022, null=True)),
                ('zipcode', models.CharField(blank=True, max_length=1022, null=True)),
                ('order_status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Conformed', 'Conformed')], default='Pending', max_length=1022, null=True)),
                ('payment_status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Conformed', 'Conformed')], default='Pending', max_length=1022, null=True)),
                ('created', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated', models.DateTimeField(auto_now=True, null=True)),
                ('plans', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='magazine.magazineplans')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
